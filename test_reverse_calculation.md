# Reverse Depth Calculation Implementation Test

## Overview
This document outlines how to test the reverse depth calculation functionality that was implemented in the DataEntryService.

## Key Improvements Made

### 1. Segment-Based ImageCropId Determination
- **Before**: Used `beforeBlock.ImageCropId` directly from OCR blocks
- **After**: Implemented `DetermineImageCropIdFromSegments()` method that:
  - Analyzes all OCR markers across image segments
  - Finds OCR markers that bracket the target depth
  - Determines which segment the target depth belongs to based on proximity
  - Handles gaps between segments by assigning to the nearest segment

### 2. Gap Handling Logic
- If target depth falls between segments (in a gap), it's assigned to the nearest segment
- Uses a 50% threshold: if target is in first half of gap, assign to previous segment; if in second half, assign to next segment
- This ensures consistent behavior when dealing with discontinuous segments

### 3. Fallback Mechanisms
- Primary: Use bracketing OCR markers to determine segment
- Secondary: Find closest OCR marker by depth value
- Tertiary: Fall back to original beforeBlock logic

## Testing Scenarios

### Test Case 1: Target Depth Within Segment
```
Segment 1: Depth 10.0 - 15.0 (ImageCropId: 100)
Segment 2: Depth 15.0 - 20.0 (ImageCropId: 200)
Target Depth: 12.5
Expected: ImageCropId = 100
```

### Test Case 2: Target Depth in Gap Between Segments
```
Segment 1: Depth 10.0 - 15.0 (ImageCropId: 100)
Gap: 15.0 - 16.0
Segment 2: Depth 16.0 - 20.0 (ImageCropId: 200)
Target Depth: 15.3 (closer to Segment 1)
Expected: ImageCropId = 100
Target Depth: 15.7 (closer to Segment 2)
Expected: ImageCropId = 200
```

### Test Case 3: Backward Compatibility
- Existing DataEntry records with null ImageCropId/X should be automatically populated
- GetAllDataEntryAsync should apply reverse calculation for missing values
- Create/Update operations should calculate missing values automatically

## API Usage

### Direct Method Call
```csharp
var input = new GetPositionByDepthDto
{
    DrillHoleId = 123,
    Depth = 15.5
};
var result = await dataEntryService.GetPositionDataEntryByDepth(input);
// result.ImageCropId and result.X will contain calculated values
```

### Automatic Integration
- **Create**: When creating DataEntry with null ImageCropId/X, values are auto-calculated
- **Update**: When updating DataEntry with null ImageCropId/X, values are auto-calculated  
- **Retrieve**: When getting DataEntry list, missing values are auto-populated

## Error Handling
- If no images found for drill hole: throws UserFriendlyException
- If no image contains target depth: throws UserFriendlyException
- If reverse calculation fails during CRUD operations: continues without error (backward compatibility)
