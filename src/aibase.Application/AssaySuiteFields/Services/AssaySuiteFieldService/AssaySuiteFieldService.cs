using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.UI;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using aibase.AssaySuiteFields.Dto;

namespace aibase.AssaySuiteFields.Services.AssaySuiteFieldService;

/// <inheritdoc />
public class AssaySuiteFieldService : IAssaySuiteFieldService
{
    private readonly IRepository<AssaySuiteField, int> _repository;
    private readonly IMapper _mapper;
    private readonly IUnitOfWorkManager _unitOfWorkManager;

    /// <summary>
    /// 
    /// </summary>
    public AssaySuiteFieldService(
        IRepository<AssaySuiteField, int> repository,
        IMapper mapper,
        IUnitOfWorkManager unitOfWorkManager)
    {
        _repository = repository;
        _mapper = mapper;
        _unitOfWorkManager = unitOfWorkManager;
    }

    /// <inheritdoc />
    public async Task<AssaySuiteField> CreateAsync(CreateAssaySuiteFieldDto input, bool returnExist = false)
    {
        var tenantId = _unitOfWorkManager.Current.GetTenantId();

        var existingAssaySuiteField = await _repository.FirstOrDefaultAsync(d => 
            d.TenantId == tenantId && d.Name == input.Name);
            
        if (existingAssaySuiteField != null)
        {
            if (returnExist)
            {
                return existingAssaySuiteField;
            }

            throw new UserFriendlyException($"The Assay Suite Field with the name {existingAssaySuiteField.Name} already exists.");
        }

        var assaySuiteField = new AssaySuiteField
        {
            Name = input.Name,
            Sequence = input.Sequence,
            IsActive = input.IsActive,
            BackgroundColour = input.BackgroundColour,
            TextColour = input.TextColour,
            AssaySuiteId = input.AssaySuiteId,
            AssayAttributeId = input.AssayAttributeId,
        };

        await _repository.InsertAsync(assaySuiteField);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        return assaySuiteField;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<AssaySuiteFieldDto>> GetAllAsync(PagedAssaySuiteFieldResultRequestDto input)
    {
        var query = _repository.GetAll()
            .WhereIf(!input.Keyword.IsNullOrEmpty(), x => input.Keyword != null && x.Name.Contains(input.Keyword))
            .WhereIf(input.IsActive.HasValue, x => input.IsActive != null && x.IsActive == input.IsActive.Value);

        var totalCount = await query.CountAsync();

        var assaySuiteFields = await query
            .OrderBy(x => x.Sequence)
            .PageBy(input)
            .ToListAsync();

        var assaySuiteFieldDto = _mapper.Map<List<AssaySuiteFieldDto>>(assaySuiteFields);
        
        return new PagedResultDto<AssaySuiteFieldDto>(totalCount, assaySuiteFieldDto);
    }

    /// <inheritdoc />
    public async Task<AssaySuiteFieldDto> GetAsync(EntityDto<int> input)
    {
        var assaySuiteField = await ValidateAssaySuiteFieldEntity(input.Id);
        var dto = _mapper.Map<AssaySuiteFieldDto>(assaySuiteField);
        
        return dto;
    }

    /// <inheritdoc />
    public async Task<AssaySuiteFieldDto> UpdateAsync(UpdateAssaySuiteFieldDto input)
    {
        var assaySuiteField = await ValidateAssaySuiteFieldEntity(input.Id);

        assaySuiteField.Name = input.Name;
        assaySuiteField.Sequence = input.Sequence;
        assaySuiteField.IsActive = input.IsActive;
        assaySuiteField.BackgroundColour = input.BackgroundColour;
        assaySuiteField.TextColour = input.TextColour;
        assaySuiteField.AssaySuiteId = input.AssaySuiteId;
        assaySuiteField.AssayAttributeId = input.AssayAttributeId;

        await _repository.UpdateAsync(assaySuiteField);

        return _mapper.Map<AssaySuiteFieldDto>(assaySuiteField);
    }

    /// <inheritdoc />
    public async Task DeleteAsync(EntityDto<int> input)
    {
        await ValidateAssaySuiteFieldEntity(input.Id);
        await _repository.DeleteAsync(input.Id);
    }

    private async Task<AssaySuiteField> ValidateAssaySuiteFieldEntity(int id)
    {
        var assaySuiteField = await _repository.GetAsync(id);
        if (assaySuiteField == null)
        {
            throw new EntityNotFoundException(typeof(AssaySuiteField), id);
        }

        return assaySuiteField;
    }
}