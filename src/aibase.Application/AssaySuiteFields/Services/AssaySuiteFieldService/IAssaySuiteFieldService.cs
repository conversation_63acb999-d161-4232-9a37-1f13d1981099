using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Dependency;
using aibase.AssaySuiteFields.Dto;

namespace aibase.AssaySuiteFields.Services.AssaySuiteFieldService;

/// <inheritdoc />
public interface IAssaySuiteFieldService : ITransientDependency
{
    /// <summary>
    /// Create a new AssaySuiteField
    /// </summary>
    /// <param name="input">Create DTO</param>
    /// <param name="returnExist">Return existing if found instead of throwing exception</param>
    /// <returns></returns>
    Task<AssaySuiteField> CreateAsync(CreateAssaySuiteFieldDto input, bool returnExist = false);

    /// <summary>
    /// Get all AssaySuiteFields with paging
    /// </summary>
    /// <param name="input">Page parameters</param>
    /// <returns></returns>
    Task<PagedResultDto<AssaySuiteFieldDto>> GetAllAsync(PagedAssaySuiteFieldResultRequestDto input);

    /// <summary>
    /// Get AssaySuiteField by id
    /// </summary>
    /// <param name="input">Entity id</param>
    /// <returns></returns>
    Task<AssaySuiteFieldDto> GetAsync(EntityDto<int> input);

    /// <summary>
    /// Update AssaySuiteField
    /// </summary>
    /// <param name="input">Update DTO</param>
    /// <returns></returns>
    Task<AssaySuiteFieldDto> UpdateAsync(UpdateAssaySuiteFieldDto input);

    /// <summary>
    /// Delete AssaySuiteField
    /// </summary>
    /// <param name="input">Entity id</param>
    /// <returns></returns>
    Task DeleteAsync(EntityDto<int> input);
}