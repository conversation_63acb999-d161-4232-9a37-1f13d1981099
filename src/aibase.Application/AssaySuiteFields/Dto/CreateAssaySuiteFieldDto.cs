using System.ComponentModel.DataAnnotations;
using Abp.AutoMapper;

namespace aibase.AssaySuiteFields.Dto;

/// <summary>
/// 
/// </summary>
[AutoMapTo(typeof(AssaySuiteField))]
public class CreateAssaySuiteFieldDto
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int Sequence { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public bool IsActive { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? BackgroundColour { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? TextColour { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int AssaySuiteId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int AssayAttributeId { get; set; }
}