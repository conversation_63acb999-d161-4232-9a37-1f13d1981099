using Abp.Application.Services.Dto;
using Abp.AutoMapper;
using aibase.AssayAttributes.Dto;

namespace aibase.AssaySuiteFields.Dto;

/// <inheritdoc />
[AutoMap(typeof(AssaySuiteField))]
public class AssaySuiteFieldDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public int Sequence { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? BackgroundColour { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? TextColour { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int AssaySuiteId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int AssayAttributeId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int TenantId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public AssayAttributeDto? AssayAttribute { get; set; }
}