using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.ImageEntity;
using aibase.JobEntity;
using aibase.Jobs.Dto;
using aibase.Jobs.Services.Socket;
using aibase.WorkflowJobs;
using AutoMapper;
using Hangfire;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace aibase.Jobs.Services.WorkflowJobService;

/// <inheritdoc />
public class WorkflowJobService : IWorkflowJobService
{
    private readonly IRepository<WorkflowJob, int> _repository;
    private readonly IRepository<Image, int> _imageRepository;
    private readonly IHubContext<SocketWorkflowJob> _hubContext;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="repository"></param>
    /// <param name="imageRepository"></param>
    /// <param name="hubContext"></param>
    /// <param name="unitOfWorkManager"></param>
    /// <param name="abpSession"></param>
    /// <param name="mapper"></param>
    public WorkflowJobService(
        IRepository<WorkflowJob, int> repository,
        IRepository<Image, int> imageRepository,
        IHubContext<SocketWorkflowJob> hubContext,
        IUnitOfWorkManager unitOfWorkManager,
        IAbpSession abpSession,
        IMapper mapper)
    {
        _repository = repository;
        _imageRepository = imageRepository;
        _hubContext = hubContext;
        _unitOfWorkManager = unitOfWorkManager;
        _abpSession = abpSession;
        _mapper = mapper;
    }

    /// <inheritdoc />
    public async Task<WorkflowJob> ExecuteBatchAsync(CreateWorkflowJobDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        // Check if a WorkflowJob already exists with the same DrillholeId and WorkflowId
        var images = await _imageRepository.GetAll()
            .Where(image => image.DrillHoleId == input.DrillholeId)
            .WhereIf(input.Type.HasValue, image => image.Type == input.Type)
            .WhereIf(input.StandardType.HasValue, image => image.StandardType == input.StandardType)
            .WhereIf(input.ImageClass.HasValue, image => image.ImageClass == input.ImageClass)
            .WhereIf(input.StatusFilter != null, x => x.ImageStatus == input.StatusFilter)
            .WhereIf(input.ImageCategory.HasValue, x => x.ImageCategory == input.ImageCategory)
            .WhereIf(input.ImageTypeId.HasValue, x => x.ImageTypeId == input.ImageTypeId)
            .WhereIf(input.ImageSubtypeId.HasValue, x => x.ImageSubtypeId == input.ImageSubtypeId)
            .OrderBy(x => x.DepthFrom)
            .ToListAsync();


        var workflowJob = new WorkflowJob
        {
            Name = input.Name,
            Description = input.Description,
            ProjectId = input.ProjectId,
            ProspectId = input.ProspectId,
            DrillholeId = input.DrillholeId,
            WorkflowId = input.WorkflowId,
            JobId = "",
            Status = JobStatus.Running,
            StatusFilter = input.StatusFilter,
            StatusDone = input.StatusDone,
            Type = input.Type,
            StandardType = input.StandardType,
            ImageClass = input.ImageClass,
            ImageCategory = input.ImageCategory,
            ImageTypeId = input.ImageTypeId,
            ImageSubtypeId = input.ImageSubtypeId,
            TotalImage = images.Count,
            TotalCompleted = 0,
            TotalErrors = 0,
            TenantId = _abpSession.GetTenantId(),
        };

        await _repository.InsertAsync(workflowJob);
        await _unitOfWorkManager.Current.SaveChangesAsync(); // To get new id.

        if (images.Count < 0) return workflowJob;

        var processBatchDto = new ProcessBatchDto()
        {
            WorkflowJobId = workflowJob.Id,
            Images = images
        };
        var backgroundId =
            BackgroundJob.Enqueue<JobService.JobService>(JobConstants.WorkflowQueue,
                (job) => job.ExecuteAsync(processBatchDto));

        workflowJob.JobId = backgroundId;

        await _repository.UpdateAsync(workflowJob);

        return workflowJob;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<WorkflowJobDto>> GetAllAsync(PagedWorkflowJobResultRequestDto input)
    {
        var query = _repository.GetAllIncluding(x => x.Workflow, 
                x => x.Project, 
                x => x.Prospect, 
                x => x.DrillHole, 
                x => x.ImageType,
                x => x.ImageSubtype)
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(),
                x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .WhereIf(input.ProjectId.HasValue, x => x.ProjectId == input.ProjectId)
            .WhereIf(input.WorkflowId.HasValue, x => x.WorkflowId == input.WorkflowId)
            .WhereIf(input.DrillholeId.HasValue, x => x.DrillholeId == input.DrillholeId)
            .WhereIf(!input.JobId.IsNullOrWhiteSpace(), x => x.JobId == input.JobId)
            .WhereIf(input.Status.HasValue, x => x.Status == input.Status)
            .OrderByDescending(r => r.CreationTime);

        var totalCount = await query.CountAsync();

        var workflows = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        return new PagedResultDto<WorkflowJobDto>(totalCount, _mapper.Map<List<WorkflowJobDto>>(workflows));
    }

    /// <inheritdoc />
    public async Task<WorkflowJobDto> GetAsync(EntityDto<int> input)
    {
        var workflowJob = await _repository
            .GetAllIncluding(x => x.Workflow, 
                x => x.Project, 
                x => x.Prospect, 
                x => x.DrillHole, 
                x => x.ImageType,
                x => x.ImageSubtype)
            .FirstOrDefaultAsync(x => x.Id == input.Id);

        if (workflowJob == null)
        {
            throw new EntityNotFoundException(typeof(WorkflowJob), input.Id);
        }

        var workflow = _mapper.Map<WorkflowJobDto>(workflowJob);

        return workflow;
    }

    /// <inheritdoc />
    public async Task<WorkflowJobDto> UpdateAsync(UpdateWorkflowJobDto input)
    {
        var workflowJob = await _repository.FirstOrDefaultAsync(x => x.Id == input.Id);
        if (workflowJob == null)
        {
            throw new EntityNotFoundException(typeof(WorkflowJob), input.Id);
        }

        if (workflowJob.Status == JobStatus.Running)
        {
            throw new UserFriendlyException("Cannot update the batch while it is running.");
        }

        var images = await _imageRepository.GetAll()
            .AsNoTracking()
            .Where(image => image.DrillHoleId == input.DrillholeId)
            .WhereIf(input.Type.HasValue, image => image.Type == input.Type)
            .WhereIf(input.StandardType.HasValue, image => image.StandardType == input.StandardType)
            .WhereIf(input.ImageClass.HasValue, image => image.ImageClass == input.ImageClass)
            .WhereIf(input.StatusFilter != null, x => x.ImageStatus == input.StatusFilter)
            .WhereIf(input.ImageCategory.HasValue, x => x.ImageCategory == input.ImageCategory)
            .WhereIf(input.ImageTypeId.HasValue, x => x.ImageTypeId == input.ImageTypeId)
            .WhereIf(input.ImageSubtypeId.HasValue, x => x.ImageSubtypeId == input.ImageSubtypeId)
            .ToListAsync();

        workflowJob.TotalCompleted = 0;
        workflowJob.TotalErrors = 0;
        workflowJob.TotalImage = images.Count;
        workflowJob.Name = input.Name ?? workflowJob.Name;
        workflowJob.Description = input.Description ?? workflowJob.Description;
        workflowJob.StatusDone = input.StatusDone;
        workflowJob.StatusFilter = input.StatusFilter;
        workflowJob.Type = input.Type;
        workflowJob.StandardType = input.StandardType;
        workflowJob.ImageClass = input.ImageClass;
        workflowJob.ImageCategory = input.ImageCategory;
        workflowJob.ImageTypeId = input.ImageTypeId;
        workflowJob.ImageSubtypeId = input.ImageSubtypeId;
        workflowJob.ProjectId = input.ProjectId ?? workflowJob.ProjectId;
        workflowJob.ProspectId = input.ProspectId ?? workflowJob.ProspectId;
        workflowJob.DrillholeId = input.DrillholeId ?? workflowJob.DrillholeId;
        workflowJob.WorkflowId = input.WorkflowId ?? workflowJob.WorkflowId;

        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task RerunJobAsync(EntityDto<int> input)
    {
        var workflowJob = await _repository.GetAsync(input.Id);
        workflowJob.Status = JobStatus.Running;
        workflowJob.TotalErrors = 0;

        var images = await _imageRepository.GetAll()
            .Where(image => image.DrillHoleId == workflowJob.DrillholeId)
            .WhereIf(workflowJob.Type.HasValue, image => image.Type == workflowJob.Type)
            .WhereIf(workflowJob.StandardType.HasValue, image => image.StandardType == workflowJob.StandardType)
            .WhereIf(workflowJob.StatusFilter != null, image => image.ImageStatus == workflowJob.StatusFilter)
            .WhereIf(workflowJob.ImageClass.HasValue, image => image.ImageClass == workflowJob.ImageClass)
            .WhereIf(workflowJob.ImageCategory.HasValue, image => image.ImageCategory == workflowJob.ImageCategory)
            .WhereIf(workflowJob.ImageTypeId.HasValue, image => image.ImageTypeId == workflowJob.ImageTypeId)
            .WhereIf(workflowJob.ImageSubtypeId.HasValue, image => image.ImageSubtypeId == workflowJob.ImageSubtypeId)
            .OrderBy(x => x.DepthFrom)
            .ToListAsync();

        if (images.Count <= 0)
        {
            throw new UserFriendlyException("No images found.");
        }

        List<Image> imageProcesses;
        if (workflowJob.StatusFilter == null)
        {
            workflowJob.TotalCompleted = 0;
            imageProcesses = images.OrderBy(x => x.Id).ToList();
        }
        else
        {
            workflowJob.TotalCompleted = images.Count(x => x.ImageStatus == workflowJob.StatusDone);
            imageProcesses = images.Where(x => x.ImageStatus == workflowJob.StatusFilter).OrderBy(x => x.Id).ToList();
        }

        var processBatchDto = new ProcessBatchDto()
        {
            WorkflowJobId = workflowJob.Id,
            Images = imageProcesses
        };

        var backgroundId =
            BackgroundJob.Enqueue<JobService.JobService>(JobConstants.WorkflowQueue,
                (job) => job.ExecuteAsync(processBatchDto));

        workflowJob.JobId = backgroundId;

        await _repository.UpdateAsync(workflowJob);

        var jsonSerializerSettings = new JsonSerializerSettings
        {
            ContractResolver = new CamelCasePropertyNamesContractResolver()
        };
        await _hubContext.Clients.Group(workflowJob.Id.ToString())
            .SendAsync("WorkflowJobSocket",
                JsonConvert.SerializeObject(workflowJob, jsonSerializerSettings));
    }

    /// <inheritdoc />
    public async Task CancelJobAsync(EntityDto<int> input)
    {
        var workflowJob = await _repository.GetAsync(input.Id);
        workflowJob.Status = JobStatus.Canceled;

        BackgroundJob.Delete(workflowJob.JobId);

        await _hubContext.Clients.Group(workflowJob.Id.ToString())
            .SendAsync("WorkflowJobSocket",
                JsonConvert.SerializeObject(workflowJob, new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                }));
    }
}