using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.DownholeSurveys.Dto;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.DownholeSurveys.Services.DownholeSurveyTypeService;

/// <inheritdoc />
public class DownholeSurveyTypeService : IDownholeSurveyTypeService
{
    private readonly IRepository<DownholeSurveyType, int> _repository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    ///
    /// </summary>
    public DownholeSurveyTypeService(
        IRepository<DownholeSurveyType, int> repository,
        IAbpSession abpSession,
        IMapper mapper,
        IUnitOfWorkManager unitOfWorkManager)
    {
        _repository = repository;
        _abpSession = abpSession;
        _mapper = mapper;
        _unitOfWorkManager = unitOfWorkManager;
    }

    /// <inheritdoc />
    public async Task<DownholeSurveyType> CreateAsync(CreateDownholeSurveyTypeDto input, bool returnExist = false)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }
        var tenantId = _abpSession.GetTenantId();

        var existingType =
            await _repository.FirstOrDefaultAsync(d => d.TenantId == tenantId && d.Name == input.Name);
        if (existingType != null)
        {
            if (returnExist)
            {
                return existingType;
            }

            throw new UserFriendlyException($"The Downhole Survey Type with the name {existingType.Name} already exists.");
        }

        // If the new record is being set as default, ensure all other records for this tenant are not default
        if (input.IsDefault)
        {
            await SetAllOtherRecordsAsNonDefaultAsync(tenantId);
        }

        var surveyType = new DownholeSurveyType()
        {
            Name = input.Name,
            IsActive = input.IsActive,
            IsDefault = input.IsDefault,
            TenantId = tenantId,
        };

        await _repository.InsertAsync(surveyType);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        return surveyType;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<DownholeSurveyTypeDto>> GetAllAsync(PagedDownholeSurveyTypeResultRequestDto input)
    {
        var query = _repository.GetAll()
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .OrderBy(r => r.Name);

        var totalCount = await query.CountAsync();

        var types = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        return new PagedResultDto<DownholeSurveyTypeDto>(totalCount, _mapper.Map<List<DownholeSurveyTypeDto>>(types));
    }

    /// <inheritdoc />
    public async Task<DownholeSurveyTypeDto> GetAsync(EntityDto<int> input)
    {
        var surveyType = await ValidateDownholeSurveyTypeEntity(input.Id);
        return _mapper.Map<DownholeSurveyTypeDto>(surveyType);
    }

    /// <inheritdoc />
    public async Task<DownholeSurveyTypeDto> UpdateAsync(UpdateDownholeSurveyTypeDto input)
    {
        var surveyType = await ValidateDownholeSurveyTypeEntity(input.Id);

        // If the record is being set as default, ensure all other records for this tenant are not default
        if (input.IsDefault.HasValue && input.IsDefault.Value)
        {
            await SetAllOtherRecordsAsNonDefaultAsync(surveyType.TenantId, input.Id);
        }

        surveyType.Name = input.Name ?? surveyType.Name;
        surveyType.IsActive = input.IsActive ?? surveyType.IsActive;
        surveyType.IsDefault = input.IsDefault ?? surveyType.IsDefault;

        await _unitOfWorkManager.Current.SaveChangesAsync();

        return await GetAsync(input);
    }

    /// <summary>
    /// Sets all DownholeSurveyType records for the specified tenant to IsDefault = false
    /// </summary>
    /// <param name="tenantId">The tenant ID</param>
    /// <param name="excludeId">Optional ID to exclude from the update (for UpdateAsync scenario)</param>
    private async Task SetAllOtherRecordsAsNonDefaultAsync(int tenantId, int? excludeId = null)
    {
        var query = _repository.GetAll()
            .Where(x => x.TenantId == tenantId && x.IsDefault);

        if (excludeId.HasValue)
        {
            query = query.Where(x => x.Id != excludeId.Value);
        }

        var defaultRecords = await query.ToListAsync();

        foreach (var record in defaultRecords)
        {
            record.IsDefault = false;
        }
    }

    private async Task<DownholeSurveyType> ValidateDownholeSurveyTypeEntity(int id)
    {
        var surveyType = await _repository.FirstOrDefaultAsync(x => x.Id == id);

        if (surveyType == null)
        {
            throw new EntityNotFoundException(typeof(DownholeSurveyType), id);
        }

        return surveyType;
    }
}