using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Dependency;
using aibase.DownholeSurveys.Dto;

namespace aibase.DownholeSurveys.Services.DownholeSurveyTypeService;

/// <summary>
/// 
/// </summary>
public interface IDownholeSurveyTypeService : ITransientDependency
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <param name="returnExist"></param>
    /// <returns></returns>
    Task<DownholeSurveyType> CreateAsync(CreateDownholeSurveyTypeDto input, bool returnExist = false);
        
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagedResultDto<DownholeSurveyTypeDto>> GetAllAsync(PagedDownholeSurveyTypeResultRequestDto input);
        
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<DownholeSurveyTypeDto> GetAsync(EntityDto<int> input);
        
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<DownholeSurveyTypeDto> UpdateAsync(UpdateDownholeSurveyTypeDto input);
}