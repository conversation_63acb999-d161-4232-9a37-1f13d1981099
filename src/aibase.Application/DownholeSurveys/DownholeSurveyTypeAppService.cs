using System.Threading.Tasks;
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using aibase.DownholeSurveys.Dto;
using aibase.DownholeSurveys.Services.DownholeSurveyTypeService;

namespace aibase.DownholeSurveys;

/// <summary>
/// 
/// </summary>
[AbpAuthorize]
public class DownholeSurveyTypeAppService : AsyncCrudAppService<DownholeSurveyType, DownholeSurveyTypeDto, int,
    PagedDownholeSurveyTypeResultRequestDto,
    CreateDownholeSurveyTypeDto, UpdateDownholeSurveyTypeDto>, IDownholeSurveyTypeAppService
{
    private readonly IDownholeSurveyTypeService _downholeSurveyTypeService;

    /// <inheritdoc />
    public DownholeSurveyTypeAppService(
        IRepository<DownholeSurveyType> repository,
        IDownholeSurveyTypeService downholeSurveyTypeService) : base(repository)
    {
        _downholeSurveyTypeService = downholeSurveyTypeService;
    }

    /// <inheritdoc />
    public override async Task<DownholeSurveyTypeDto> CreateAsync(CreateDownholeSurveyTypeDto input)
    {
        var surveyType = await _downholeSurveyTypeService.CreateAsync(input);
        return MapToEntityDto(surveyType);
    }

    /// <inheritdoc />
    public override async Task<PagedResultDto<DownholeSurveyTypeDto>> GetAllAsync(
        PagedDownholeSurveyTypeResultRequestDto input)
    {
        return await _downholeSurveyTypeService.GetAllAsync(input);
    }

    /// <inheritdoc />
    public override async Task<DownholeSurveyTypeDto> GetAsync(EntityDto<int> input)
    {
        return await _downholeSurveyTypeService.GetAsync(input);
    }

    /// <inheritdoc />
    public override async Task<DownholeSurveyTypeDto> UpdateAsync(UpdateDownholeSurveyTypeDto input)
    {
        return await _downholeSurveyTypeService.UpdateAsync(input);
    }
}