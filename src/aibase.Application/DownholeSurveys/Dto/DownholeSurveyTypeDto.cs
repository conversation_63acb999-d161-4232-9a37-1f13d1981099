using Abp.Application.Services.Dto;
using AutoMapper;

namespace aibase.DownholeSurveys.Dto;

/// <inheritdoc />
[AutoMap(typeof(DownholeSurveyType))]
public class DownholeSurveyTypeDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool IsDefault { get; set; }
}