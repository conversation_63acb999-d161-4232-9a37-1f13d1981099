using Abp.Application.Services.Dto;
using aibase.DesurveyResults;
using AutoMapper;

namespace aibase.DownholeSurveys.Dto;

/// <inheritdoc />
[AutoMap(typeof(DesurveyResult))]
public class DesurveyResultDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public int DrillHoleId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public double Depth { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public double Northing { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public double Easting { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public double Elevation { get; set; }
}