﻿using Abp.Application.Services.Dto;

namespace aibase.Users.Dto
{
    /// <inheritdoc />
    public class PagedUserResultRequestDto : PagedResultRequestDto
    {
        /// <summary>
        /// 
        /// </summary>
        public string? Keyword { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int? CompanyId { get; set; }
    }
}
