﻿using Abp.Application.Services;
using Abp.Authorization;
using Abp.Domain.Repositories;
using aibase.AIServiceEntity;
using aibase.AIServices.Dto;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using aibase.AIServices.Services.AiService;
using Microsoft.AspNetCore.Mvc;

namespace aibase.AIServices;

/// <summary>
/// 
/// </summary>
[AbpAuthorize]
public class AIServiceAppService : AsyncCrudAppService<AIService, AiServiceDto, int, PagedAiServiceResultRequestDto,
    CreateAiServiceDto, AiServiceDto>, IAiServiceAppService
{
    private readonly IAiService _aiService;

    /// <inheritdoc />
    public AIServiceAppService(IRepository<AIService, int> repository, IAiService aiService) : base(repository)
    {
        _aiService = aiService;
    }

    /// <inheritdoc />
    [ApiExplorerSettings(IgnoreApi = true)]
    public override Task<AiServiceDto> CreateAsync(CreateAiServiceDto input)
    {
        return base.CreateAsync(input);
    }

    /// <inheritdoc />
    [ApiExplorerSettings(IgnoreApi = true)]
    public override Task<AiServiceDto> UpdateAsync(AiServiceDto input)
    {
        return base.UpdateAsync(input);
    }

    /// <inheritdoc />
    [ApiExplorerSettings(IgnoreApi = true)]
    public override Task DeleteAsync(EntityDto<int> input)
    {
        return base.DeleteAsync(input);
    }

    /// <inheritdoc />
    [ApiExplorerSettings(IgnoreApi = true)]
    public override Task<AiServiceDto> GetAsync(EntityDto<int> input)
    {
        return base.GetAsync(input);
    }

    /// <inheritdoc />
    public override async Task<PagedResultDto<AiServiceDto>> GetAllAsync(PagedAiServiceResultRequestDto input)
    {
        return await _aiService.GetAllAsync(input);
    }
}