﻿
using System.ComponentModel.DataAnnotations;

namespace aibase.AIServices.Dto;

/// <summary>
/// 
/// </summary>
public class CreateAiServiceDto
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public string Thumbnail { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public string? SubscriptionKey { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public string? Endpoint { get; set; }
}