using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Repositories;
using Abp.Extensions;
using Abp.Linq.Extensions;
using aibase.AIServiceEntity;
using aibase.AIServices.Dto;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.AIServices.Services.AiService;

/// <inheritdoc />
public class AiService : IAiService
{
    private readonly IRepository<AIService, int> _repository;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="repository"></param>
    /// <param name="mapper"></param>
    public AiService(IRepository<AIService, int> repository, IMapper mapper)
    {
        _repository = repository;
        _mapper = mapper;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<AiServiceDto>> GetAllAsync(PagedAiServiceResultRequestDto input)
    {
        var query = _repository.GetAll()
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(),
                x => input.Keyword != null && (x.Name.ToLower().Contains(input.Keyword.ToLower()) ||
                                               x.Title.ToLower().Contains(input.Keyword.ToLower())))
            .OrderByDescending(r => r.CreationTime);
        
        var totalCount = await query.CountAsync();

        var aiServices = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();
        
        return new PagedResultDto<AiServiceDto>(totalCount, _mapper.Map<List<AiServiceDto>>(aiServices));
    }
}