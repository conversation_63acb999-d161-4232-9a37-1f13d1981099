using Abp.Application.Services.Dto;

namespace aibase.StructureTypes.Dto;

/// <summary>
/// DTO for updating a StructureType
/// </summary>
public class UpdateStructureTypeDto : EntityDto
{
    /// <summary>
    /// Name of the structure type
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Description of the structure type
    /// </summary>
    public string? Description { get; set; }
}