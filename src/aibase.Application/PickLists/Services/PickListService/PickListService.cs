using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.GeologyFields;
using aibase.PickListItems;
using aibase.PickLists.Dto;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.PickLists.Services.PickListService;

/// <inheritdoc />
public class PickListService : IPickListService
{
    private readonly IRepository<PickList, int> _repository;
    private readonly IRepository<PickListItem, int> _pickListItemRepository;
    private readonly IRepository<GeologyField, int> _geologyFieldRepository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="repository"></param>
    /// <param name="pickListItemRepository"></param>
    /// <param name="geologyFieldRepository"></param>
    /// <param name="unitOfWorkManager"></param>
    /// <param name="abpSession"></param>
    /// <param name="mapper"></param>
    public PickListService(
        IRepository<PickList, int> repository, 
        IRepository<PickListItem, int> pickListItemRepository,
        IRepository<GeologyField, int> geologyFieldRepository,
        IUnitOfWorkManager unitOfWorkManager, 
        IAbpSession abpSession, 
        IMapper mapper
        )
    {
        _repository = repository;
        _pickListItemRepository = pickListItemRepository;
        _geologyFieldRepository = geologyFieldRepository;
        _unitOfWorkManager = unitOfWorkManager;
        _abpSession = abpSession;
        _mapper = mapper;
    }

    /// <inheritdoc />
    public async Task<PickList> CreateAsync(CreatePickListDto input, bool returnExist = false)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }
        var tenantId = _abpSession.GetTenantId();

        var existingPickList =
            await _repository.FirstOrDefaultAsync(d => d.TenantId == tenantId && d.Name == input.Name);
        if (existingPickList != null)
        {
            if (returnExist)
            {
                return existingPickList;
            }
            
            throw new UserFriendlyException($"The Pick List with the name {existingPickList.Name} already exists.");
        }

        var pickList = new PickList()
        {
            Name = input.Name,
            IsActive = input.IsActive,
            TenantId = _abpSession.GetTenantId(),
        };

        await _repository.InsertAsync(pickList);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        var pickListItems = input.PickListitems.Select(item => new PickListItem()
        {
            Name = item.Name,
            Sequence = item.Sequence,
            IsActive = item.IsActive,
            PickListId = pickList.Id,
            TenantId = _abpSession.GetTenantId()
        }).ToList();

        foreach (var item in pickListItems)
        {
            await _pickListItemRepository.InsertAsync(item);
        }
        await _unitOfWorkManager.Current.SaveChangesAsync();

        return pickList;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<PickListDto>> GetAllAsync(PagedPickListResultRequestDto input)
    {
        var query = _repository.GetAllIncluding(x => x.PickListItems)
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .OrderBy(r => r.Name);

        var totalCount = await query.CountAsync();

        var pickLists = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        return new PagedResultDto<PickListDto>(totalCount, _mapper.Map<List<PickListDto>>(pickLists));
    }

    /// <inheritdoc />
    public async Task<PickListDto> GetAsync(EntityDto<int> input)
    {
        var pickList = await ValidatePickListEntity(input.Id);
        return _mapper.Map<PickListDto>(pickList);
    }

    /// <inheritdoc />
    public async Task<PickListDto> UpdateAsync(UpdatePickListDto input)
    {
        var pickList = await ValidatePickListEntity(input.Id);
        
        if (input.IsActive == false)
        {
            var geologyDateUsed =
                await _geologyFieldRepository.FirstOrDefaultAsync(x =>
                    x.Type == FieldType.PickList && x.PickListId == input.Id);
            if (geologyDateUsed != null)
            {
                throw new UserFriendlyException("Cannot deactivate this record because it is already in use.");
            }
        }

        pickList.Name = input.Name ?? pickList.Name;
        pickList.IsActive = input.IsActive ?? pickList.IsActive;
        
        if (input.PickListitems == null) return await GetAsync(input);
        
        // Get existing items for this picklist
        var existingItems = pickList.PickListItems.ToList();
        var existingItemIds = existingItems.Select(x => x.Id).ToList();
        var inputItemIds = input.PickListitems.Where(x => x.Id != 0).Select(x => x.Id).ToList();
        
        // Items to delete (exist in DB but not in input)
        var itemsToDelete = existingItems.Where(x => !inputItemIds.Contains(x.Id)).ToList();
        foreach (var item in itemsToDelete)
        {
            await _pickListItemRepository.DeleteAsync(item.Id);
        }
        
        // Update existing items and create new ones
        foreach (var inputItem in input.PickListitems)
        {
            if (existingItemIds.Contains(inputItem.Id))
            {
                // Update existing item
                var existingItem = existingItems.First(x => x.Id == inputItem.Id);
                existingItem.Name = inputItem.Name;
                existingItem.Sequence = inputItem.Sequence;
                existingItem.IsActive = inputItem.IsActive;
                await _pickListItemRepository.UpdateAsync(existingItem);
            }
            else
            {
                // Create new item
                var newItem = new PickListItem
                {
                    Name = inputItem.Name,
                    Sequence = inputItem.Sequence,
                    IsActive = inputItem.IsActive,
                    PickListId = pickList.Id,
                    TenantId = _abpSession.GetTenantId()
                };
                await _pickListItemRepository.InsertAsync(newItem);
            }
        }
        
        await _unitOfWorkManager.Current.SaveChangesAsync();
        
        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task DeleteAsync(EntityDto<int> input)
    {
        var pickList = await ValidatePickListEntity(input.Id);

        var geologyFieldUsed = await _geologyFieldRepository.FirstOrDefaultAsync(x =>
            x.Type == FieldType.PickList && x.PickListId == input.Id);
        if (geologyFieldUsed != null)
        {
            throw new UserFriendlyException("Cannot delete this PickList because it is being used in GeologyField.");
        }
        
        await _repository.DeleteAsync(pickList);
    }

    private async Task<PickList> ValidatePickListEntity(int id)
    {
        var pickList = await _repository.GetAllIncluding(x => x.PickListItems)
            .FirstOrDefaultAsync(x => x.Id == id);

        if (pickList == null)
        {
            throw new EntityNotFoundException(typeof(PickList), id);
        }

        return pickList;
    }
}
