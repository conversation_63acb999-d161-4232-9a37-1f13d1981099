using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.AssayAttributes.Dto;
using aibase.AssaySuiteAttributes;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.AssayAttributes.Services.AssayAttributeService;

/// <inheritdoc />
public class AssayAttributeService : IAssayAttributeService
{
    private readonly IRepository<AssayAttribute, int> _repository;
    private readonly IRepository<AssaySuiteAttribute, int> _assaySuiteAttributeRepository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    ///
    /// </summary>
    /// <param name="repository"></param>
    /// <param name="assaySuiteAttributeRepository"></param>
    /// <param name="unitOfWorkManager"></param>
    /// <param name="abpSession"></param>
    /// <param name="mapper"></param>
    public AssayAttributeService(
        IRepository<AssayAttribute, int> repository,
        IRepository<AssaySuiteAttribute, int> assaySuiteAttributeRepository,
        IUnitOfWorkManager unitOfWorkManager,
        IAbpSession abpSession,
        IMapper mapper)
    {
        _repository = repository;
        _assaySuiteAttributeRepository = assaySuiteAttributeRepository;
        _unitOfWorkManager = unitOfWorkManager;
        _abpSession = abpSession;
        _mapper = mapper;
    }

    /// <inheritdoc />
    public async Task<AssayAttribute> CreateAsync(CreateAssayAttributeDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var assayAttribute = new AssayAttribute()
        {
            Name = input.Name,
            Code = input.Code,
            TextColor = input.TextColor,
            BackgroundColor = input.BackgroundColor,
            Description = input.Description ?? string.Empty,
            IsActive = input.IsActive,
            TenantId = _abpSession.GetTenantId(),
        };

        await _repository.InsertAsync(assayAttribute);

        // Save changes to ensure the AssayAttribute is committed to the database
        // This is necessary so the ID is available for foreign key relationships
        await _unitOfWorkManager.Current.SaveChangesAsync();

        // Assign to assay suites if SuiteIds are provided
        if (input.SuiteIds != null && input.SuiteIds.Count > 0)
        {
            await AssignToAssaySuitesAsync(assayAttribute.Id, input.SuiteIds);
        }

        return assayAttribute;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<AssayAttributeDto>> GetAllAsync(PagedAssayAttributeResultRequestDto input)
    {
        var query = _repository.GetAll()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .OrderByDescending(r => r.CreationTime);

        var totalCount = await query.CountAsync();

        var aiServices = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        return new PagedResultDto<AssayAttributeDto>(totalCount, _mapper.Map<List<AssayAttributeDto>>(aiServices));
    }

    /// <inheritdoc />
    public async Task<AssayAttributeDto> GetAsync(EntityDto<int> input)
    {
        var assayAttribute = await ValidateAssayAttributeEntity(input.Id);
        return _mapper.Map<AssayAttributeDto>(assayAttribute);
    }

    /// <inheritdoc />
    public async Task<AssayAttributeDto> UpdateAsync(UpdateAssayAttributeDto input)
    {
        var assayAttribute = await ValidateAssayAttributeEntity(input.Id);

        assayAttribute.Name = input.Name ?? assayAttribute.Name;
        assayAttribute.Code = input.Code ?? assayAttribute.Code;
        assayAttribute.TextColor = input.TextColor ?? assayAttribute.TextColor;
        assayAttribute.BackgroundColor = input.BackgroundColor ?? assayAttribute.BackgroundColor;
        assayAttribute.Description = input.Description ?? string.Empty;
        assayAttribute.IsActive = input.IsActive ?? assayAttribute.IsActive;

        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task DeleteAsync(EntityDto<int> input)
    {
        await ValidateAssayAttributeEntity(input.Id);
        await _repository.DeleteAsync(input.Id);
    }

    private async Task<AssayAttribute> ValidateAssayAttributeEntity(int id)
    {
        var assayAttribute = await _repository.FirstOrDefaultAsync(x => x.Id == id);

        if (assayAttribute == null)
        {
            throw new EntityNotFoundException(typeof(AssayAttribute), id);
        }

        return assayAttribute;
    }

    /// <summary>
    /// Assigns an assay attribute to multiple assay suites
    /// </summary>
    /// <param name="assayAttributeId">The ID of the assay attribute to assign</param>
    /// <param name="suiteIds">List of assay suite IDs to assign the attribute to</param>
    private async Task AssignToAssaySuitesAsync(int assayAttributeId, List<int> suiteIds)
    {
        var tenantId = _abpSession.GetTenantId();

        foreach (var suiteId in suiteIds)
        {
            // Check if assignment already exists to avoid duplicates
            var existingAssignment = await _assaySuiteAttributeRepository
                .FirstOrDefaultAsync(x => x.AssayAttributeId == assayAttributeId &&
                                         x.AssaySuiteId == suiteId &&
                                         x.TenantId == tenantId);

            if (existingAssignment == null)
            {
                var assaySuiteAttribute = new AssaySuiteAttribute()
                {
                    AssayAttributeId = assayAttributeId,
                    AssaySuiteId = suiteId,
                    TenantId = tenantId,
                };

                await _assaySuiteAttributeRepository.InsertAsync(assaySuiteAttribute);
            }
        }
    }
}