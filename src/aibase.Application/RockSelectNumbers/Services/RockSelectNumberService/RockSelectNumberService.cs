using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.GeologyFields;
using aibase.RockSelectNumbers.Dto;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.RockSelectNumbers.Services.RockSelectNumberService;

/// <inheritdoc />
public class RockSelectNumberService : IRockSelectNumberService
{
    private readonly IRepository<RockSelectNumber, int> _repository;
    private readonly IRepository<GeologyField, int> _geologyFieldRepository;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="repository"></param>
    /// <param name="geologyFieldRepository"></param>
    /// <param name="abpSession"></param>
    /// <param name="mapper"></param>
    public RockSelectNumberService(
        IRepository<RockSelectNumber, int> repository, 
        IRepository<GeologyField, int> geologyFieldRepository,
        IAbpSession abpSession,
        IMapper mapper
        )
    {
        _repository = repository;
        _geologyFieldRepository = geologyFieldRepository;
        _abpSession = abpSession;
        _mapper = mapper;
    }

    /// <inheritdoc />
    public async Task<RockSelectNumber> CreateAsync(CreateRockSelectNumberDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var rockSelectNumber = new RockSelectNumber()
        {
            Name = input.Name,
            RockGroupId = input.RockGroupId,
            NumberId = input.NumberId,
            IsActive = input.IsActive,
            TenantId = _abpSession.GetTenantId(),
        };

        await _repository.InsertAsync(rockSelectNumber);
        return rockSelectNumber;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<RockSelectNumberDto>> GetAllAsync(PagedRockSelectNumberResultRequestDto input)
    {
        var query = _repository.GetAllIncluding(x => x.RockGroup, x => x.Number)
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .OrderBy(r => r.Name);

        var totalCount = await query.CountAsync();

        var rockSelectNumber = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        return new PagedResultDto<RockSelectNumberDto>(totalCount,
            _mapper.Map<List<RockSelectNumberDto>>(rockSelectNumber));
    }

    /// <inheritdoc />
    public async Task<RockSelectNumberDto> UpdateAsync(UpdateRockSelectNumberDto input)
    {
        var rockSelectNumber = await ValidateRockSelectNumberEntity(input.Id);

        if (input.IsActive == false)
        {
            var geologyDateUsed =
                await _geologyFieldRepository.FirstOrDefaultAsync(x =>
                    x.Type == FieldType.RockSelectNumber && x.RockSelectNumberId == input.Id);
            if (geologyDateUsed != null)
            {
                throw new UserFriendlyException("Cannot deactivate this record because it is already in use.");
            }
        }

        rockSelectNumber.Name = input.Name ?? rockSelectNumber.Name;
        rockSelectNumber.RockGroupId = input.RockGroupId ?? rockSelectNumber.RockGroupId;
        rockSelectNumber.NumberId = input.NumberId ?? rockSelectNumber.NumberId;
        rockSelectNumber.IsActive = input.IsActive ?? rockSelectNumber.IsActive;

        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task<RockSelectNumberDto> GetAsync(EntityDto<int> input)
    {
        var rockSelectNumber = await ValidateRockSelectNumberEntity(input.Id);
        return _mapper.Map<RockSelectNumberDto>(rockSelectNumber);
    }

    /// <inheritdoc />
    public async Task DeleteAsync(EntityDto<int> input)
    {
        var rockSelectNumber = await ValidateRockSelectNumberEntity(input.Id);
        
        var geologyFieldUsed = await _geologyFieldRepository.FirstOrDefaultAsync(x =>
            x.Type == FieldType.RockGroup && x.RockGroupId == input.Id);
        if (geologyFieldUsed != null)
        {
            throw new UserFriendlyException("Cannot delete this RockSelectNumber because it is being used in GeologyField.");
        }
        
        await _repository.DeleteAsync(rockSelectNumber);
    }

    private async Task<RockSelectNumber> ValidateRockSelectNumberEntity(int id)
    {
        var rockSelectNumber = await _repository.FirstOrDefaultAsync(x => x.Id == id);

        if (rockSelectNumber == null)
        {
            throw new EntityNotFoundException(typeof(RockSelectNumber), id);
        }

        return rockSelectNumber;
    }
}