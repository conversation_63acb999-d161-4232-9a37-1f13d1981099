using Abp.Application.Services.Dto;
using Abp.AutoMapper;

namespace aibase.RockSelectNumbers.Dto;

/// <inheritdoc />
[AutoMap(typeof(RockSelectNumber))]
public class UpdateRockSelectNumberDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public string? Name { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int? RockGroupId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int? NumberId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public bool? IsActive { get; set; }
}