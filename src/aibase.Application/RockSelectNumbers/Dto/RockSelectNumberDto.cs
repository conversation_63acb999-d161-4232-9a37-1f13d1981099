using Abp.Application.Services.Dto;
using Abp.AutoMapper;
using aibase.Numbers.Dto;
using aibase.RockGroups.Dto;

namespace aibase.RockSelectNumbers.Dto;

/// <inheritdoc />
[AutoMap(typeof(RockSelectNumber))]
public class RockSelectNumberDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public RockGroupDto RockGroup { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public NumberDto Number { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool IsActive { get; set; }
}