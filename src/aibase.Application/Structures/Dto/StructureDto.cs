using Abp.Application.Services.Dto;
using AutoMapper;
using aibase.StructureTypes.Dto;
using aibase.RockGroups.Dto;

namespace aibase.Structures.Dto;

/// <inheritdoc />
[AutoMap(typeof(Structure))]
public class StructureDto : EntityDto
{
    /// <summary>
    /// Name of the structure
    /// </summary>
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// Code of the structure
    /// </summary>
    public string Code { get; set; } = string.Empty;
    
    /// <summary>
    /// Description of the structure
    /// </summary>
    public string Description { get; set; } = string.Empty;
    
    /// <summary>
    /// Associated StructureType details
    /// </summary>
    public StructureTypeDto? StructureType { get; set; }
    
    /// <summary>
    /// Type of selector for the structure
    /// </summary>
    public StructureSelector Selector { get; set; }
    
    /// <summary>
    /// Whether the structure has orientation
    /// </summary>
    public bool HasOrientation { get; set; }
    
    /// <summary>
    /// Whether the structure has width
    /// </summary>
    public bool HasWidth { get; set; }
    
    /// <summary>
    /// Whether the structure has condition
    /// </summary>
    public bool HasCondition { get; set; }
    
    /// <summary>
    /// Whether the structure has mineral
    /// </summary>
    public bool HasMineral { get; set; }
    
    /// <summary>
    /// Optional ID of the associated RockGroup
    /// </summary>
    public int? RockGroupId { get; set; }
    
    /// <summary>
    /// Associated RockGroup details
    /// </summary>
    public RockGroupDto? RockGroup { get; set; }
    
    /// <summary>
    /// Text color for the structure
    /// </summary>
    public string TextColor { get; set; } = string.Empty;
    
    /// <summary>
    /// Background color for the structure
    /// </summary>
    public string BackgroundColor { get; set; } = string.Empty;
    
    /// <summary>
    /// Optional icon for the structure
    /// </summary>
    public string? Icon { get; set; }
    
    /// <summary>
    /// Whether the structure is active
    /// </summary>
    public bool IsActive { get; set; }
}