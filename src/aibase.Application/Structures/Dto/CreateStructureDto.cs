using System.ComponentModel.DataAnnotations;

namespace aibase.Structures.Dto;

/// <summary>
/// DTO for creating a new Structure
/// </summary>
public class CreateStructureDto
{
    /// <summary>
    /// Name of the structure
    /// </summary>
    [Required]
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// Code of the structure
    /// </summary>
    [Required]
    public string Code { get; set; } = string.Empty;
    
    /// <summary>
    /// Description of the structure
    /// </summary>
    public string? Description { get; set; }
    
    /// <summary>
    /// ID of the associated StructureType
    /// </summary>
    [Required]
    public int StructureTypeId { get; set; }
    
    /// <summary>
    /// Type of selector for the structure
    /// </summary>
    [Required]
    public StructureSelector Selector { get; set; }
    
    /// <summary>
    /// Whether the structure has orientation
    /// </summary>
    [Required]
    public bool HasOrientation { get; set; }
    
    /// <summary>
    /// Whether the structure has width
    /// </summary>
    [Required]
    public bool HasWidth { get; set; }
    
    /// <summary>
    /// Whether the structure has condition
    /// </summary>
    [Required]
    public bool HasCondition { get; set; }
    
    /// <summary>
    /// Whether the structure has mineral
    /// </summary>
    [Required]
    public bool HasMineral { get; set; }
    
    /// <summary>
    /// Optional ID of the associated RockGroup
    /// </summary>
    public int? RockGroupId { get; set; }
    
    /// <summary>
    /// Text color for the structure
    /// </summary>
    [Required]
    public string TextColor { get; set; } = string.Empty;
    
    /// <summary>
    /// Background color for the structure
    /// </summary>
    [Required]
    public string BackgroundColor { get; set; } = string.Empty;
    
    /// <summary>
    /// Optional icon for the structure
    /// </summary>
    public string? Icon { get; set; }
    
    /// <summary>
    /// Whether the structure is active
    /// </summary>
    [Required]
    public bool IsActive { get; set; }
}