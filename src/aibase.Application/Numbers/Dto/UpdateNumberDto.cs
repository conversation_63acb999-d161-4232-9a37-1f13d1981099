using Abp.Application.Services.Dto;
using Abp.AutoMapper;

namespace aibase.Numbers.Dto;

/// <summary>
/// 
/// </summary>
[AutoMap(typeof(Number))]
public class UpdateNumberDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public ValueType? ValueType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double? UpperLimit { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double? LowerLimit { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool? IsPercent { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double? UpperMajor { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double? UpperMinor { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double? LowerMajor { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double? LowerMinor { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? UnitId { get; set; }
}