using System.ComponentModel.DataAnnotations;

namespace aibase.Numbers.Dto;

/// <summary>
/// 
/// </summary>
public class CreateNumberDto
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public ValueType ValueType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public double UpperLimit { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public double LowerLimit { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public bool IsPercent { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public bool IsActive { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double? UpperMajor { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double? UpperMinor { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double? LowerMajor { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double? LowerMinor { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Range(1, int.MaxValue, ErrorMessage = "UnitId must be greater than 0")]
    public int UnitId { get; set; }
}