using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.GeologyFields;
using aibase.Numbers.Dto;
using aibase.RockSelectNumbers;
using aibase.RockTypeNumbers;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.Numbers.Services;

/// <inheritdoc />
public class NumberService : INumberService
{
    private readonly IRepository<Number, int> _repository;
    private readonly IRepository<GeologyField, int> _geologyFieldRepository;
    private readonly IRepository<RockSelectNumber, int> _rockSelectNumberRepository;
    private readonly IRepository<RockTypeNumber, int> _rockTypeNumberRepository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    public NumberService(
        IRepository<Number, int> repository,
        IRepository<GeologyField, int> geologyFieldRepository,
        IRepository<RockSelectNumber, int> rockSelectNumberRepository,
        IRepository<RockTypeNumber, int> rockTypeNumberRepository,
        IAbpSession abpSession,
        IMapper mapper,
        IUnitOfWorkManager unitOfWorkManager)
    {
        _repository = repository;
        _geologyFieldRepository = geologyFieldRepository;
        _rockSelectNumberRepository = rockSelectNumberRepository;
        _rockTypeNumberRepository = rockTypeNumberRepository;
        _abpSession = abpSession;
        _mapper = mapper;
        _unitOfWorkManager = unitOfWorkManager;
    }

    /// <inheritdoc />
    public async Task<Number> CreateAsync(CreateNumberDto input, bool returnExist = false)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }
        var tenantId = _abpSession.GetTenantId();

        var existingNumber =
            await _repository.FirstOrDefaultAsync(d => d.TenantId == tenantId && d.Name == input.Name);
        if (existingNumber != null)
        {
            if (returnExist)
            {
                return existingNumber;
            }
            
            throw new UserFriendlyException($"The Number with the name {existingNumber.Name} already exists.");
        }

        var number = new Number
        {
            Name = input.Name,
            ValueType = input.ValueType,
            UpperLimit = input.UpperLimit,
            LowerLimit = input.LowerLimit,
            IsPercent = input.IsPercent,
            IsActive = input.IsActive,
            UnitId = input.UnitId,
            UpperMajor = input.UpperMajor,
            UpperMinor = input.UpperMinor,
            LowerMajor = input.LowerMajor,
            LowerMinor = input.LowerMinor,
            TenantId = tenantId,
        };

        await _repository.InsertAsync(number);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        return number;
    }

    /// <inheritdoc />
    public async Task<NumberDto> UpdateAsync(UpdateNumberDto input)
    {
        var number = await ValidateNumberEntity(input.Id);

        if (input.IsActive == false)
        {
            var geologyDateUsed =
                await _geologyFieldRepository.FirstOrDefaultAsync(x =>
                    x.Type == FieldType.Number && x.NumberId == input.Id);
            if (geologyDateUsed != null)
            {
                throw new UserFriendlyException("Cannot deactivate this record because it is already in use.");
            }
        }

        number.Name = input.Name ?? number.Name;
        number.ValueType = input.ValueType ?? number.ValueType;
        number.UpperLimit = input.UpperLimit ?? number.UpperLimit;
        number.LowerLimit = input.LowerLimit ?? number.LowerLimit;
        number.IsPercent = input.IsPercent ?? number.IsPercent;
        number.IsActive = input.IsActive ?? number.IsActive;
        number.UpperMajor = input.UpperMajor;
        number.UpperMinor = input.UpperMinor;
        number.LowerMajor = input.LowerMajor;
        number.LowerMinor = input.LowerMinor;
        number.UnitId = input.UnitId ?? number.UnitId;

        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<NumberDto>> GetAllAsync(PagedNumberResultRequestDto input)
    {
        var query = _repository.GetAllIncluding(x => x.Unit)
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
            .OrderBy(r => r.Name);

        var totalCount = await query.CountAsync();

        var numbers = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        return new PagedResultDto<NumberDto>(totalCount, _mapper.Map<List<NumberDto>>(numbers));
    }

    /// <inheritdoc />
    public async Task<NumberDto> GetAsync(EntityDto<int> input)
    {
        var number = await ValidateNumberEntity(input.Id);
        return _mapper.Map<NumberDto>(number);
    }

    /// <inheritdoc />
    public async Task DeleteAsync(EntityDto<int> input)
    {
        var number = await ValidateNumberEntity(input.Id);

        // Check if number is used in GeologyField
        var geologyFieldUsed = await _geologyFieldRepository.FirstOrDefaultAsync(x =>
            x.Type == FieldType.Number && x.NumberId == input.Id);
        if (geologyFieldUsed != null)
        {
            throw new UserFriendlyException("Cannot delete this Number because it is being used in GeologyField.");
        }

        // Check if number is used in RockSelectNumber
        var rockSelectNumberUsed = await _rockSelectNumberRepository.FirstOrDefaultAsync(x =>
            x.NumberId == input.Id);
        if (rockSelectNumberUsed != null)
        {
            throw new UserFriendlyException("Cannot delete this Number because it is being used in RockSelectNumber.");
        }

        // Check if number is used in RockTypeNumber
        var rockTypeNumberUsed = await _rockTypeNumberRepository.FirstOrDefaultAsync(x =>
            x.NumberId == input.Id);
        if (rockTypeNumberUsed != null)
        {
            throw new UserFriendlyException("Cannot delete this number because it is being used in RockTypeNumber.");
        }

        await _repository.DeleteAsync(number);
    }

    private async Task<Number> ValidateNumberEntity(int id)
    {
        var number = await _repository.GetAllIncluding(x => x.Unit)
            .FirstOrDefaultAsync(x => x.Id == id);

        if (number == null)
        {
            throw new EntityNotFoundException(typeof(Number), id);
        }

        return number;
    }
}