using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.Authorization.Users;
using aibase.GeologySuites.Dto;
using aibase.GeotechSuites.Dto;
using aibase.WorkRoleGeotechSuites;
using aibase.WorkRoles.Dto;
using aibase.WorkRoleSuites;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.WorkRoles.Services;

/// <inheritdoc />
public class WorkRoleService : IWorkRoleService
{
    private readonly IRepository<WorkRole> _repository;
    private readonly IRepository<WorkRoleSuite> _workRoleSuiteRepository;
    private readonly IRepository<WorkRoleGeotechSuite, int> _workRoleGeotechSuiteRepository;
    private readonly IRepository<User, long> _userRepository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    public WorkRoleService(
        IRepository<WorkRole> repository,
        IRepository<WorkRoleSuite> workRoleSuiteRepository,
        IUnitOfWorkManager unitOfWorkManager,
        IAbpSession abpSession,
        IMapper mapper, IRepository<WorkRoleGeotechSuite, int> workRoleGeotechSuiteRepository, IRepository<User, long> userRepository)
    {
        _repository = repository;
        _abpSession = abpSession;
        _mapper = mapper;
        _workRoleGeotechSuiteRepository = workRoleGeotechSuiteRepository;
        _userRepository = userRepository;
        _workRoleSuiteRepository = workRoleSuiteRepository;
        _unitOfWorkManager = unitOfWorkManager;
    }

    /// <inheritdoc />
    public async Task<WorkRole> CreateAsync(CreateWorkRoleDto input, bool returnExist = false)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }
        var tenantId = _abpSession.GetTenantId();

        var existingWorkRole =
            await _repository.FirstOrDefaultAsync(d => d.TenantId == tenantId && d.Name == input.Name);
        if (existingWorkRole != null)
        {
            if (returnExist)
            {
                return existingWorkRole;
            }
            
            throw new UserFriendlyException($"The Work Role with the name {existingWorkRole.Name} already exists.");
        }
        
        var workRole = new WorkRole
        {
            Name = input.Name,
            Description = input.Description,
            IsActive = input.IsActive,
            TenantId = tenantId,
        };
        await _repository.InsertAsync(workRole);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        if (input.GeologySuiteIds != null) await CreateWorkRoleSuitesAsync(workRole.Id, input.GeologySuiteIds);
        
        if (input.GeotechSuiteIds is { Count: > 0 })
        {
            var assignProjectGeotechSuiteDto = new AssignWorkRoleGeotechSuiteDto
            {
                WorkRoleId = workRole.Id,
                GeotechSuiteIds = input.GeotechSuiteIds
            };
            await AssignWorkRoleGeotechSuite(assignProjectGeotechSuiteDto);
        }

        return workRole;
    }

    /// <inheritdoc />
    public async Task<WorkRoleDto> UpdateAsync(UpdateWorkRoleDto input)
    {
        var workRole = await ValidateWorkRoleEntity(input.Id);

        workRole.Name = input.Name ?? workRole.Name;
        workRole.Description = input.Description ?? workRole.Description;
        workRole.IsActive = input.IsActive ?? workRole.IsActive;

        await _workRoleSuiteRepository.DeleteAsync(x => x.WorkRoleId == workRole.Id);
        await CreateWorkRoleSuitesAsync(workRole.Id, input.GeologySuiteIds);
        
        if (input.GeotechSuiteIds != null)
        {
            var assignProjectGeotechSuiteDto = new AssignWorkRoleGeotechSuiteDto
            {
                WorkRoleId = workRole.Id,
                GeotechSuiteIds = input.GeotechSuiteIds
            };
            await AssignWorkRoleGeotechSuite(assignProjectGeotechSuiteDto);
        }

        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<WorkRoleDto>> GetAllAsync(PagedWorkRoleResultRequestDto input)
    {
        var query = _repository.GetAll()
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .OrderBy(r => r.Name);

        var totalCount = await query.CountAsync();

        var workRoles = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        return new PagedResultDto<WorkRoleDto>(totalCount, _mapper.Map<List<WorkRoleDto>>(workRoles));
    }

    /// <inheritdoc />
    public async Task<WorkRoleDto> GetAsync(EntityDto<int> input)
    {
        var workRole = await _repository.GetAll()
            .AsNoTracking()
            .Include(x => x.WorkRoleSuites)
            .ThenInclude(x => x.GeologySuite)
            .Include(x => x.WorkRoleGeotechSuites)
            .ThenInclude(x => x.GeotechSuite)
            .FirstOrDefaultAsync(x => x.Id == input.Id);
        if (workRole == null)
        {
            throw new EntityNotFoundException(nameof(WorkRole));
        }
        
        var workRoleDto = _mapper.Map<WorkRoleDto>(workRole);
        workRoleDto.GeologySuites = workRole.WorkRoleSuites
            .Select(x => _mapper.Map<GeologySuiteDto>(x.GeologySuite)).ToList();
        workRoleDto.GeotechSuites = workRole.WorkRoleGeotechSuites
            .Select(x => _mapper.Map<GeotechSuiteDto>(x.GeotechSuite)).ToList();
        return workRoleDto;
    }

    private async Task<WorkRole> ValidateWorkRoleEntity(int id)
    {
        var workRole = await _repository.FirstOrDefaultAsync(x => x.Id == id);
        if (workRole == null)
        {
            throw new EntityNotFoundException(typeof(WorkRole), id);
        }

        return workRole;
    }

    private async Task CreateWorkRoleSuitesAsync(int workRoleId, List<int> geologySuiteIds)
    {
        foreach (var workRoleSuite in geologySuiteIds.Select(geologySuiteId => new WorkRoleSuite
                 {
                     WorkRoleId = workRoleId,
                     GeologySuiteId = geologySuiteId,
                 }))
        {
            await _workRoleSuiteRepository.InsertAsync(workRoleSuite);
        }
    }

    /// <inheritdoc />
    public async Task AssignWorkRoleGeotechSuite(AssignWorkRoleGeotechSuiteDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var geotechSuites = await _workRoleGeotechSuiteRepository.GetAllListAsync(x =>
            x.WorkRoleId == input.WorkRoleId);

        var geotechSuiteIds = geotechSuites.Select(x => x.GeotechSuiteId).ToList();

        var geotechSuitesToAdd = input.GeotechSuiteIds.Except(geotechSuiteIds).ToList();
        var geotechSuitesToDelete = geotechSuiteIds.Except(input.GeotechSuiteIds).ToList();

        foreach (var workRoleGeotechSuite in geotechSuitesToAdd.Select(geotechSuiteId =>
                     new WorkRoleGeotechSuite
                     {
                         WorkRoleId = input.WorkRoleId,
                         GeotechSuiteId = geotechSuiteId,
                     }))
        {
            await _workRoleGeotechSuiteRepository.InsertAsync(workRoleGeotechSuite);
        }

        foreach (var geotechSuiteId in geotechSuitesToDelete)
        {
            await _workRoleGeotechSuiteRepository.DeleteAsync(x => x.GeotechSuiteId == geotechSuiteId && x.WorkRoleId == input.WorkRoleId);
        }
    }

    /// <inheritdoc />
    public async Task DeleteAsync(EntityDto<int> input)
    {
        var users = await _userRepository.FirstOrDefaultAsync(u => u.WorkRoleId == input.Id);

        if (users != null)
        {
            throw new UserFriendlyException("Cannot delete this work role because it is being referenced by one or more users.");
        }
        
        await _repository.DeleteAsync(input.Id);
    }
}