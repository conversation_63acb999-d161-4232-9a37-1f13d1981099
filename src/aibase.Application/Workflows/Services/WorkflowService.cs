using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.WorkflowEntity;
using aibase.Workflows.Dto;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.Workflows.Services;

/// <inheritdoc />
public class WorkflowService : IWorkflowService
{
    private readonly IRepository<Workflow, int> _repository;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="repository"></param>
    /// <param name="abpSession"></param>
    /// <param name="mapper"></param>
    public WorkflowService(IRepository<Workflow, int> repository, IAbpSession abpSession, IMapper mapper)
    {
        _repository = repository;
        _abpSession = abpSession;
        _mapper = mapper;
    }

    /// <inheritdoc />
    public async Task<Workflow> CreateAsync(CreateWorkflowDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var workflow = new Workflow()
        {
            Name = input.Name,
            ButtonName = input.ButtonName,
            BackgroundColor = input.BackgroundColor,
            Color = input.Color,
            IsActive = input.IsActive,
            TenantId = _abpSession.GetTenantId()
        };

        await _repository.InsertAsync(workflow);
        return workflow;
    }

    /// <inheritdoc />
    public async Task<WorkflowDto> UpdateAsync(WorkflowDto input)
    {
        var workflow = await ValidateWorkflowEntity(input.Id);
        
        workflow.Name = input.Name;
        workflow.ButtonName = input.ButtonName;
        workflow.BackgroundColor = input.BackgroundColor;
        workflow.Color = input.Color;
        workflow.IsActive = input.IsActive;
        
        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<WorkflowDto>> GetAllAsync(PagedWorkflowResultRequestDto input)
    {
        var query = _repository.GetAll()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive);

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.SortField) && !string.IsNullOrWhiteSpace(input.SortOrder))
        {
            query = query.ApplySorting(input.SortField, input.SortOrder, r => r.CreationTime);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime); // Default sorting
        }
            
        var totalCount = await query.CountAsync();

        var workflows = await query.Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

       return new PagedResultDto<WorkflowDto>(totalCount, _mapper.Map<List<WorkflowDto>>(workflows));
    }

    /// <inheritdoc />
    public async Task<WorkflowDto> GetAsync(EntityDto<int> input)
    {
        var workflow = await ValidateWorkflowEntity(input.Id);
        return _mapper.Map<WorkflowDto>(workflow);
    }
    
    private async Task<Workflow> ValidateWorkflowEntity(int id)
    {
        var unit = await _repository.FirstOrDefaultAsync(x => x.Id == id);
        if (unit == null)
        {
            throw new EntityNotFoundException(typeof(Workflow), id);
        }
        
        return unit;
    }
}