using System;
using Newtonsoft.Json;

namespace aibase.Images.Services;

/// <summary>
/// 
/// </summary>
public static class ImageCommon
{
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="idsJson"></param>
    /// <param name="paramName"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentException"></exception>
    public static int[]? DeserializeIds(string? idsJson, string paramName)
    {
        if (string.IsNullOrEmpty(idsJson)) return null;
        try
        {
            // Try to deserialize as array first
            try
            {
                return JsonConvert.DeserializeObject<int[]>(idsJson);
            }
            catch
            {
                // If array deserialization fails, try as single integer
                var singleId = JsonConvert.DeserializeObject<int>(idsJson);
                return new[] { singleId };
            }
        }
        catch (JsonException ex)
        {
            throw new ArgumentException($"Invalid {paramName} format", paramName, ex);
        }
    }
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="namesJson"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentException"></exception>
    public static string[] DeserializeNames(string? namesJson)
    {
        if (string.IsNullOrEmpty(namesJson)) return [];
        try
        {
            return JsonConvert.DeserializeObject<string[]>(namesJson) ?? [];
        }
        catch (JsonException)
        {
            return [];
        }
    }
}