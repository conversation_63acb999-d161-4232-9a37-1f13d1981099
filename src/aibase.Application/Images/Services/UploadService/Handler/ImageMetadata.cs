using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using aibase.Images.Dto;

namespace aibase.Images.Services.UploadService.Handler;

/// <summary>
/// 
/// </summary>
public class ImageMetadata
{
    /// <summary>
    /// 
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public string HoleName { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public double DepthFrom { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double DepthTo { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string Extension { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="originalFileName"></param>
    /// <returns></returns>
    public static ImageMetadata FromFileName(string originalFileName)
    {
        var fileName = Path.GetFileNameWithoutExtension(originalFileName);
        var extension = Path.GetExtension(originalFileName);
        var parts = fileName.Split("_");

        return new ImageMetadata
        {
            HoleName = parts.Length > 0 ? parts[0] : fileName,
            DepthFrom = parts.Length > 1 ? double.Parse(parts[1]) : 0,
            DepthTo = parts.Length > 2 ? double.Parse(parts[2]) : 0,
            Extension = extension,
            FileName = parts.Length >= 3 ? $"{parts[0]}_{parts[1]}_{parts[2]}_{Guid.NewGuid()}" : fileName
        };
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="points"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentException"></exception>
    public static CoordinateV2Dto ConvertToCoordinateV2Dto(List<List<double>> points)
    {
        if (points is not { Count: 4 })
            throw new ArgumentException("Exactly 4 coordinates are required.");

        var xValues = points.Select(p => p[0]).ToList();
        var yValues = points.Select(p => p[1]).ToList();

        var minX = xValues.Min();
        var maxX = xValues.Max();
        var minY = yValues.Min();
        var maxY = yValues.Max();

        return new CoordinateV2Dto
        {
            X = minX,
            Y = minY,
            Width = maxX - minX,
            Height = maxY - minY
        };
    }
}