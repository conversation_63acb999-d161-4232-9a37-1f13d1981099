using System.Threading.Tasks;
using Abp.Dependency;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using aibase.DrillHoleEntity;
using aibase.Images.Dto;
using aibase.ProjectEntity;
using aibase.Prospects;

namespace aibase.Images.Services.UploadService.Handler;

/// <inheritdoc />
public class ProjectHierarchyManager : ITransientDependency
{
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IRepository<Project, int> _projectRepository;
    private readonly IRepository<Prospect, int> _prospectRepository;
    private readonly IRepository<DrillHole, int> _drillHoleRepository;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="projectRepository"></param>
    /// <param name="prospectRepository"></param>
    /// <param name="drillHoleRepository"></param>
    /// <param name="unitOfWorkManager"></param>
    public ProjectHierarchyManager(
        IRepository<Project, int> projectRepository,
        IRepository<Prospect, int> prospectRepository,
        IRepository<DrillHole, int> drillHoleRepository,
        IUnitOfWorkManager unitOfWorkManager)
    {
        _projectRepository = projectRepository;
        _prospectRepository = prospectRepository;
        _drillHoleRepository = drillHoleRepository;
        _unitOfWorkManager = unitOfWorkManager;
    }
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    public async Task<ProjectHierarchy> GetOrCreateHierarchyAsync(CreateImageDto input, int tenantId)
    {
        if (input.IsUnnamed == true || input.IsAiNaming == true)
        {
            return await HandleUnnamedHierarchyAsync(input, tenantId);
        }

        return await GetExistingHierarchyAsync(input);
    }

    private async Task<ProjectHierarchy> HandleUnnamedHierarchyAsync(CreateImageDto input, int tenantId)
    {
        if (input.ProjectId == null)
        {
            return await CreateFullUnnamedHierarchyAsync(tenantId);
        }

        var project = await _projectRepository.GetAsync((int)input.ProjectId);
        var prospect = input.ProspectId == null 
            ? await GetOrCreateUnnamedProspectAsync(project.Id, tenantId)
            : await _prospectRepository.GetAsync((int)input.ProspectId);
        
        var drillHole = await GetOrCreateUnnamedDrillHoleAsync(project.Id, prospect.Id, tenantId);

        return new ProjectHierarchy(project, prospect, drillHole);
    }
    
    private async Task<ProjectHierarchy> CreateFullUnnamedHierarchyAsync(int tenantId)
    {
        var project = await GetOrCreateUnnamedProjectAsync(tenantId);
        var prospect = await GetOrCreateUnnamedProspectAsync(project.Id, tenantId);
        var drillHole = await GetOrCreateUnnamedDrillHoleAsync(project.Id, prospect.Id, tenantId);

        return new ProjectHierarchy(project, prospect, drillHole);
    }
    
    private async Task<Project> GetOrCreateUnnamedProjectAsync(int tenantId)
    {
        var unnamedProject = await _projectRepository.FirstOrDefaultAsync(p =>
            p.Name == ImageConstSettings.UnnamedName && p.TenantId == tenantId) ?? new Project
        {
            Name =  ImageConstSettings.UnnamedName,
            Code = ImageConstSettings.UnnamedCode,
            Description = ImageConstSettings.DefaultUnnamedProjectDes,
            BackgroundColor = ImageConstSettings.DefaultBackgroundColor,
            TextColor = ImageConstSettings.DefaultTextColor,
            BoundingBoxIds = string.Empty,
            BoundingRowsIds = string.Empty,
            TenantId = tenantId
        };

        if (unnamedProject.Id == 0)
        {
            await _projectRepository.InsertAsync(unnamedProject);
            await _unitOfWorkManager.Current.SaveChangesAsync();
        }

        return unnamedProject;
    }
    
    private async Task<Prospect> GetOrCreateUnnamedProspectAsync(int projectId, int tenantId)
    {
        var unnamedProspect = await _prospectRepository.FirstOrDefaultAsync(p =>
            p.Name == ImageConstSettings.UnnamedName && p.ProjectId == projectId && p.TenantId == tenantId) ?? new Prospect
        {
            Name =  ImageConstSettings.UnnamedName,
            Description = ImageConstSettings.DefaultUnnamedProspectDes,
            BackgroundColor = ImageConstSettings.DefaultBackgroundColor,
            TextColor =  ImageConstSettings.DefaultTextColor,
            ProjectId = projectId,
            TenantId = tenantId
        };

        if (unnamedProspect.Id == 0)
        {
            await _prospectRepository.InsertAsync(unnamedProspect);
            await _unitOfWorkManager.Current.SaveChangesAsync();
        }

        return unnamedProspect;
    }
    
    private async Task<DrillHole> GetOrCreateUnnamedDrillHoleAsync(int projectId, int prospectId, int tenantId)
    {
        var unnamedDrillHole = await _drillHoleRepository.FirstOrDefaultAsync(d =>
            d.Name ==  ImageConstSettings.UnnamedName && 
            d.ProjectId == projectId && 
            d.ProspectId == prospectId && 
            d.TenantId == tenantId) ?? new DrillHole
        {
            Name = ImageConstSettings.UnnamedName,
            DrillHoleStatus = DrillHoleStatus.NotStarted,
            MaxDepth = 0,
            ProjectId = projectId,
            ProspectId = prospectId,
            TenantId = tenantId
        };

        if (unnamedDrillHole.Id == 0)
        {
            await _drillHoleRepository.InsertAsync(unnamedDrillHole);
            await _unitOfWorkManager.Current.SaveChangesAsync();
        }

        return unnamedDrillHole;
    }

    private async Task<ProjectHierarchy> GetExistingHierarchyAsync(CreateImageDto input)
    {
        var project = await _projectRepository.GetAsync((int)input.ProjectId);
        var prospect = await _prospectRepository.GetAsync((int)input.ProspectId);
        var drillHole = await _drillHoleRepository.GetAsync((int)input.HoleId);

        return new ProjectHierarchy(project, prospect, drillHole);
    }
}

/// <summary>
/// 
/// </summary>
/// <param name="Project"></param>
/// <param name="Prospect"></param>
/// <param name="DrillHole"></param>
public record ProjectHierarchy(Project Project, Prospect Prospect, DrillHole DrillHole);