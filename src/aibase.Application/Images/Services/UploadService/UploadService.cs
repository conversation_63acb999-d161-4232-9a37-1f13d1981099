using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.Authorization.Users;
using aibase.DrillHoleEntity;
using aibase.DrillHoles.Services.DrillHoleService;
using aibase.FileEntity;
using aibase.ImageCrops;
using aibase.ImageEntity;
using aibase.Images.Dto;
using aibase.Images.Services.UploadService.Handler;
using aibase.ImageSubtypes;
using aibase.ImageTypes;
using aibase.Jobs.Services.JobService;
using aibase.Polygons;
using aibase.ProjectEntity;
using Hangfire;
using Newtonsoft.Json;

namespace aibase.Images.Services.UploadService;

/// <inheritdoc />
public class UploadService : IUploadService
{
    private readonly IAbpSession _abpSession;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly ImageUploadValidator _validator;
    private readonly ImageFileHandler _fileHandler;
    private readonly ProjectHierarchyManager _hierarchyManager;
    private readonly IRepository<Image, int> _imageRepository;
    private readonly IRepository<File, int> _fileRepository;
    private readonly IRepository<Polygon, int> _polygonRepository;
    private readonly IRepository<DrillHole, int> _drillholeRepository;
    private readonly IRepository<ImageCrop, int> _imageCropRepository;
    private readonly IRepository<ImageType, int> _imageTypeRepository;
    private readonly IRepository<ImageSubtype, int> _imageSubtypeRepository;
    private readonly IDrillHoleService _drillHoleService;
    private readonly UserManager _userManager;

    /// <summary>
    /// 
    /// </summary>
    public UploadService(
        IAbpSession abpSession,
        ImageUploadValidator validator,
        ImageFileHandler fileHandler,
        ProjectHierarchyManager hierarchyManager,
        IRepository<Image, int> imageRepository,
        IRepository<File, int> fileRepository,
        IRepository<Polygon, int> polygonRepository,
        IRepository<DrillHole, int> drillholeRepository,
        IRepository<ImageCrop, int> imageCropRepository,
        IRepository<ImageType, int> imageTypeRepository,
        IRepository<ImageSubtype, int> imageSubtypeRepository,
        IUnitOfWorkManager unitOfWorkManager,
        IDrillHoleService drillHoleService,
        UserManager userManager
    )
    {
        _abpSession = abpSession;
        _validator = validator;
        _fileHandler = fileHandler;
        _hierarchyManager = hierarchyManager;
        _imageRepository = imageRepository;
        _fileRepository = fileRepository;
        _polygonRepository = polygonRepository;
        _unitOfWorkManager = unitOfWorkManager;
        _drillholeRepository = drillholeRepository;
        _imageCropRepository = imageCropRepository;
        _imageTypeRepository = imageTypeRepository;
        _imageSubtypeRepository = imageSubtypeRepository;
        _drillHoleService = drillHoleService;
        _userManager = userManager;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="UserFriendlyException"></exception>
    public async Task<Image> UploadImage(CreateImageDto input)
    {
        await _validator.ValidateUploadRequest(input, _abpSession.TenantId);


        // Always get project and prospect from drillhole, ignoring provided values
        if (input.HoleId.HasValue)
        {
            var drillhole = await _drillholeRepository.FirstOrDefaultAsync(x =>
                x.Id == input.HoleId && x.TenantId == _abpSession.GetTenantId());
            if (drillhole != null)
            {
                input.ProjectId = drillhole.ProjectId;
                input.ProspectId = drillhole.ProspectId;
            }
        }

        CropCoordinate? coordinateBox = null;
        if (!string.IsNullOrWhiteSpace(input.CoordinateBox))
        {
            try
            {
                var coordinate = JsonConvert.DeserializeObject<List<List<double>>>(input.CoordinateBox) ?? [];
                var box = ImageMetadata.ConvertToCoordinateV2Dto(coordinate);
                coordinateBox = new CropCoordinate
                {
                    X = box.X,
                    Y = box.Y,
                    Width = box.Width,
                    Height = box.Height,
                    Id = Guid.NewGuid().ToString(),
                    Type = ImageConstSettings.ImageCropBox
                };
            }
            catch (Exception)
            {
                // ignored
            }
        }

        var tempFilePath = string.Empty;
        try
        {
            // Get project hierarchy
            var hierarchy = await _hierarchyManager.GetOrCreateHierarchyAsync(input, _abpSession.GetTenantId());

            // Handle file upload
            tempFilePath = await ImageFileHandler.SaveTemporaryFile(input.Image);
            var metadata = ImageMetadata.FromFileName(input.Image.FileName);


            var imageChecker =
                await _imageRepository.FirstOrDefaultAsync(x =>
                    x.ProjectId == input.ProjectId &&
                    x.ProspectId == input.ProspectId &&
                    x.DrillHoleId == input.HoleId &&
                    x.ImageCategory == input.ImageCategory &&
                    x.ImageTypeId == input.ImageTypeId &&
                    x.ImageSubtypeId == input.ImageSubtypeId &&
                    Math.Abs(x.DepthFrom - metadata.DepthFrom) < 0.01 &&
                    Math.Abs(x.DepthTo - metadata.DepthTo) < 0.01
                );
            if (imageChecker != null)
            {
                // First delete associated image crops to prevent FK constraint violation
                var crops = await _imageCropRepository.GetAllListAsync(c => c.ImageId == imageChecker.Id);
                foreach (var crop in crops)
                {
                    await _imageCropRepository.DeleteAsync(crop);
                }

                // Then delete associated files to prevent FK constraint violation
                var files = await _fileRepository.GetAllListAsync(f => f.ImageId == imageChecker.Id);
                foreach (var file in files)
                {
                    await _fileRepository.DeleteAsync(file);
                }

                // Finally delete the image
                await _imageRepository.DeleteAsync(imageChecker);
                await _unitOfWorkManager.Current.SaveChangesAsync();
            }

            var uploadedImage = await _fileHandler.UploadToStorage(input.Image, metadata.FileName);

            // Create entities
            var imageEntity = await CreateImageEntity(input, hierarchy, metadata, coordinateBox);
            await CreateFileEntity(uploadedImage, imageEntity.Id);

            // Upload Image Crop Box
            if (input.ImageBox != null)
            {
                var uploadedImageBox = await _fileHandler.UploadToStorage(input.ImageBox,
                    $"cropped_{imageEntity.Id}_{metadata.DepthFrom}_{metadata.DepthTo}");


                var imageCropBox = new ImageCrop
                {
                    UrlCroppedImage = uploadedImageBox.Url,
                    MediumSize = uploadedImageBox.Url,
                    Coordinate = coordinateBox != null ? JsonConvert.SerializeObject(coordinateBox) : string.Empty,
                    Type = ImageConstSettings.ImageCropBox,
                    DepthFrom = metadata.DepthFrom,
                    DepthTo = metadata.DepthTo,
                    ImageId = imageEntity.Id
                };
                await _imageCropRepository.InsertAsync(imageCropBox);
            }

            // Queue background processing
            BackgroundJob.Enqueue<JobService>(job =>
                job.ProcessResizedImage(tempFilePath, metadata.FileName, metadata.Extension, imageEntity.Id,
                    input.WorkflowId));

            await _drillHoleService.UpdateTotalImageByDrillholeAsync(imageEntity.DrillHoleId);
            return imageEntity;
        }
        catch (Exception ex)
        {
            ImageFileHandler.CleanupTemporaryFile(tempFilePath);
            throw new UserFriendlyException(ex.Message);
        }
    }

    private async Task<Image> CreateImageEntity(
        CreateImageDto input,
        ProjectHierarchy hierarchy,
        ImageMetadata metadata,
        CropCoordinate? coordinateBox)
    {
        var currentUser = await _userManager.GetUserByIdAsync(_abpSession.GetUserId());

        var boundingBox = await GetBoundingBox(input.BoundingBoxId, hierarchy.Project);
        var boundingRows = await GetBoundingRows(input.BoundingRowsId, hierarchy.Project);

        var imageCategory = input.ImageCategory != 0
            ? input.ImageCategory
            : input.ImageClass != null
                ? (ImageCategoryNew)input.ImageClass
                : ImageCategoryNew.Drilling;

        // Determine ImageTypeId based on input.Type
        var imageTypeId = input.ImageTypeId;
        if (input.ImageTypeId == null)
        {
            imageTypeId = await GetImageTypeIdByType(input.Type, hierarchy.DrillHole.TenantId);
        }

        // Determine ImageSubtypeId based on input.StandardType
        var imageSubtypeId = input.ImageSubtypeId;
        if (input.ImageSubtypeId == null && input.StandardType.HasValue)
        {
            imageSubtypeId =
                await GetImageSubtypeIdByStandardType(input.StandardType.Value, hierarchy.DrillHole.TenantId);
        }

        var imageEntity = new Image
        {
            DrillHoleId = hierarchy.DrillHole.Id,
            ProspectId = hierarchy.Prospect.Id,
            ProjectId = hierarchy.Project.Id,
            ImageClass = input.ImageClass ?? ImageClass.General,
            Type = input.Type,
            StandardType = input.StandardType,
            ImageCategory = imageCategory,
            ImageTypeId = imageTypeId,
            ImageSubtypeId = imageSubtypeId,
            DepthFrom = input.DepthFrom ?? metadata.DepthFrom,
            DepthTo = input.DepthTo ?? metadata.DepthTo,
            BoundingBox = coordinateBox != null
                ? JsonConvert.SerializeObject(new List<CropCoordinate> { coordinateBox })
                : boundingBox?.Coordinates ?? string.Empty,
            BoundingRows = boundingRows?.Coordinates ?? string.Empty,
            ImageStatus = ImageStatus.NotStarted,
            CreatedByUser = currentUser.UserName,
            CreatedByName = $"{currentUser.FirstName} {currentUser.LastName}"
        };

        await _imageRepository.InsertAsync(imageEntity);
        await _unitOfWorkManager.Current.SaveChangesAsync();
        return imageEntity;
    }

    private async Task CreateFileEntity(ImageFileHandler.UploadedImageInfo uploadedImage, int imageId)
    {
        var fileEntity = new File
        {
            FileName = uploadedImage.FileName,
            Url = uploadedImage.Url,
            Size = uploadedImage.Size,
            Width = uploadedImage.Width,
            Height = uploadedImage.Height,
            ImageId = imageId
        };

        await _fileRepository.InsertAsync(fileEntity);
    }

    private async Task<Polygon?> GetBoundingBox(int? boundingBoxId, Project project)
    {
        var id = boundingBoxId ?? (project.BoundingBoxIds != string.Empty
            ? int.Parse(project.BoundingBoxIds.Split(',').First())
            : 0);
        return id <= 0 ? null : await _polygonRepository.FirstOrDefaultAsync(id);
    }

    private async Task<Polygon?> GetBoundingRows(int? boundingRowsId, Project project)
    {
        var id = boundingRowsId ?? (project.BoundingRowsIds != string.Empty
            ? int.Parse(project.BoundingRowsIds.Split(',').First())
            : 0);
        return id <= 0 ? null : await _polygonRepository.FirstOrDefaultAsync(id);
    }

    /// <summary>
    /// Gets ImageType ID based on input.Type value
    /// </summary>
    /// <param name="type">The type value from input</param>
    /// <param name="tenantId">The tenant ID</param>
    /// <returns>ImageType ID or null if not found</returns>
    private async Task<int?> GetImageTypeIdByType(ImageCategory type, int tenantId)
    {
        var imageTypeName = type switch
        {
            (ImageCategory)1 => "Standard",
            (ImageCategory)2 => "Hyperspectral",
            (ImageCategory)3 => "Optical",
            (ImageCategory)5 => "Rig",
            _ => null
        };

        if (string.IsNullOrEmpty(imageTypeName))
        {
            return null;
        }


        var imageType = await _imageTypeRepository.FirstOrDefaultAsync(x =>
            x.TenantId == tenantId && x.Name == imageTypeName);

        if (imageType == null)
        {
            var errorMessage = $"ImageType with Name='{imageTypeName}' and TenantId={tenantId} not found";
            throw new UserFriendlyException(errorMessage);
        }

        return imageType.Id;
    }

    /// <summary>
    /// Gets ImageSubtype ID based on input.StandardType value
    /// </summary>
    /// <param name="standardType">The standard type value from input</param>
    /// <param name="tenantId">The tenant ID</param>
    /// <returns>ImageSubtype ID or null if not found</returns>
    private async Task<int?> GetImageSubtypeIdByStandardType(StandardType standardType, int tenantId)
    {
        var imageSubtypeName = standardType switch
        {
            (StandardType)1 => "Dry",
            (StandardType)2 => "Wet",
            _ => null
        };

        if (string.IsNullOrEmpty(imageSubtypeName))
        {
            return null;
        }


        var imageSubtype = await _imageSubtypeRepository.FirstOrDefaultAsync(x =>
            x.TenantId == tenantId && x.Name == imageSubtypeName);

        if (imageSubtype == null)
        {
            var errorMessage = $"ImageSubtype with Name='{imageSubtypeName}' and TenantId={tenantId} not found";
            throw new UserFriendlyException(errorMessage);
        }

        return imageSubtype.Id;
    }
}