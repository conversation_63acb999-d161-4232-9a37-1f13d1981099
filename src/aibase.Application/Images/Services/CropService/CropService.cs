﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.UI;
using aibase.ImageCrops;
using aibase.Images.Dto;
using aibase.Images.Services.ResizeService;
using aibase.Images.Services.UploadService.Handler;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;
using File = aibase.FileEntity.File;

namespace aibase.Images.Services.CropService
{
    /// <inheritdoc />
    public class CropService(
        IRepository<ImageEntity.Image, int> repository,
        IUnitOfWorkManager unitOfWorkManager,
        IRepository<ImageCrop, int> cropRepository,
        IResizeService resizeService)
        : ICropService
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="cropCoordinates"></param>
        /// <param name="isSave"></param>
        /// <param name="assignStep"></param>
        /// <returns></returns>
        public async Task<List<ImageCrop>> CropImg(int id, CropCoordinate[] cropCoordinates, bool isSave = true,
            bool assignStep = false)
        {
            try
            {
                // Get the image entity with its files
                var image = await repository.GetAllIncluding(x => x.Files)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == id);
                if (image == null)
                {
                    throw new EntityNotFoundException(typeof(Image), id);
                }

                var fileImg = image.Files.OrderBy(x => x.Size == ImageConstSettings.ImageFullSize ? 0 : 1)
                    .FirstOrDefault();
                if (fileImg == null)
                {
                    throw new EntityNotFoundException(typeof(File), id);
                }

                // Deserialize and prepare coordinates
                var boundingBoxDeserialize =
                    JsonConvert.DeserializeObject<CropCoordinate[]>(image.BoundingBox ?? "[]") ?? [];
                var boundingRowDeserialize =
                    JsonConvert.DeserializeObject<CropCoordinate[]>(image.BoundingRows ?? "[]") ?? [];
                var boundingBox = boundingBoxDeserialize.OrderBy(c => c.Y).ToArray();
                var boundingRow = boundingRowDeserialize.OrderBy(c => c.Y).ToArray();

                var polygonImage = boundingBox.Concat(boundingRow).OrderBy(c => c.Y).ToArray();
                var coordinates = assignStep ? cropCoordinates : polygonImage;

                var imageUrl = fileImg.Url;
                var imgDepthFrom = image.DepthFrom;
                var imgDepthTo = image.DepthTo;

                // Delete existing crops if saving
                if (isSave)
                {
                    await cropRepository.DeleteAsync(c => c.ImageId == id);
                    await unitOfWorkManager.Current.SaveChangesAsync();
                }

                var imageCrops = new List<ImageCrop>();

                // Load image only once
                using var img = await LoadImageAsync(imageUrl);
                coordinates = FilterAndAdjustCoordinates(coordinates, img.Width, img.Height);

                var countRow = coordinates.Count(c =>
                    c.Type is ImageConstSettings.ImageCropRow or ImageConstSettings.ImageCropRowLower);
                var delta = countRow > 0 ? (imgDepthTo - imgDepthFrom) / countRow : 0;

                // Process coordinates sequentially
                var currentDepthFrom = imgDepthFrom;
                foreach (var cropCoordinate in coordinates)
                {
                    if (!(cropCoordinate.Width > 0) || !(cropCoordinate.Height > 0)) continue;

                    var imageCrop = new ImageCrop
                    {
                        Type = cropCoordinate.Type,
                        Coordinate = JsonConvert.SerializeObject(cropCoordinate),
                        ImageId = id
                    };

                    // Set depth values based on type
                    switch (cropCoordinate.Type)
                    {
                        case ImageConstSettings.ImageCropRow:
                        case ImageConstSettings.ImageCropRowLower:
                        {
                            var newDepthTo = currentDepthFrom + delta;
                            imageCrop.DepthFrom = currentDepthFrom;
                            imageCrop.DepthTo = newDepthTo;
                            currentDepthFrom = newDepthTo;
                            break;
                        }
                        case ImageConstSettings.ImageCropBox:
                        case ImageConstSettings.ImageCropBoxLower:
                            imageCrop.DepthFrom = imgDepthFrom;
                            imageCrop.DepthTo = imgDepthTo;
                            break;
                    }

                    try
                    {
                        // Process individual crop with proper resource management
                        await ProcessSingleCrop(img, imageCrop, cropCoordinate, isSave);
                        imageCrops.Add(imageCrop);
                    }
                    catch (Exception ex)
                    {
                        // Log error but continue processing other crops
                        // Consider adding proper logging here
                        Console.WriteLine($"Error processing crop: {ex.Message}");
                    }
                }

                // Save all changes at once
                if (isSave)
                {
                    await unitOfWorkManager.Current.SaveChangesAsync();
                }

                return imageCrops;
            }
            catch (Exception ex)
            {
                // Log the exception
                // Consider adding proper logging here
                Console.WriteLine($"CropImg failed: {ex.Message}");
                throw;
            }
        }

        private async Task ProcessSingleCrop(Image img, ImageCrop imageCrop, CropCoordinate cropCoordinate, bool isSave)
        {
            // Create the crop rectangle
            var cropRect = new Rectangle(
                (int)cropCoordinate.X,
                (int)cropCoordinate.Y,
                (int)cropCoordinate.Width,
                (int)cropCoordinate.Height
            );

            // Clone with proper disposal
            using var croppedImage = img.Clone(ctx => ctx.Crop(cropRect));
            using var memoryStream = new MemoryStream();

            // Save to memory stream
            await croppedImage.SaveAsJpegAsync(memoryStream);
            memoryStream.Position = 0;

            var croppedFileName =
                $"cropped_{imageCrop.ImageId}_{imageCrop.DepthFrom}_{imageCrop.DepthTo}_{Guid.NewGuid()}";
            var formFile = new FormFile(memoryStream, 0, memoryStream.Length, croppedFileName, croppedFileName);

            try
            {
                var imageUrls =
                    await resizeService.ResizeAndUploadImageAsync(formFile, croppedFileName, ".jpg", true);

                var originImage = imageUrls.FirstOrDefault(x => x.size == ImageConstSettings.ImageFullSize);
                var mediumImage = imageUrls.FirstOrDefault(x => x.size == ImageConstSettings.ImageMediumSize);

                imageCrop.UrlCroppedImage = originImage.url;
                imageCrop.MediumSize = mediumImage.url;

                if (isSave)
                {
                    await cropRepository.InsertAsync(imageCrop);
                }

            }
            catch (Exception ex)
            {
                throw new UserFriendlyException(ex.Message);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="coordinates"></param>
        /// <param name="imgWidth"></param>
        /// <param name="imgHeight"></param>
        /// <returns></returns>
        /// <exception cref="UserFriendlyException"></exception>
        public static CropCoordinate[] FilterAndAdjustCoordinates(CropCoordinate[] coordinates, int imgWidth,
            int imgHeight)
        {
            if (imgWidth <= 0 || imgHeight <= 0)
            {
                throw new UserFriendlyException("The image size must be greater than 0.");
            }

            var filteredList = new List<CropCoordinate>();

            foreach (var coord in coordinates)
            {
                if (coord.Width <= 0 || coord.Height <= 0) continue;

                // Create a new instance to avoid modifying the original
                var adjustedCoord = new CropCoordinate
                {
                    Type = coord.Type,
                    X = Math.Max(0, coord.X),
                    Y = Math.Max(0, coord.Y),
                    Width = coord.Width,
                    Height = coord.Height
                };

                adjustedCoord.Width = Math.Min(adjustedCoord.Width, imgWidth - adjustedCoord.X);
                adjustedCoord.Height = Math.Min(adjustedCoord.Height, imgHeight - adjustedCoord.Y);

                filteredList.Add(adjustedCoord);
            }

            return filteredList.ToArray();
        }

        private static async Task<Image> LoadImageAsync(string imageUrl)
        {
            using var httpClient = new HttpClient();
            httpClient.Timeout = TimeSpan.FromMinutes(2); // Set a reasonable timeout

            try
            {
                using var response = await httpClient.GetAsync(imageUrl);
                response.EnsureSuccessStatusCode();

                await using var inputStream = await response.Content.ReadAsStreamAsync();
                var image = await Image.LoadAsync(inputStream);

                // Get the EXIF orientation value if it exists
                if (image.Metadata.ExifProfile?.TryGetValue(
                        SixLabors.ImageSharp.Metadata.Profiles.Exif.ExifTag.Orientation,
                        out var orientation) == true)
                {
                    // Apply rotation based on EXIF orientation
                    switch (orientation.Value)
                    {
                        case 2: // Flipped horizontally
                            image.Mutate(i => i.Flip(FlipMode.Horizontal));
                            break;
                        case 3: // Rotated 180 degrees
                            image.Mutate(i => i.Rotate(180));
                            break;
                        case 4: // Flipped vertically
                            image.Mutate(i => i.Flip(FlipMode.Vertical));
                            break;
                        case 5: // Flipped horizontally and rotated 270 degrees
                            image.Mutate(i => i.Flip(FlipMode.Horizontal).Rotate(270));
                            break;
                        case 6: // Rotated 90 degrees
                            image.Mutate(i => i.Rotate(90));
                            break;
                        case 7: // Flipped horizontally and rotated 90 degrees
                            image.Mutate(i => i.Flip(FlipMode.Horizontal).Rotate(90));
                            break;
                        case 8: // Rotated 270 degrees
                            image.Mutate(i => i.Rotate(270));
                            break;
                    }

                    // Remove the orientation tag to prevent double rotation by other software
                    image.Metadata.ExifProfile?.RemoveValue(SixLabors.ImageSharp.Metadata.Profiles.Exif.ExifTag
                        .Orientation);
                }

                return image;
            }
            catch (HttpRequestException ex)
            {
                throw new Exception($"Failed to download image from {imageUrl}: {ex.Message}", ex);
            }
            catch (TaskCanceledException ex)
            {
                throw new Exception($"Image download timed out for {imageUrl}: {ex.Message}", ex);
            }
        }
    }
}