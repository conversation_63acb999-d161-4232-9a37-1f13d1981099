﻿using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using Abp.UI;
using Azure;
using Azure.Storage.Blobs;
using Microsoft.Extensions.Configuration;

namespace aibase.Images.Services.AzureService
{
    /// <inheritdoc />
    public class AzureService : IAzureService
    {
        private readonly string _connectionString;
        private readonly string _containerName;
        private readonly string _cdnHostname;

        /// <summary>
        ///
        /// </summary>
        /// <param name="configuration"></param>
        public AzureService(IConfiguration configuration)
        {
            _connectionString = configuration.GetValue<string>("AzureStorage:ConnectionString") ?? "";
            _containerName = configuration.GetValue<string>("AzureStorage:ContainerName") ?? "";
            _cdnHostname = configuration.GetValue<string>("AzureStorage:CdnHostname") ?? "";
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="fileStream"></param>
        /// <param name="fileName"></param>
        /// <param name="overwrite"></param>
        /// <returns></returns>
        public async Task<string> UploadFileAsync(Stream fileStream, string fileName, bool overwrite = false)
        {
            try
            {
                var blobServiceClient = new BlobServiceClient(_connectionString);
                var blobContainerClient =
                    blobServiceClient.GetBlobContainerClient(_containerName);


                var blobClient = blobContainerClient.GetBlobClient(fileName);
                if (!overwrite)
                {
                    // Check if the blob exists
                    if (await blobClient.ExistsAsync())
                    {
                        var cdnUrl = !string.IsNullOrEmpty(_cdnHostname)
                            ? blobClient.Uri.ToString().Replace(blobClient.Uri.Host, _cdnHostname)
                            : blobClient.Uri.ToString();
                        return cdnUrl;
                        // throw new UserFriendlyException("The image name already exists in Azure Storage Blob");
                    }
                }
                
                await blobClient.UploadAsync(fileStream, overwrite);
                var cdnImageUrl = !string.IsNullOrEmpty(_cdnHostname)
                    ? blobClient.Uri.ToString().Replace(blobClient.Uri.Host, _cdnHostname)
                    : blobClient.Uri.ToString();
                return cdnImageUrl;
            }
            catch (RequestFailedException ex)
            {
                throw new UserFriendlyException($"An error occurred while uploading the file '{fileName}'.", ex);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="fileName"></param>
        /// <exception cref="UserFriendlyException"></exception>
        public async Task DeleteFileAsync(string fileName)
        {
            var blobServiceClient = new BlobServiceClient(_connectionString);
            var blobContainerClient = blobServiceClient.GetBlobContainerClient(_containerName);

            var blobClient = blobContainerClient.GetBlobClient(Path.GetFileName(fileName));

            bool exists = await blobClient.ExistsAsync();

            if (exists)
            {
                try
                {
                    await blobClient.DeleteIfExistsAsync();
                }
                catch (RequestFailedException ex)
                {
                    throw new UserFriendlyException($"Error deleting file '{fileName}': {ex.Message}");
                }
            }
        }

        /// <inheritdoc />
        public async Task<(bool success, string message)> CheckConnectionAsync()
    {
        try
        {
            // Test the connection string format
            if (string.IsNullOrEmpty(_connectionString))
            {
                return (false, "Connection string is empty");
            }

            // Create clients
            var blobServiceClient = new BlobServiceClient(_connectionString);
            var containerClient = blobServiceClient.GetBlobContainerClient(_containerName);

            // Test service access by getting account properties
            await blobServiceClient.GetAccountInfoAsync();

            // Test container access
            bool containerExists = await containerClient.ExistsAsync();
            if (!containerExists)
            {
                return (false, $"Container '{_containerName}' does not exist");
            }

            // Test container permissions by listing blobs (this will verify read access)
            await containerClient.GetPropertiesAsync();
            
            // Optional: Test write permission by creating and deleting a test blob
            var testBlobName = $"connection-test-{Guid.NewGuid()}.txt";
            var blobClient = containerClient.GetBlobClient(testBlobName);
            
            using (var ms = new MemoryStream(Encoding.UTF8.GetBytes("connection test")))
            {
                await blobClient.UploadAsync(ms, overwrite: true);
            }
            await blobClient.DeleteAsync();

            return (true, "Connection successful");
        }
        catch (RequestFailedException ex)
        {
            var errorMessage = ex.ErrorCode switch
            {
                "InvalidAuthenticationInfo" => "Invalid authentication. Please check the account key or SAS token.",
                "ContainerNotFound" => $"Container '{_containerName}' not found.",
                "UnauthorizedBlobOverwrite" => "No write permission to the container.",
                _ => $"Azure Storage error: {ex.ErrorCode} - {ex.Message}"
            };
            
            return (false, errorMessage);
        }
        catch (Exception ex)
        {
            var errorMessage = $"Unexpected error checking Azure Storage connection: {ex.Message}";
            return (false, errorMessage);
        }
    }
    }
}