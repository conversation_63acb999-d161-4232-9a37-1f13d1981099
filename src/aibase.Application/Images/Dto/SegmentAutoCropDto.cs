using System.Collections.Generic;
using Newtonsoft.Json;

namespace aibase.Images.Dto;

/// <summary>
/// 
/// </summary>
public class SegmentAutoCropDto
{
    /// <summary>
    /// 
    /// </summary>
    public SegmentAutoCropDetection Detection { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("warped_image_filename")]
    public string WarpedImageFilename { get; set; } = string.Empty;
    
    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("warped_image_url")]
    public string WarpedImageUrl { get; set; } = string.Empty;
}

/// <summary>
/// 
/// </summary>
public class SegmentAutoCropDetection
{
    /// <summary>
    /// 
    /// </summary>
    public List<SegmentAutoCropPrediction> Predictions { get; set; } = [];
}

/// <summary>
/// 
/// </summary>
public class SegmentAutoCropPrediction
{
    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("class")]
    public string Class { get; set; } = string.Empty;
    
    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("confidence")]
    public double Confidence { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("height")]
    public double Height { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("width")]
    public double Width { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("x")]
    public double X { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("y")]
    public double Y { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("points")]
    public List<List<int>> Points { get; set; } = [];
}