﻿using System;
using Abp.Application.Services.Dto;
using aibase.ImageCrops.Dto;
using aibase.ImageEntity;
using AutoMapper;
using System.Collections.Generic;
using aibase.DrillHoles.Dto;
using aibase.ImageSubtypes.Dto;
using aibase.ImageTypes.Dto;
using aibase.Projects.Dto;
using aibase.Prospects.Dto;

namespace aibase.Images.Dto
{
    /// <inheritdoc />
    [AutoMap(typeof(Image))]
    public class ImageDto : EntityDto
    {
        /// <summary>
        /// 
        /// </summary>
        public List<FileDto> files { get; set; } = [];

        /// <summary>
        /// 
        /// </summary>
        public ImageClass ImageClass { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public ImageCategory Type { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public StandardType? StandardType { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DrillHoleDto DrillHole { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public ProjectDto Project { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public ProspectDto Prospect { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public double DepthFrom { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public double DepthTo { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public ImageStatus? ImageStatus { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? BoundingBox { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? BoundingRows { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? OcrResult { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? DirectOcrResult { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? SegmentResult { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? SegmentDetailResult { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? DrillholeNameOcr { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public double? DepthFromOcr { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public double? DepthToOcr { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? Fractures { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? OriginalDirectOcrResult { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? OriginalSegmentResult { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? OriginalBoundingBox { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? OriginalBoundingRows { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? OriginalDrillholeNameOcr { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? OriginalFractures { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public List<ImageCropDto> CroppedImages { get; set; } = [];

        /// <summary>
        /// 
        /// </summary>
        public string? CreatedByUser { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public string? CreatedByName { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public DateTime CreationTime { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public ImageCategoryNew ImageCategory { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public ImageTypeDto? ImageType { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public ImageSubtypeDto? ImageSubtype { get; set; }
    }
}