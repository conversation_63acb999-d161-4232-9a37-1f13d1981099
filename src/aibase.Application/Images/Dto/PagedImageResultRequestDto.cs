﻿using Abp.Application.Services.Dto;
using aibase.ImageEntity;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace aibase.Images.Dto;

/// <summary>
/// 
/// </summary>
public class PagedImageResultRequestDto : PagedResultRequestDto, IValidatableObject
{
    /// <summary>
    ///
    /// </summary>
    public string? ProjectIds { get; set; }
        
    /// <summary>
    ///
    /// </summary>
    public string? ProspectIds { get; set; }
        
    /// <summary>
    ///
    /// </summary>
    public string? HoleIds { get; set; }
        
    /// <summary>
    ///
    /// </summary>
    public double? DepthFrom { get; set; }
        
    /// <summary>
    ///
    /// </summary>
    public double? DepthTo { get; set; }
        
    /// <summary>
    ///
    /// </summary>
    public double? Depth { get; set; }
        
    /// <summary>
    ///
    /// </summary>
    public ImageClass? ImageClass { get; set; }
        
    /// <summary>
    ///
    /// </summary>
    public ImageCategory? Type { get; set; }
        
    /// <summary>
    ///
    /// </summary>
    public StandardType? StandardType { get; set; }
        
    /// <summary>
    ///
    /// </summary>
    public ImageStatus? ImageStatus { get; set; }
        
    /// <summary>
    ///
    /// </summary>
    public string? SortField { get; set; }
        
    /// <summary>
    ///
    /// </summary>
    public string? SortOrder { get; set; }
        
    /// <summary>
    ///
    /// </summary>
    public string? DrillHoleNames { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public ImageCategoryNew? ImageCategory { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public int? ImageTypeId { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public int? ImageSubtypeId { get; set; }

    /// <summary>
    /// Specifies a JSON string array of field names to exclude from the ImageDto in the response.
    /// Field names are compared case-insensitively.
    /// Example: "[\"OcrResult\", \"fractures\"]"
    /// Available fields to ignore:
    /// "OriginalFractures", "OriginalBoundingBox", "OriginalBoundingRows", "OriginalSegmentResult",
    /// "OriginalDrillholeNameOcr", "OriginalDirectOcrResult", "SegmentResult", "SegmentDetailResult",
    /// "DrillholeNameOcr", "DepthFromOcr", "DepthToOcr", "OcrResult", "DirectOcrResult",
    /// "Fractures", "Project", "Prospect", "DrillHole", "CroppedImages", "BoundingBox",
    /// "BoundingRows", "files"
    /// </summary>
    public string? IgnoreFields { get; set; } = "[]";

    /// <inheritdoc />
    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        if (string.IsNullOrWhiteSpace(HoleIds) && string.IsNullOrWhiteSpace(DrillHoleNames))
        {
            yield return new ValidationResult(
                "Either HoleIds or DrillHoleNames must be provided.",
                new[] { nameof(HoleIds), nameof(DrillHoleNames) });
        }
    }
}