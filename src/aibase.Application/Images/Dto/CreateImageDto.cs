﻿using aibase.ImageEntity;
using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;

namespace aibase.Images.Dto;

/// <summary>
/// 
/// </summary>
public class CreateImageDto
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public IFormFile Image { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public IFormFile? ImageBox { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public string? CoordinateBox { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public ImageClass? ImageClass { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public ImageCategory Type { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public StandardType? StandardType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? HoleId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? ProjectId { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public int? ProspectId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? BoundingBoxId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? BoundingRowsId { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public int? WorkflowId { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public bool? IsUnnamed { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public bool? IsAiNaming { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public double? DepthFrom { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public double? DepthTo { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public ImageCategoryNew ImageCategory { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public int? ImageTypeId { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public int? ImageSubtypeId { get; set; }
}