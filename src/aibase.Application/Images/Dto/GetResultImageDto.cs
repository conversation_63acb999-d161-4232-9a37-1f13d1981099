﻿using System.Collections.Generic;
using aibase.ImageCrops.Dto;

namespace aibase.Images.Dto
{
    /// <summary>
    /// 
    /// </summary>
    public class GetResultImageDto
    {
        /// <summary>
        /// 
        /// </summary>
        public List<ImageCropDto> ImageCrops { get; set; } = [];
        
        /// <summary>
        /// 
        /// </summary>
        public string? ImageOcrs { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public string? ImageSegments { get; set; }
    }
}
