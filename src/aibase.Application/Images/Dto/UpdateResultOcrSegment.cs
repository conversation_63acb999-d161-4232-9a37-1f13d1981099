﻿using System.ComponentModel.DataAnnotations;

namespace aibase.Images.Dto;

/// <summary>
/// 
/// </summary>
public class UpdateResultOcrSegment
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int Id { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public string? Ocr { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public string? DirectOcr { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public string? Segment { get; set; }
}