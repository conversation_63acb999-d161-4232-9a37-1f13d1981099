﻿using aibase.ImageEntity;
using System.ComponentModel.DataAnnotations;

namespace aibase.Images.Dto;

/// <summary>
/// 
/// </summary>
public class UpdateImageDto
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int Id { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public int? ProjectId { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public int? ProspectId { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public int? DrillHoleId { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public string? BoundingBox { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public string? BoundingRows { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public double? DepthFrom { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public double? DepthTo { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public ImageStatus? Status { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public ImageCategory? Type { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public ImageClass? ImageClass { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public StandardType? StandardType { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public ImageCategoryNew? ImageCategory { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public int? ImageTypeId { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public int? ImageSubtypeId { get; set; }
}