﻿using System.ComponentModel.DataAnnotations;
using aibase.ImageEntity;

namespace aibase.Images.Dto;

/// <summary>
/// 
/// </summary>
public class ProcessImageDto
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int WorkflowId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int ImageId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public ImageStatus? ImageStatus { get; set; }
}