﻿namespace aibase.Images.Dto;

/// <summary>
/// 
/// </summary>
public class CropCoordinate
{
    /// <summary>
    /// 
    /// </summary>
    public double X { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public double Y { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public double Width { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public double Height { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public string Id { get; set; } = string.Empty;
        
    /// <summary>
    /// 
    /// </summary>
    public string Type { get; set; } = string.Empty;
}