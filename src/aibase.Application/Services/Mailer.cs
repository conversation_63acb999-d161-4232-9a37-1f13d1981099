using System;
using System.Net;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using RestSharp;
using RestSharp.Authenticators;

namespace AiBase.Services;

/// <summary>
/// Email service implementation
/// </summary>
public class Mailer : IMailer
{
    private readonly IConfiguration _configuration;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="configuration"></param>
    public Mailer(IConfiguration configuration)
    {
        _configuration = configuration;
    }

    /// <summary>
    /// Send verification email to user
    /// </summary>
    /// <param name="email">User's email address</param>
    /// <param name="firstName">User's first name</param>
    /// <param name="lastName">User's last name</param>
    /// <param name="userId">User's ID</param>
    /// <param name="token">Verification token</param>
    public async Task SendVerificationEmailAsync(string email, string firstName, string lastName, long userId,
        string token)
    {
        var frontEndUrl = _configuration["App:FrontEndUrl"]?.TrimEnd('/');
        var verificationLink =
            $"{frontEndUrl}/verify-email?token={userId}_{token}&email={WebUtility.UrlEncode(email)}";

        var message = $@"
                Dear {firstName} {lastName},
                <br/><br/>
                Thank you for registering with FastGeo! To complete the registration process and verify your email address, please click the button below:
                <br/><br/>
                <p><a href=""{verificationLink}"" style=""display:inline-block;background-color:#007bff;color:#ffffff;text-decoration:none;padding:10px 20px;border-radius:5px;"">Verify Email Address</a></p>
                <br/>
                Once your email is verified, you'll have full access to our services and features.
                <br/><br/>
                Thank you,<br/>
                FastGeo Team";

        await SendEmailAsync(email, "[FastGeo] Verify Your Email", message);
    }

    /// <summary>
    /// Send password reset email to user
    /// </summary>
    public async Task SendPasswordResetEmailAsync(string email, string firstName, string lastName,
        string resetToken)
    {
        var frontEndUrl = _configuration["App:FrontEndUrl"]?.TrimEnd('/');
        var resetUrl =
            $"{frontEndUrl}/reset-password?token={WebUtility.UrlEncode(resetToken)}&email={WebUtility.UrlEncode(email)}";

        var emailBody = $@"
                <h2>Reset Your Password</h2>
                <p>Hello {firstName} {lastName},</p>
                <p>You have requested to reset your password. Click the link below to set a new password:</p>
                <p><a href='{resetUrl}' style='padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px;'>Reset Password</a></p>
                <p>If you didn't request this, please ignore this email.</p>
                <p>This link will expire in 24 hours.</p>
                <p>If the button above doesn't work, copy and paste this URL into your browser:</p>
                <p>{resetUrl}</p>";

        await SendEmailAsync(email, "[FastGeo] Reset Your Password", emailBody);
    }

    /// <inheritdoc />
    public async Task SendPasswordAdminCreateAccountAsync(string email, string firstName, string lastName,
        string resetToken)
    {
        var frontEndUrl = _configuration["App:FrontEndUrl"]?.TrimEnd('/');
        var loginUrl = $"{frontEndUrl}/login";
        var resetUrl =
            $"{frontEndUrl}/reset-password?token={WebUtility.UrlEncode(resetToken)}&email={WebUtility.UrlEncode(email)}";

        var emailBody = $@"
            <h2>Welcome to FastGeo</h2>
            <p>Hello {firstName} {lastName},</p>
            <p>Your FastGeo account has been successfully created! To get started, you'll need to set up your password.</p>
            <p><strong>Your Account Details:</strong> <a href='mailto:<EMAIL>' style='color: #007bff; text-decoration: underline;'>{email}</a></p>
            <p><strong>Next Steps:</strong></p>
            <ol>
                <li>
                    Click <a href='{resetUrl}' style='color: #007bff; text-decoration: underline;'>here</a> to set your password
                </li>
                <li>
                    Log in to your account at <a href='https://portal1.fastgeo.com.au/login' style='color: #007bff; text-decoration: underline;'>https://portal1.fastgeo.com.au/login</a>
                </li>
            </ol>
            <p><i>If you are having trouble, please reset your password using the <a href='https://portal1.fastgeo.com.au/forgot-password' style='color: #007bff; text-decoration: underline;'>'Forgot Password'</a> link on the login page.</i></p>
            <p><strong>Need Help?</strong> If you have any questions or need assistance, please visit our website <a href='http://www.fastgeo.ai' style='color: #007bff; text-decoration: underline;'>http://www.fastgeo.ai</a> or contact our support team at <a href='mailto:<EMAIL>' style='color: #007bff; text-decoration: underline;'><EMAIL></a>.</p>
            <p style='margin-top: 20px;'>Best regards,<br>The FastGeo Team</p>";

        await SendEmailAsync(email, "Welcome to FastGeo", emailBody);
    }

    /// <summary>
    /// Send email using configured SMTP settings
    /// </summary>
    /// <param name="email">Recipient email address</param>
    /// <param name="subject">Email subject</param>
    /// <param name="message">Email message body</param>
    public async Task SendEmailAsync(string email, string subject, string message)
    {
        var apiKey = _configuration["Email:SmtpPassword"] ?? ""; // Using existing SMTP password as API key
        const string domain = "no-reply.fastgeo.com.au";
        var fromEmail = _configuration["Email:FromEmail"];
        var fromDisplayName = _configuration["Email:FromDisplayName"];

        var options = new RestClientOptions("https://api.mailgun.net/v3")
        {
            Authenticator = new HttpBasicAuthenticator("api", apiKey)
        };
        var client = new RestClient(options);

        var request = new RestRequest();
        request.AddParameter("domain", domain, ParameterType.UrlSegment);
        request.Resource = "{domain}/messages";
        request.AddParameter("from", $"{fromDisplayName} <{fromEmail}>");
        request.AddParameter("to", email);
        request.AddParameter("subject", subject);
        request.AddParameter("html", message);
        request.Method = Method.Post;

        var response = await client.ExecuteAsync(request);

        if (!response.IsSuccessful)
        {
            throw new Exception($"Failed to send email: {response.ErrorMessage}");
        }
    }
}