using System.Threading.Tasks;
using Abp.Application.Services;
using Abp.Authorization;
using aibase.Services.ErrorFileUpload.Dto;
using Microsoft.AspNetCore.Mvc;

namespace aibase.Services.ErrorFileUpload
{
    /// <inheritdoc />
    [AbpAuthorize]
    public class ErrorFileAppService : ApplicationService
    {
        private readonly IJsonFileUploadService _jsonFileUploadService;

        /// <inheritdoc />
        public ErrorFileAppService(IJsonFileUploadService jsonFileUploadService)
        {
            _jsonFileUploadService = jsonFileUploadService;
        }

        /// <summary>
        /// Uploads a JSON file and sends a notification to Microsoft Teams
        /// </summary>
        /// <param name="input">The upload request containing the file and notification details</param>
        /// <returns>The URL of the uploaded file</returns>
        [HttpPost]
        public async Task<string> UploadAsync([FromForm] JsonUploadDto input)
        {
            return await _jsonFileUploadService.UploadJsonFileAsync(input);
        }
    }
}