using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.Numbers;
using aibase.Units.Dto;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.Units.Services.UnitService;

/// <inheritdoc />
public class UnitService : IUnitService
{
    private readonly IRepository<Unit, int> _repository;
    private readonly IRepository<Number, int> _numberRepository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    public UnitService(IRepository<Unit, int> repository, IAbpSession abpSession, IMapper mapper, IUnitOfWorkManager unitOfWorkManager, IRepository<Number, int> numberRepository)
    {
        _repository = repository;
        _abpSession = abpSession;
        _mapper = mapper;
        _unitOfWorkManager = unitOfWorkManager;
        _numberRepository = numberRepository;
    }

    /// <inheritdoc />
    public async Task<Unit> CreateAsync(CreateUnitDto input, bool returnExist = false)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }
        var tenantId = _abpSession.GetTenantId();

        var existingUnit =
            await _repository.FirstOrDefaultAsync(d => d.TenantId == tenantId && d.Name == input.Name);
        if (existingUnit != null)
        {
            if (returnExist)
            {
                return existingUnit;
            }
            
            throw new UserFriendlyException($"The Unit with the name {existingUnit.Name} already exists.");
        }
        
        var unit = new Unit()
        {
            Name = input.Name,
            Code = input.Code,
            IsActive = input.IsActive,
            TenantId = tenantId,
        };

        await _repository.InsertAsync(unit);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        return unit;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<UnitDto>> GetAllAsync(PagedUnitResultRequestDto input)
    {
        var query = _repository.GetAll()
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .OrderBy(r => r.Name);

        var totalCount = await query.CountAsync();

        var units = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        return new PagedResultDto<UnitDto>(totalCount, _mapper.Map<List<UnitDto>>(units));
    }

    /// <inheritdoc />
    public async Task<UnitDto> UpdateAsync(UpdateUnitDto input)
    {
        var unit = await ValidateUnitEntity(input.Id);

        unit.Name = input.Name ?? unit.Name;
        unit.Code = input.Code ?? unit.Code;
        unit.IsActive = input.IsActive ?? unit.IsActive;

        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task<UnitDto> GetAsync(EntityDto<int> input)
    {
        var unit = await ValidateUnitEntity(input.Id);
        return _mapper.Map<UnitDto>(unit);
    }

    /// <inheritdoc />
    public async Task DeleteAsync(EntityDto<int> input)
    {
        var unit = await ValidateUnitEntity(input.Id);
        
        var numberUsed = await _numberRepository.FirstOrDefaultAsync(x =>  x.UnitId == input.Id);
        if (numberUsed != null)
        {
            throw new UserFriendlyException("Cannot delete this Unit because it is being used in Number.");
        }
        
        await _repository.DeleteAsync(unit);
    }

    private async Task<Unit> ValidateUnitEntity(int id)
    {
        var unit = await _repository.FirstOrDefaultAsync(x => x.Id == id);
        if (unit == null)
        {
            throw new EntityNotFoundException(typeof(Unit), id);
        }
        
        return unit;
    }
}