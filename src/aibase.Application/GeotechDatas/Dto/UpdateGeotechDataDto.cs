using Abp.Application.Services.Dto;

namespace aibase.GeotechDatas.Dto;

/// <summary>
/// DTO for updating a GeotechData
/// </summary>
public class UpdateGeotechDataDto : EntityDto
{
    /// <summary>
    /// ID of the associated GeotechSuite
    /// </summary>
    public int? GeotechSuiteId { get; set; }
    
    /// <summary>
    /// ID of the associated GeotechField
    /// </summary>
    public int? GeotechFieldId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int? DrillHoleId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int? ImageCropId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public double? X { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public double? XTo { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int? ImageCropIdTo { get; set; }
    
    /// <summary>
    /// Starting depth of the geotechnical data
    /// </summary>
    public double? Depth { get; set; }
    
    /// <summary>
    /// Ending depth of the geotechnical data (optional)
    /// </summary>
    public double? DepthTo { get; set; }
    
    /// <summary>
    /// Alpha angle measurement (optional)
    /// </summary>
    public double? AlphaAngle { get; set; }
    
    /// <summary>
    /// Beta angle measurement (optional)
    /// </summary>
    public double? BetaAngle { get; set; }
    
    /// <summary>
    /// Width measurement (optional)
    /// </summary>
    public double? Width { get; set; }
    
    /// <summary>
    /// ID of the associated StructureCondition (optional)
    /// </summary>
    public int? StructureConditionId { get; set; }
    
    /// <summary>
    /// ID of the associated RockGroup (optional)
    /// </summary>
    public int? RockGroupId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int? RockTypeId { get; set; }
}