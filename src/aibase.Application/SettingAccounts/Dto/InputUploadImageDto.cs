using System.Collections.Generic;

namespace aibase.SettingAccounts.Dto;

/// <summary>
/// 
/// </summary>
public class InputUploadImageDto
{
    /// <summary>
    /// 
    /// </summary>
    public InputDto Input { get; set; }
}

/// <summary>
/// 
/// </summary>
public class InputDto
{
    /// <summary>
    /// 
    /// </summary>
    public ImageInfo Image { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int ImageClass { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int Type { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int StandardType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? HoleId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? ProjectId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? ProspectId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? BoundingBoxId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? BoundingRowsId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? WorkflowId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool? IsUnnamed { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool? IsAiNaming { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public float? DepthFrom { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public float? DepthTo { get; set; }
}

/// <summary>
/// 
/// </summary>
public class ImageInfo
{
    /// <summary>
    /// 
    /// </summary>
    public string ContentDisposition { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string ContentType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Dictionary<string, string[]> Headers { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public long Length { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string FileName { get; set; }
}