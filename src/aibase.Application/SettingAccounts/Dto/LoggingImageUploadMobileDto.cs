using System;

namespace aibase.SettingAccounts.Dto;

/// <summary>
/// 
/// </summary>
public class LoggingImageUploadMobileDto
{
    /// <summary>
    /// 
    /// </summary>
    public long UserId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public DateTime ExecutionTime { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public InputUploadImageDto Params { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public bool Success { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public string? ErrorInfo { get; set; }
}