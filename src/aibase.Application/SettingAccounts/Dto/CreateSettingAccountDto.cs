﻿using aibase.ImageEntity;
using aibase.Settings;
using System.ComponentModel.DataAnnotations;

namespace aibase.SettingAccounts.Dto
{
    /// <summary>
    /// 
    /// </summary>
    public class CreateSettingAccountDto
    {
        /// <summary>
        /// 
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? Units { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? UnitsSymbol { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public ImageTypeUpload? ImageType { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? CollectionName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public BoundingPolygonType? BoundingPolygonType { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public RowPolygonType? RowPolygonType { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool ViewWithBoundingPolygon { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool RowPolygon { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? LogoProduct { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool? IsUseLogo { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public ImageStatus? ImageStatus { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DrillHoleView DrillHoleView  { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Required]
        public int TenantId { get; set; }
    }
}
