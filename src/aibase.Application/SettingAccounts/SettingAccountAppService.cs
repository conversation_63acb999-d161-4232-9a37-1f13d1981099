﻿using Abp.Application.Services;
using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using aibase.SettingAccounts.Dto;
using aibase.Settings;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using aibase.SettingAccounts.Services.SettingAccountService;

namespace aibase.SettingAccounts
{
    /// <summary>
    /// 
    /// </summary>
    [AbpAuthorize]
    public class SettingAccountAppService :
        AsyncCrudAppService<Setting, SettingAccountDto, int, PagedSettingAccountResultRequestDto,
            CreateSettingAccountDto, SettingAccountDto>, ISettingAccountAppService
    {
        private readonly ISettingAccountService _settingAccountService;

        /// <inheritdoc />
        public SettingAccountAppService(
            IRepository<Setting, int> repository,
            ISettingAccountService settingAccountService) : base(repository)
        {
            _settingAccountService = settingAccountService;
        }

        /// <inheritdoc />
        [ApiExplorerSettings(IgnoreApi = true)]
        public override async Task<SettingAccountDto> CreateAsync(CreateSettingAccountDto input)
        {
            return await base.CreateAsync(input);
        }

        /// <inheritdoc />
        public override async Task<SettingAccountDto> UpdateAsync(SettingAccountDto input)
        {
            return await _settingAccountService.UpdateAsync(input);
        }

        /// <inheritdoc />
        public override async Task<SettingAccountDto> GetAsync(EntityDto<int> input)
        {
            return await _settingAccountService.GetAsync(input);
        }

        /// <inheritdoc />
        public override async Task<PagedResultDto<SettingAccountDto>> GetAllAsync(
            PagedSettingAccountResultRequestDto input)
        {
            return await _settingAccountService.GetAllAsync(input);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public async Task<SettingAccountDto> GetDetailSettingAsync()
        {
            return await _settingAccountService.GetDetailSettingAsync();
        }

        /// <inheritdoc />
        public async Task<string> ExportGeologyConfigAsync()
        {
            return await _settingAccountService.ExportGeologyConfigAsync();
        }

        /// <inheritdoc />
        public async Task ImportGeologyConfigAsync([FromForm] ImportGeologyConfigDto input)
        {
            await _settingAccountService.ImportGeologyConfigAsync(input);
        }

        /// <inheritdoc />
        public Task<PagedResultDto<LoggingImageUploadMobileDto>> GetLoggingImageUpdateMobile(PagedLoggingImageUploadMobileResultRequestDto input)
        {
            return _settingAccountService.GetLoggingImageUpdateMobile(input);
        }
    }
}