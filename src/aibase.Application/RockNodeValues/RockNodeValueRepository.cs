using Abp.EntityFrameworkCore;
using aibase.EntityFrameworkCore;
using aibase.EntityFrameworkCore.Repositories;

namespace aibase.RockNodeValues
{
    /// <summary>
    /// 
    /// </summary>
    public class RockNodeValueRepository : aibaseRepositoryBase<RockNodeValue, int>, IRockNodeValueRepository
    {
        /// <inheritdoc />
        public RockNodeValueRepository(IDbContextProvider<aibaseDbContext> dbContextProvider)
            : base(dbContextProvider)
        {
        }
    }
}