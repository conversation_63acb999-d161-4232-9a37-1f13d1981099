using Abp.Application.Services.Dto;

namespace aibase.ImageTypes.Dto;

/// <inheritdoc />
public class PagedImageTypeResultRequestDto : PagedResultRequestDto
{
    /// <summary>
    /// 
    /// </summary>
    public string? Keyword { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public bool? IsActive { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int? ProjectId { get; set; }
}