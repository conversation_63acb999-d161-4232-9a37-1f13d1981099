using System.Collections.Generic;
using Abp.Application.Services.Dto;
using Abp.AutoMapper;

namespace aibase.ImageTypes.Dto;

/// <inheritdoc />
[AutoMap(typeof(ImageType))]
public class UpdateImageTypeDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool? IsStandard { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool? IsRigCorrected { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public bool? IsRig { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int? Sequence { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int? Priority { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public List<int>? ProjectIds { get; set; } = [];
}