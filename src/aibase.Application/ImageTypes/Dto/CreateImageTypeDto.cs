using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace aibase.ImageTypes.Dto;

/// <summary>
/// Used to create a new ImageType
/// </summary>
public class CreateImageTypeDto
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// 
    /// </summary>
    public bool IsStandard { get; set; } = false;

    /// <summary>
    /// 
    /// </summary>
    public bool IsRigCorrected { get; set; } = false;
    
    /// <summary>
    /// 
    /// </summary>
    public bool IsRig { get; set; } = false;
    
    /// <summary>
    /// 
    /// </summary>
    public int Sequence { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int? Priority { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<int>? ProjectIds { get; set; } = [];
}