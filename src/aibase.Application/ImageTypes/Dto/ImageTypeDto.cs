using Abp.Application.Services.Dto;
using aibase.ImageSubtypes.Dto;
using AutoMapper;
using System.Collections.Generic;
using aibase.Projects.Dto;

namespace aibase.ImageTypes.Dto;

/// <inheritdoc />
[AutoMap(typeof(ImageType))]
public class ImageTypeDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool IsStandard { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool IsRigCorrected { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool IsRig { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int Sequence { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int? Priority { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public List<ImageSubtypeDto> ImageSubtypes { get; set; } = [];
    
    /// <summary>
    /// 
    /// </summary>
    public List<ProjectDto> Projects { get; set; } = [];
}