using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore.Repositories;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.ImageSubtypes.Dto;
using aibase.ImageTypes.Dto;
using aibase.ProjectImageTypes;
using aibase.Projects.Dto;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.ImageTypes.Services;

/// <inheritdoc />
public class ImageTypeService : IImageTypeService
{
    private readonly IRepository<ImageType, int> _repository;
    private readonly IRepository<ProjectImageType, int> _projectImageTypeRepository;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;
    private readonly IUnitOfWorkManager _unitOfWorkManager;

    /// <summary>
    /// Constructor
    /// </summary>
    public ImageTypeService(
        IRepository<ImageType, int> repository,
        IRepository<ProjectImageType, int> projectImageTypeRepository,
        IAbpSession abpSession,
        IMapper mapper,
        IUnitOfWorkManager unitOfWorkManager
        )
    {
        _repository = repository;
        _projectImageTypeRepository = projectImageTypeRepository;
        _abpSession = abpSession;
        _mapper = mapper;
        _unitOfWorkManager = unitOfWorkManager;
    }

    /// <inheritdoc />
    public async Task<ImageType> CreateAsync(CreateImageTypeDto input, bool returnExist = false)
    {
        var tenantId = _abpSession.GetTenantId();

        var existingImageType = await _repository
            .FirstOrDefaultAsync(x => x.Name == input.Name && x.TenantId == tenantId);
            
        if (existingImageType != null)
        {
            if (returnExist)
            {
                return existingImageType;
            }

            throw new UserFriendlyException($"An Image Type with the name {existingImageType.Name} already exists.");
        }

        var imageType = new ImageType
        {
            Name = input.Name,
            IsActive = input.IsActive,
            IsStandard = input.IsStandard,
            IsRigCorrected = input.IsRigCorrected,
            IsRig = input.IsRig,
            Sequence = input.Sequence,
            Priority = input.Priority,
            TenantId = tenantId,
        };
        
        await ValidateBusinessRulesAsync(imageType.IsStandard, imageType.IsRigCorrected, imageType.IsRig, _abpSession.GetTenantId());

        await _repository.InsertAsync(imageType);
        await _unitOfWorkManager.Current.SaveChangesAsync();
        
        if (input.ProjectIds is { Count: > 0 })
        {
            var assignImageTypeProjectDto = new AssignImageTypeProjectDto
            {
                ImageTypeId = imageType.Id,
                ProjectIds = input.ProjectIds
            };
            await AssignProjectAsync(assignImageTypeProjectDto);
        }

        return imageType;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<ImageTypeDto>> GetAllAsync(PagedImageTypeResultRequestDto input)
    {
        var imageTypeIds = new List<int>();
        if (input.ProjectId.HasValue)
        {
            var projectImageTypes = await _projectImageTypeRepository.GetAll()
                .AsNoTracking()
                .Where(x => x.ProjectId == input.ProjectId)
                .Select(x => x.ImageTypeId)
                .Distinct()
                .ToListAsync();
            imageTypeIds.AddRange(projectImageTypes);
        }
        
        var query = _repository.GetAllIncluding(x => x.ImageSubtypes)
            .Include(x => x.ProjectImageTypes)
            .ThenInclude(x => x.Project)
            .AsNoTracking()
            .Where(x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(imageTypeIds.Count != 0, x => imageTypeIds.Contains(x.Id))
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive);

        if (!string.IsNullOrWhiteSpace(input.Keyword))
        {
            query = query.Where(x => x.Name.ToLower().Contains(input.Keyword.ToLower()));
        }
        
        query = query.OrderBy(x => x.Sequence);

        var totalCount = await query.CountAsync();
        var imageTypes = await query
            .OrderBy(x => x.Name)
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .Select(x => new ImageTypeDto
            {
                Id = x.Id,
                Name = x.Name,
                IsActive = x.IsActive,
                IsStandard = x.IsStandard,
                IsRigCorrected = x.IsRigCorrected,
                IsRig = x.IsRig,
                Sequence = x.Sequence,
                Priority = x.Priority,
                ImageSubtypes = _mapper.Map<List<ImageSubtypeDto>>(x.ImageSubtypes),
                Projects = _mapper.Map<List<ProjectDto>>(x.ProjectImageTypes.Select(p => p.Project))
            })
            .ToListAsync();

        return new PagedResultDto<ImageTypeDto>(totalCount, imageTypes);
    }

    /// <inheritdoc />
    public async Task<ImageTypeDto> UpdateAsync(UpdateImageTypeDto input)
    {
        var imageType = await ValidateImageTypeEntity(input.Id);

        imageType.Name = input.Name ?? imageType.Name;
        imageType.IsActive = input.IsActive ?? imageType.IsActive;
        imageType.IsStandard = input.IsStandard ?? imageType.IsStandard;
        imageType.IsRigCorrected = input.IsRigCorrected ?? imageType.IsRigCorrected;
        imageType.IsRig = input.IsRig ?? imageType.IsRig;
        imageType.Sequence = input.Sequence ?? imageType.Sequence;
        imageType.Priority = input.Priority;
        
        await ValidateBusinessRulesAsync(imageType.IsStandard, imageType.IsRigCorrected, imageType.IsRig,_abpSession.GetTenantId(), imageType.Id);

        await _repository.UpdateAsync(imageType);
        
        if (input.ProjectIds != null)
        {
            var assignImageTypeProjectDto = new AssignImageTypeProjectDto
            {
                ImageTypeId = imageType.Id,
                ProjectIds = input.ProjectIds
            };
            await AssignProjectAsync(assignImageTypeProjectDto);
        }

        return _mapper.Map<ImageTypeDto>(imageType);
    }

    /// <inheritdoc />
    public async Task<ImageTypeDto> GetAsync(EntityDto<int> input)
    {
        var imageType = await ValidateImageTypeEntity(input.Id);
        return _mapper.Map<ImageTypeDto>(imageType);
    }

    /// <inheritdoc />
    public async Task MockDataAsync(int tenantId)
    {
        var imageTypes = new List<ImageType>();
        
        var standard = new ImageType
        {
            Name = "Standard",
            IsActive = true,
            IsStandard = true,
            IsRigCorrected = false,
            TenantId = tenantId,
            Sequence = 1
        };
        imageTypes.Add(standard);

        var hyperspectral = new ImageType
        {
            Name = "Hyperspectral",
            IsActive = true,
            IsStandard = false,
            IsRigCorrected = false,
            TenantId = tenantId,
            Sequence = 2
        };
        imageTypes.Add(hyperspectral);
        
        var optical = new ImageType
        {
            Name = "Optical",
            IsActive = true,
            IsStandard = false,
            IsRigCorrected = false,
            TenantId = tenantId,
            Sequence = 3
        };
        imageTypes.Add(optical);
        
        var rig = new ImageType
        {
            Name = "Rig",
            IsActive = true,
            IsStandard = false,
            IsRigCorrected = true,
            TenantId = tenantId,
            Sequence = 4
        };
        imageTypes.Add(rig);

        await _repository.InsertRangeAsync(imageTypes);
    }

    private async Task<ImageType> ValidateImageTypeEntity(int id)
    {
        var imageType = await _repository.GetAllIncluding(x => x.ImageSubtypes)
            .FirstOrDefaultAsync(x => x.Id == id && x.TenantId == _abpSession.GetTenantId());

        if (imageType == null)
        {
            throw new EntityNotFoundException(typeof(ImageType), id);
        }

        return imageType;
    }
    
    private async Task ValidateBusinessRulesAsync(bool isStandard, bool isRigCorrected,  bool isRig, int tenantId, int? excludeId = null)
    {
        // IsStandard and IsRigCorrected are mutually exclusive
        if (isStandard && isRigCorrected)
        {
            throw new UserFriendlyException("ImageType cannot be both Standard and Rig-Corrected.");
        }
        
        if (isStandard && isRig)
        {
            throw new UserFriendlyException("ImageType cannot be both Standard and Rig.");
        }
        
        if (isRigCorrected && isRig)
        {
            throw new UserFriendlyException("ImageType cannot be both Rig-Corrected and Rig.");
        }
        
        if (isRigCorrected && isRig && isStandard)
        {
            throw new UserFriendlyException("ImageType cannot be Standard, Rig-Corrected and Rig.");
        }
        
        if (isStandard)
        {
            await SetAllOtherRecordsAsNonStandardAsync(tenantId, excludeId);
        }
        
        if (isRigCorrected)
        {
            await SetAllOtherRecordsAsNonRigCorrectedAsync(tenantId, excludeId);
        }
        
        if (isRig)
        {
            await SetAllOtherRecordsAsNonRigRigAsync(tenantId, excludeId);
        }
    }
    
    private async Task SetAllOtherRecordsAsNonStandardAsync(int tenantId, int? excludeId = null)
    {
        var query = _repository.GetAll()
            .Where(x => x.TenantId == tenantId && x.IsStandard);
        
        if (excludeId.HasValue)
        {
            query = query.Where(x => x.Id != excludeId.Value);
        }

        var standardRecords = await query.ToListAsync();
            
        foreach (var record in standardRecords)
        {
            record.IsStandard = false;
        }
    }
    
    private async Task SetAllOtherRecordsAsNonRigCorrectedAsync(int tenantId, int? excludeId = null)
    {
        var query = _repository.GetAll()
            .Where(x => x.TenantId == tenantId && x.IsRigCorrected);
        
        if (excludeId.HasValue)
        {
            query = query.Where(x => x.Id != excludeId.Value);
        }

        var rigCorrectedRecords = await query.ToListAsync();

        foreach (var record in rigCorrectedRecords)
        {
            record.IsRigCorrected = false;
        }
    }
    
    private async Task SetAllOtherRecordsAsNonRigRigAsync(int tenantId, int? excludeId = null)
    {
        var query = _repository.GetAll()
            .Where(x => x.TenantId == tenantId && x.IsRig);
        
        if (excludeId.HasValue)
        {
            query = query.Where(x => x.Id != excludeId.Value);
        }

        var rigCorrectedRecords = await query.ToListAsync();

        foreach (var record in rigCorrectedRecords)
        {
            record.IsRig = false;
        }
    }
    
    private async Task AssignProjectAsync(AssignImageTypeProjectDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var projects = await _projectImageTypeRepository.GetAllListAsync(x =>
            x.ImageTypeId == input.ImageTypeId);

        var projectIds = projects.Select(x => x.ProjectId).ToList();

        var projectsToAdd = input.ProjectIds.Except(projectIds).ToList();
        var projectsToDelete = projectIds.Except(input.ProjectIds).ToList();

        foreach (var projectImageType in projectsToAdd.Select(projectId =>
                     new ProjectImageType
                     {
                         ImageTypeId = input.ImageTypeId,
                         ProjectId = projectId,
                     }))
        {
            await _projectImageTypeRepository.InsertAsync(projectImageType);
        }

        foreach (var projectId in projectsToDelete)
        {
            await _projectImageTypeRepository.DeleteAsync(x =>
                x.ProjectId == projectId && x.ImageTypeId == input.ImageTypeId);
        }
    }
}