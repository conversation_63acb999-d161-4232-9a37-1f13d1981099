using System.Collections.Generic;
using System.Threading.Tasks;
using Abp.Dependency;
using aibase.Downholes.Dto;
using Microsoft.AspNetCore.Mvc;

namespace aibase.Downholes.Services.GeophysicsService;

/// <inheritdoc />
public interface IGeophysicsService : ITransientDependency
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IActionResult> GetGeophysicsDataByProjectAsync(PagedDownholeResultRequestDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    Task<IActionResult> UploadGeophysicsDataAsync([FromForm] UploadDownholeDataDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    Task<List<string>> ValidationGeophysicsDataAsync([FromForm] UploadGeophysicsDataBySuiteDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<int> UploadGeophysicsDataByImportTemplateAsync([FromForm] UploadGeophysicsDataBySuiteDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    Task<int> UpdateGeophysicsDataByImportTemplateAsync([FromForm] UploadGeophysicsDataBySuiteDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    Task<int> AddAndUpdateGeophysicsDataByImportTemplateAsync([FromForm] UploadGeophysicsDataBySuiteDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IActionResult> ValidateGeophysicsDataAsync([FromForm] UploadDownholeDataDto input);
}