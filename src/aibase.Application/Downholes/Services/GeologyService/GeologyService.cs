using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.UI;
using aibase.Downholes.Dto;
using aibase.DrillHoleEntity;
using aibase.EntityFrameworkCore;
using aibase.GeologyDatas;
using aibase.GeologySuites;
using aibase.RockTypes;
using EFCore.BulkExtensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using OfficeOpenXml;

namespace aibase.Downholes.Services.GeologyService;

/// <inheritdoc />
public class GeologyService : IGeologyService
{
    private static readonly string[] DepthColumn = ["Depth From", "Depth To"];
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IDbContextProvider<aibaseDbContext> _dbContextProvider;
    private readonly IRepository<GeologyData, int> _repository;
    private readonly IRepository<DrillHole, int> _drillHoleRepository;
    private readonly IRepository<RockType, int> _rockTypesRepository;
    private readonly IRepository<GeologySuite, int> _geologySuiteRepository;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="unitOfWorkManager"></param>
    /// <param name="dbContextProvider"></param>
    /// <param name="repository"></param>
    /// <param name="drillHoleRepository"></param>
    /// <param name="rockTypesRepository"></param>
    /// <param name="geologySuiteRepository"></param>
    public GeologyService(
        IUnitOfWorkManager unitOfWorkManager,
        IDbContextProvider<aibaseDbContext> dbContextProvider,
        IRepository<GeologyData, int> repository,
        IRepository<DrillHole, int> drillHoleRepository,
        IRepository<RockType, int> rockTypesRepository,
        IRepository<GeologySuite, int> geologySuiteRepository)
    {
        _unitOfWorkManager = unitOfWorkManager;
        _dbContextProvider = dbContextProvider;
        _repository = repository;
        _drillHoleRepository = drillHoleRepository;
        _rockTypesRepository = rockTypesRepository;
        _geologySuiteRepository = geologySuiteRepository;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<IActionResult> GetGeologyDataByProject(PagedGeologyResultRequestDto input)
    {
        var groupIds = !input.Keyword.IsNullOrWhiteSpace()
            ? await _repository.GetAllIncluding(x => x.RockType)
                .AsNoTracking()
                .Where(x => input.Keyword != null && (x.AttributeValue.ToLower().Contains(input.Keyword.ToLower()) ||
                                                      x.RockType.Code.ToLower().Contains(input.Keyword.ToLower())))
                .Select(x => x.GroupId)
                .ToListAsync()
            : [];

        var query = _repository.GetAllIncluding(x => x.RockType)
            .AsNoTracking()
            .Include(x => x.RockType)
            .ThenInclude(r => r.RockStyle)
            .WhereIf(groupIds.Count > 0, x => groupIds.Contains(x.GroupId))
            .WhereIf(input.ProjectId.HasValue, x => x.ProjectId == input.ProjectId)
            .WhereIf(input.GeologySuiteId.HasValue, x => x.GeologySuiteId == input.GeologySuiteId)
            .WhereIf(input.DrillHoleName != null && input.DrillHoleName.Count != 0,
                x => input.DrillHoleName != null && input.DrillHoleName.Contains(x.DrillHole ?? ""))
            .WhereIf(!string.IsNullOrEmpty(input.AttributeName), x => x.AttributeName == input.AttributeName)
            .Where(x => !string.IsNullOrEmpty(x.AttributeName))
            .Select(x => new
            {
                x.AttributeName,
                x.AttributeValue,
                x.GroupId,
                x.DrillHole,
                x.RockType
            });

        var geologyData = await query.ToListAsync();

        var groupedData = geologyData
            .GroupBy(d => d.GroupId)
            .Select(g =>
            {
                var dict = g.ToDictionary(
                    d => d.AttributeName,
                    d => d.AttributeValue);
                dict["groupId"] = g.Key;
                dict["DrillHole"] = g.FirstOrDefault()?.DrillHole ?? "";
                var rockType = g.FirstOrDefault()?.RockType;
                if (rockType != null)
                {
                    dict["RockTypeId"] = rockType.Id.ToString();
                    dict["RockTypeCode"] = rockType.Code;
                    dict["TextColor"] = rockType.RockStyle?.LineColor ?? "";
                    dict["BackgroundColor"] = rockType.RockStyle?.FillColor ?? "";
                }
                else
                {
                    dict["RockTypeId"] = "";
                    dict["RockTypeCode"] = "";
                    dict["TextColor"] = "";
                    dict["BackgroundColor"] = "";
                }

                return dict;
            })
            .ToList();

        if (input.DepthFrom.HasValue)
        {
            groupedData = groupedData.Where(item =>
            {
                if (item.ContainsKey("Depth From") && double.TryParse(item["Depth From"], out double depthValue))
                {
                    return depthValue >= input.DepthFrom;
                }

                return false;
            }).ToList();
        }

        if (input.DepthTo.HasValue)
        {
            groupedData = groupedData.Where(item =>
            {
                if (item.ContainsKey("Depth To") && double.TryParse(item["Depth To"], out double depthValue))
                {
                    return depthValue <= input.DepthTo;
                }

                return false;
            }).ToList();
        }

        var response = new
        {
            Items = groupedData,
        };

        return new OkObjectResult(response);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <param name="groupId"></param>
    public async Task CreateGeologyDataPoint(CreateGeologyDataDto input, string? groupId = null)
    {
        var uuid = groupId ?? Guid.NewGuid().ToString();
        var rockType = await _rockTypesRepository.FirstOrDefaultAsync(x => x.Id == input.RockTypeId);

        var attributeGeologyData = new GeologyData
        {
            ProjectId = input.ProjectId,
            GeologySuiteId = input.GeologySuiteId,
            AttributeName = input.GeologyAttribute,
            AttributeValue = rockType?.Code ?? "",
            GroupId = uuid,
            DrillHole = input.DrillHoleName,
            RockTypeId = input.RockTypeId,
        };
        await _repository.InsertAsync(attributeGeologyData);

        var depthFromData = new GeologyData
        {
            ProjectId = input.ProjectId,
            GeologySuiteId = input.GeologySuiteId,
            AttributeName = "Depth From",
            AttributeValue = input.DepthFrom.ToString(CultureInfo.InvariantCulture),
            GroupId = uuid,
            DrillHole = input.DrillHoleName,
            RockTypeId = input.RockTypeId,
        };
        await _repository.InsertAsync(depthFromData);

        var depthToData = new GeologyData
        {
            ProjectId = input.ProjectId,
            GeologySuiteId = input.GeologySuiteId,
            AttributeName = "Depth To",
            AttributeValue = input.DepthTo.ToString(CultureInfo.InvariantCulture),
            GroupId = uuid,
            DrillHole = input.DrillHoleName,
            RockTypeId = input.RockTypeId,
        };
        await _repository.InsertAsync(depthToData);

        var descriptionData = new GeologyData
        {
            ProjectId = input.ProjectId,
            GeologySuiteId = input.GeologySuiteId,
            AttributeName = "Description",
            AttributeValue = input.Description,
            GroupId = uuid,
            DrillHole = input.DrillHoleName,
            RockTypeId = input.RockTypeId,
        };
        await _repository.InsertAsync(descriptionData);
        await _unitOfWorkManager.Current.SaveChangesAsync();
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    public async Task UpdateGeologyDataPoint(UpdateGeologyDataPointDto input)
    {
        var uuid = input.GroupId;

        await DeleteGeologyDataPoint(uuid);

        var inputCreate = new CreateGeologyDataDto()
        {
            ProjectId = input.ProjectId,
            GeologySuiteId = input.GeologySuiteId,
            RockTypeId = input.RockTypeId,
            Description = input.Description,
            DepthFrom = input.DepthFrom,
            DepthTo = input.DepthTo,
            DrillHoleName = input.DrillHoleName,
            GeologyAttribute = input.GeologyAttribute
        };
        await CreateGeologyDataPoint(inputCreate, uuid);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="groupId"></param>
    public async Task DeleteGeologyDataPoint(string groupId)
    {
        var existingData = await _repository.GetAllListAsync(x => x.GroupId == groupId);
        if (existingData is { Count: > 0 })
        {
            foreach (var data in existingData)
            {
                await _repository.DeleteAsync(data);
            }
        }
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<IActionResult> UploadGeologyData([FromForm] UploadGeologyDataDto input)
    {
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

        var geologySuites = await _geologySuiteRepository.GetAllIncluding(x => x.GeologySuiteFields)
            .AsNoTracking()
            .Where(x => x.GeologyProjectSuites.Any(y => y.ProjectId == input.ProjectId))
            .ToListAsync();
        
        var attributeNames = geologySuites
            .SelectMany(suite => suite.GeologySuiteFields)
            .Select(field => field.Name)
            .Distinct()
            .ToList();

        var attributes = DepthColumn.Concat(attributeNames).ToArray();
        var providedFields = new List<string>();

        var fileExtension = Path.GetExtension(input.ExcelFile.FileName).ToLower();

        using (var stream = new MemoryStream())
        {
            await input.ExcelFile.CopyToAsync(stream);
            stream.Position = 0;

            switch (fileExtension)
            {
                case ".xlsx":
                {
                    ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

                    using var package = new ExcelPackage(stream);
                    var worksheet = package.Workbook.Worksheets[0];
                    var columnCount = worksheet.Dimension.End.Column;

                    for (var col = 2; col <= columnCount; col++)
                    {
                        providedFields.Add(worksheet.Cells[1, col].Text.Trim());
                    }

                    break;
                }
                case ".csv":
                {
                    using var reader = new StreamReader(stream);
                    var headerLine = await reader.ReadLineAsync();
                    if (!string.IsNullOrEmpty(headerLine))
                    {
                        providedFields = headerLine.Split(',')
                            .Skip(1)
                            .Select(field => field.Trim())
                            .ToList();
                    }

                    break;
                }
                default:
                    throw new UserFriendlyException("Unsupported file format. Please upload a .xlsx or .csv file.");
            }
        }

        await DeleteGeologyDataAsync(input.ProjectId, input.GeologySuiteId);

        var dataToInsert = new List<GeologyData>();
        using (var stream = new MemoryStream())
        {
            await input.ExcelFile.CopyToAsync(stream);
            stream.Position = 0;

            switch (fileExtension)
            {
                case ".xlsx":
                {
                    using var package = new ExcelPackage(stream);
                    var worksheet = package.Workbook.Worksheets[0];
                    var rowCount = worksheet.Dimension.End.Row;

                    for (var row = 2; row <= rowCount; row++)
                    {
                        var uuid = Guid.NewGuid().ToString();
                        dataToInsert.AddRange(from field in providedFields
                            where attributes.Contains(field)
                            let columnIndex = providedFields.IndexOf(field) + 2
                            select new GeologyData()
                            {
                                ProjectId = input.ProjectId,
                                GeologySuiteId = input.GeologySuiteId,
                                DrillHole = worksheet.Cells[row, 1].Text,
                                AttributeName = field,
                                AttributeValue = worksheet.Cells[row, columnIndex].Text,
                                GroupId = uuid,
                            });
                    }

                    break;
                }
                case ".csv":
                {
                    using var reader = new StreamReader(stream);
                    await reader.ReadLineAsync();

                    while (await reader.ReadLineAsync() is { } line)
                    {
                        var uuid = Guid.NewGuid().ToString();

                        var values = line.Split(',');
                        var drillHole = values[0].Trim();

                        dataToInsert.AddRange(from field in providedFields
                            where attributes.Contains(field)
                            let columnIndex = providedFields.IndexOf(field) + 1
                            select new GeologyData
                            {
                                ProjectId = input.ProjectId,
                                GeologySuiteId = input.GeologySuiteId,
                                DrillHole = drillHole,
                                AttributeName = field,
                                AttributeValue = values[columnIndex].Trim(),
                                GroupId = uuid,
                            });
                    }

                    break;
                }
                default:
                    throw new UserFriendlyException("Unsupported file format. Please upload a .xlsx or .csv file.");
            }
        }

        await InsertDownholeDataAsync(dataToInsert);

        await _unitOfWorkManager.Current.SaveChangesAsync();
        return new OkObjectResult("Data uploaded successfully.");
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<IActionResult> ValidateGeologyData([FromForm] ValidateFileUploadGeology input)
    {
        var drillHoleNotFound = new List<string>();

        var listDrillHoleNameExcel =
            await DownholeCommon.ExtractDrillHoleNamesFromExcel(input.ExcelFile, "Hole ID");
        var uniqueDrillHoleNames = listDrillHoleNameExcel.Distinct().ToList();
        var projectDrillHoles = await _drillHoleRepository.GetAll()
            .Where(dh => dh.ProjectId == input.ProjectId)
            .Select(dh => dh.Name)
            .ToListAsync();

        var invalidDrillHoles = uniqueDrillHoleNames
            .Except(projectDrillHoles)
            .ToList();

        if (invalidDrillHoles.Count != 0)
        {
            drillHoleNotFound = invalidDrillHoles;
        }

        var response = new
        {
            drillHoleNotFound
        };
        return new BadRequestObjectResult(response);
    }

    private async Task InsertDownholeDataAsync(List<GeologyData> downholeData)
    {
        using var transaction = _unitOfWorkManager.Begin(TransactionScopeOption.RequiresNew);
        try
        {
            var dbContext = await _dbContextProvider.GetDbContextAsync();
            await dbContext.BulkInsertAsync(downholeData);

            await transaction.CompleteAsync();
        }
        catch (DbUpdateException dbEx)
        {
            throw new Exception("Downhole insert failed", dbEx);
        }
        catch (Exception ex)
        {
            throw new Exception("An error occurred while inserting downhole data", ex);
        }
    }

    private async Task DeleteGeologyDataAsync(int projectId, int geologySuiteId)
    {
        using var transaction = _unitOfWorkManager.Begin(TransactionScopeOption.RequiresNew);
        try
        {
            var dbContext = await _dbContextProvider.GetDbContextAsync();
            // var dataToDelete = dbContext.AbpDownholeDatas.Where(x => x.ProjectId == projectId).ToList();
            // await dbContext.BulkDeleteAsync(dataToDelete);
            await dbContext.Database.ExecuteSqlRawAsync(
                "DELETE FROM public.\"AbpGeologyData\" WHERE \"ProjectId\" = {0} AND \"GeologySuiteId\" = {1}",
                projectId, geologySuiteId);

            await transaction.CompleteAsync();
        }
        catch (DbUpdateException dbEx)
        {
            throw new Exception("Downhole delete failed", dbEx);
        }
        catch (Exception ex)
        {
            throw new Exception("An error occurred while inserting downhole data", ex);
        }
    }
}