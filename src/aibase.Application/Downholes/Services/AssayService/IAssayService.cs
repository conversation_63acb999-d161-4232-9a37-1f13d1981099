using System.Collections.Generic;
using System.Threading.Tasks;
using Abp.Dependency;
using aibase.Downholes.Dto;
using Microsoft.AspNetCore.Mvc;

namespace aibase.Downholes.Services.AssayService;

/// <inheritdoc />
public interface IAssayService : ITransientDependency
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IActionResult> GetAssaysDataByProject(PagedAssayResultRequestDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    Task<IActionResult> UploadAssayData([FromForm] UploadAssayDataDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<int> UploadAssayDataByImportTemplateAsync([FromForm] UploadAssayDataByAssaySuiteDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    Task<int> UpdateAssayDataByImportTemplateAsync(UploadAssayDataByAssaySuiteDto input);
    
    /// <summary>
    ///
    /// </summary>
    /// <returns></returns>
    Task<int> AddAndUpdateAssayDataByImportTemplateAsync(UploadAssayDataByAssaySuiteDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IActionResult> GetAllAssayDataByDrillholeAsync(PagedAssayDataByDrillholeRequestDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<string>> ValidationAssayDataAsync(UploadAssayDataByAssaySuiteDto input);
}