using System.Threading.Tasks;
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using aibase.Downholes.Dto;
using aibase.DownholeSurveys.Dto;
using aibase.DrillHoles.Dto;
using Microsoft.AspNetCore.Mvc;

namespace aibase.Downholes;

/// <inheritdoc />
public interface IDownholeAppService : IApplicationService
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IActionResult> UploadDownholeData([FromForm] UploadDownholeDataDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IActionResult> UploadAssayData([FromForm] UploadAssayDataDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IActionResult> GetAssayData(PagedAssayResultRequestDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IActionResult> GetAllAssayDataByDrillholeAsync(PagedAssayDataByDrillholeRequestDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task CreateGeologyDataPointAsync(CreateGeologyDataDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateGeologyDataPointAsync(UpdateGeologyDataPointDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="groupId"></param>
    /// <returns></returns>
    Task DeleteGeologyDataPointAsync(string groupId);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IActionResult> GetGeologyData(PagedGeologyResultRequestDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IActionResult> UploadGeologyDataAsync([FromForm] UploadGeologyDataDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IActionResult> GetDownholeByProject(PagedDownholeResultRequestDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IActionResult> ValidateDownholeData([FromForm] UploadDownholeDataDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IActionResult> ValidateGeologyData([FromForm] ValidateFileUploadGeology input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagedResultDto<DownholeSurveyDto>> GetDownholeSurveyDataAsync(PagedDownholeSurveyResultRequestDto input);
    
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task CalculateDesurveyAsync(CalculateDesurveyDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagedResultDto<DesurveyResultDto>> GetAllDesurveyResultAsync(PagedDesurveyResultRequestDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagedResultDto<DrillHoleDto>> GetAllDesurveyResultByDrillHoleAsync(GetAllDesurveyResultByDrillHoleDto input);
}