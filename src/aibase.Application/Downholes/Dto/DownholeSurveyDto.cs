using Abp.Application.Services.Dto;
using aibase.DownholeSurveys;
using AutoMapper;

namespace aibase.Downholes.Dto;

/// <inheritdoc />
[AutoMap(typeof(DownholeSurvey))]
public class DownholeSurveyDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public int ProjectId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int DrillHoleId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double Depth { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double Dip { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double Azimuth { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string Type { get; set; } = string.Empty;
}