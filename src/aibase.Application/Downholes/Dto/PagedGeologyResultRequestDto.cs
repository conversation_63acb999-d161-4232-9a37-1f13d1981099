﻿using System.Collections.Generic;
using Abp.Application.Services.Dto;

namespace aibase.Downholes.Dto;

/// <inheritdoc />
public class PagedGeologyResultRequestDto : PagedResultRequestDto
{
    /// <summary>
    /// 
    /// </summary>
    public string? Keyword { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public string? AttributeName { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public int? ProjectId { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public int? GeologySuiteId { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public List<string>? DrillHoleName { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public double? DepthFrom { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public double? DepthTo { get; set; }
}