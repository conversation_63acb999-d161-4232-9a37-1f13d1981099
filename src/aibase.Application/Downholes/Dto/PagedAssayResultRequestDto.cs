﻿using System.Collections.Generic;
using Abp.Application.Services.Dto;

namespace aibase.Downholes.Dto;

/// <inheritdoc />
public class PagedAssayResultRequestDto : PagedResultRequestDto
{
    /// <summary>
    /// 
    /// </summary>
    public string? AttributeName { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public int? ProjectId { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public int? DrillHoleId { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public int? AssaySuitId { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public List<string>? DrillHoleName { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public double? DepthFrom { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public double? DepthTo { get; set; }
}