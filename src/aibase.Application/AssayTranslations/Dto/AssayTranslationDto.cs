using Abp.Application.Services.Dto;
using Abp.AutoMapper;

namespace aibase.AssayTranslations.Dto;

/// <inheritdoc />
[AutoMap(typeof(AssayTranslation))]
public class AssayTranslationDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public string Character { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public double ChangeTo { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool IsActive { get; set; }
}