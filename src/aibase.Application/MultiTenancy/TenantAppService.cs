﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.IdentityFramework;
using Abp.Linq.Extensions;
using Abp.MultiTenancy;
using Abp.Runtime.Session;
using aibase.Authorization.Roles;
using aibase.Authorization.Users;
using aibase.Editions;
using aibase.MultiTenancy.Dto;
using aibase.Polygons;
using AiBase.Services;
using aibase.Settings;
using Microsoft.AspNetCore.Identity;

namespace aibase.MultiTenancy
{
    /// <summary>
    /// TenantAppService is the application service for the <see cref="Tenant"/> entity.
    /// </summary>
    [AbpAuthorize]
    public class TenantAppService :
        AsyncCrudAppService<Tenant, TenantDto, int, PagedTenantResultRequestDto, CreateTenantDto, UpdateTenantDto>,
        ITenantAppService
    {
        private readonly TenantManager _tenantManager;
        private readonly EditionManager _editionManager;
        private readonly UserManager _userManager;
        private readonly RoleManager _roleManager;
        private readonly IAbpZeroDbMigrator _abpZeroDbMigrator;
        private readonly IRepository<Setting, int> _settingRepository;
        private readonly IAbpSession _abpSession;
        private readonly IRepository<Polygon, int> _polygonRepository;
        private readonly IMailer _mailer;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="repository"></param>
        /// <param name="tenantManager"></param>
        /// <param name="editionManager"></param>
        /// <param name="userManager"></param>
        /// <param name="roleManager"></param>
        /// <param name="abpZeroDbMigrator"></param>
        /// <param name="settingRepository"></param>
        /// <param name="polygonRepository"></param>
        /// <param name="abpSession"></param>
        /// <param name="mailer"></param>
        public TenantAppService(
            IRepository<Tenant, int> repository,
            TenantManager tenantManager,
            EditionManager editionManager,
            UserManager userManager,
            RoleManager roleManager,
            IAbpZeroDbMigrator abpZeroDbMigrator,
            IRepository<Setting, int> settingRepository,
            IRepository<Polygon, int> polygonRepository,
            IAbpSession abpSession, IMailer mailer)
            : base(repository)
        {
            _tenantManager = tenantManager;
            _editionManager = editionManager;
            _userManager = userManager;
            _roleManager = roleManager;
            _abpZeroDbMigrator = abpZeroDbMigrator;
            _settingRepository = settingRepository;
            _abpSession = abpSession;
            _mailer = mailer;
            _polygonRepository = polygonRepository;
        }

        /// <summary>
        /// Create a new tenant.
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public override async Task<TenantDto> CreateAsync(CreateTenantDto input)
        {
            CheckCreatePermission();

            const string password = User.DefaultPassword;

            // Create tenant
            var tenant = new Tenant(input.CompanyName.Replace(" ", "_"), input.CompanyName.Replace(" ", "_"))
            {
                Country = input.Country,
                FirstAddress = input.FirstAddress,
                SecondAddress = input.SecondAddress,
                Suburb = input.Suburb,
                PostCode = input.PostCode,
                State = input.State,
                ConnectionString = null,
                DeactivationDate = input.DeactivationDate,
                AccountType = input.AccountType
            };

            var defaultEdition = await _editionManager.FindByNameAsync(EditionManager.DefaultEditionName);
            if (defaultEdition != null)
            {
                tenant.EditionId = defaultEdition.Id;
            }

            await _tenantManager.CreateAsync(tenant);
            await CurrentUnitOfWork.SaveChangesAsync();

            // Create tenant database
            _abpZeroDbMigrator.CreateOrMigrateForTenant(tenant);

            using (CurrentUnitOfWork.DisableFilter(AbpDataFilters.MayHaveTenant))
            {
                var adminRole = _roleManager.Roles.Single(r => r.Name == StaticRoleNames.Tenants.Admin);
                await _roleManager.GrantAllPermissionsAsync(adminRole);

                // Create admin user for the tenant
                var adminUser = User.CreateTenantAdminUser(tenant.Id, input.EmailAddress);
                adminUser.Name = input.EmailAddress.Split("@")[0];
                adminUser.FirstName = input.EmailAddress.Split("@")[0];
                adminUser.IsActive = true;
                adminUser.IsEmailConfirmed = true;

                await _userManager.InitializeOptionsAsync(tenant.Id);
                CheckErrors(await _userManager.CreateAsync(adminUser, password));
                await CurrentUnitOfWork.SaveChangesAsync();

                CheckErrors(await _userManager.AddToRoleAsync(adminUser, adminRole.Name));
                await CurrentUnitOfWork.SaveChangesAsync();

                var token = await _userManager.GeneratePasswordResetTokenAsync(adminUser);
                var resetToken = $"{adminUser.Id}_{token}";

                await _mailer.SendPasswordAdminCreateAccountAsync(
                    input.EmailAddress,
                    adminUser.FirstName,
                    adminUser.LastName,
                    resetToken
                );

                // Create setting account
                var setting = new Setting()
                {
                    TenantId = tenant.Id,
                    ProductName = $"{input.CompanyName}",
                };

                await _settingRepository.InsertAsync(setting);
                await CurrentUnitOfWork.SaveChangesAsync();


                // Create default polygon
                var boxUuid = Guid.NewGuid();
                var defaultBoxPolygon = new Polygon
                {
                    Name = "Box 1",
                    Type = PolygonType.BoundingBox,
                    Description = "Default bounding box",
                    Coordinates =
                        $"[{{\"x\":100,\"y\":100,\"width\":1000,\"height\":1000,\"id\":\"{boxUuid}\",\"type\":\"Box\"}}]",
                    TenantId = tenant.Id,
                };
                await _polygonRepository.InsertAsync(defaultBoxPolygon);
                await CurrentUnitOfWork.SaveChangesAsync();

                var rowUuid = Guid.NewGuid();
                var defaultRowPolygon = new Polygon
                {
                    Name = "Rows 1",
                    Type = PolygonType.BoundingRows,
                    Description = "Default bounding row",
                    Coordinates =
                        $"[{{\"x\":200,\"y\":200,\"width\":800,\"height\":800,\"id\":\"{rowUuid}\",\"type\":\"Row\"}}]",
                    TenantId = tenant.Id,
                };
                await _polygonRepository.InsertAsync(defaultRowPolygon);
                await CurrentUnitOfWork.SaveChangesAsync();
            }

            return MapToEntityDto(tenant);
        }

        /// <inheritdoc />
        public override Task<PagedResultDto<TenantDto>> GetAllAsync(PagedTenantResultRequestDto input)
        {
            using (CurrentUnitOfWork.DisableFilter(AbpDataFilters.MayHaveTenant))
            {
                IQueryable<Tenant> query = _tenantManager.Tenants
                    .WhereIf(_abpSession.TenantId.HasValue, x => x.Id == _abpSession.GetTenantId())
                    .WhereIf(!input.Keyword.IsNullOrWhiteSpace(),
                        x => input.Keyword != null && (x.TenancyName.Contains(input.Keyword) || x.Name.Contains(input.Keyword)))
                    .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
                    .OrderByDescending(r => r.CreationTime);

                // Apply sorting
                if (!string.IsNullOrWhiteSpace(input.SortField) && !string.IsNullOrWhiteSpace(input.SortOrder))
                {
                    query = query.ApplySorting(input.SortField, input.SortOrder, r => r.CreationTime);
                }
                else
                {
                    query = query.OrderByDescending(x => x.CreationTime); // Default sorting
                }

                var tenants = query.Skip(input.SkipCount)
                    .Take(input.MaxResultCount)
                    .Select(x => new TenantDto
                    {
                        Id = x.Id,
                        IsActive = x.IsActive,
                        Country = x.Country,
                        State = x.State,
                        TenancyName = x.TenancyName.Replace("_", " "),
                        EmailAddress = _userManager.Users.FirstOrDefault(u => u.TenantId == x.Id).EmailAddress,
                        DeactivationDate = x.DeactivationDate,
                        AccountType = x.AccountType,
                        CreateTime = x.CreationTime
                    })
                    .ToList();

                var result = new PagedResultDto<TenantDto>(query.Count(), ObjectMapper.Map<List<TenantDto>>(tenants));
                return Task.FromResult(result);
            }
        }

        /// <inheritdoc />
        protected override void MapToEntity(UpdateTenantDto updateInput, Tenant entity)
        {
            entity.Name = updateInput.TenancyName?.Replace(" ", "_") ?? entity.Name;
            entity.TenancyName = updateInput.TenancyName?.Replace(" ", "_") ?? entity.TenancyName;
            entity.IsActive = updateInput.IsActive ?? entity.IsActive;
            entity.Country = updateInput.Country ?? entity.Country;
            entity.State = updateInput.State ?? entity.State;
            entity.PostCode = updateInput.PostCode ?? entity.PostCode;
            entity.Suburb = updateInput.Suburb ?? entity.Suburb;
            entity.FirstAddress = updateInput.FirstAddress ?? entity.FirstAddress;
            entity.SecondAddress = updateInput.SecondAddress ?? entity.SecondAddress;
            entity.AccountType = updateInput.AccountType ?? entity.AccountType;
            entity.DeactivationDate = updateInput.DeactivationDate;
        }

        /// <inheritdoc />
        public override async Task<TenantDto> UpdateAsync(UpdateTenantDto input)
        {
            using (CurrentUnitOfWork.DisableFilter(AbpDataFilters.MayHaveTenant))
            {
                var tenant = await _tenantManager.GetByIdAsync(input.Id);

                MapToEntity(input, tenant);

                var setting = await _settingRepository.FirstOrDefaultAsync(x => x.TenantId == input.Id);
                setting.ProductName = input.TenancyName?.Replace("_", " ") ?? setting.ProductName;
                await _settingRepository.UpdateAsync(setting);

                return MapToEntityDto(tenant);
            }
        }

        /// <inheritdoc />
        public override async Task DeleteAsync(EntityDto<int> input)
        {
            //CheckDeletePermission();
            using (CurrentUnitOfWork.SetTenantId(null))
            {
                var tenant = await _tenantManager.GetByIdAsync(input.Id);

                using (CurrentUnitOfWork.SetTenantId(tenant.Id))
                {
                    var users = _userManager.Users.Where(u => u.TenantId == tenant.Id).ToList();

                    foreach (var user in users)
                    {
                        await _userManager.DeleteAsync(user);
                    }

                    await CurrentUnitOfWork.SaveChangesAsync();
                }

                await _tenantManager.DeleteAsync(tenant);
            }
        }

        private void CheckErrors(IdentityResult identityResult)
        {
            identityResult.CheckErrors(LocalizationManager);
        }
    }
}