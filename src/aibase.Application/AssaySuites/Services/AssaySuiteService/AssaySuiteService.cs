using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.AssayAttributes;
using aibase.AssayAttributes.Dto;
using aibase.AssayDatas;
using aibase.AssayProjectSuites;
using aibase.AssaySuiteFields.Dto;
using aibase.AssaySuites.Dto;
using aibase.GeologySuites.Dto;
using aibase.ProjectEntity;
using aibase.ProjectLoggingViews;
using aibase.Projects.Dto;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.AssaySuites.Services.AssaySuiteService;

/// <inheritdoc />
public class AssaySuiteService : IAssaySuiteService
{
    private readonly IRepository<AssaySuite, int> _repository;
    private readonly IRepository<AssayAttribute, int> _assayAttributeRepository;
    private readonly IRepository<AssayData, int> _assayDataRepository;
    private readonly IRepository<ProjectLoggingView, int> _projectLoggingRepository;
    private readonly IRepository<AssayProjectSuite, int> _assayProjectSuiteRepository;
    private readonly IRepository<Project, int> _projectRepository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    public AssaySuiteService(
        IRepository<AssaySuite, int> repository,
        IRepository<AssayAttribute, int> assayAttributeRepository,
        IRepository<ProjectLoggingView, int> projectLoggingRepository,
        IAbpSession abpSession,
        IMapper mapper, IUnitOfWorkManager unitOfWorkManager,
        IRepository<AssayProjectSuite, int> assayProjectSuiteRepository, IRepository<Project, int> projectRepository,
        IRepository<AssayData, int> assayDataRepository)
    {
        _repository = repository;
        _assayAttributeRepository = assayAttributeRepository;
        _projectLoggingRepository = projectLoggingRepository;
        _abpSession = abpSession;
        _mapper = mapper;
        _unitOfWorkManager = unitOfWorkManager;
        _assayProjectSuiteRepository = assayProjectSuiteRepository;
        _projectRepository = projectRepository;
        _assayDataRepository = assayDataRepository;
    }

    /// <inheritdoc />
    public async Task<AssaySuite> CreateAsync(CreateAssaySuiteDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var tenantId = _abpSession.GetTenantId();

        var existingAssaySuite =
            await _repository.FirstOrDefaultAsync(d => d.TenantId == tenantId && d.Name == input.Name);
        if (existingAssaySuite != null)
        {
            throw new UserFriendlyException(
                $"The AssaySuite with the name {existingAssaySuite.Name} already exists.");
        }

        var assaySuite = new AssaySuite()
        {
            Name = input.Name,
            TenantId = tenantId,
        };

        await _repository.InsertAsync(assaySuite);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        if (input.ProjectIds.Count == 0) return assaySuite;

        var assignProject = new AssignProjectAssaySuiteDto
        {
            AssaySuiteId = assaySuite.Id,
            ProjectIds = input.ProjectIds,
        };
        await AssignProjectAssaySuiteAsync(assignProject);

        return assaySuite;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<AssaySuiteDto>> GetAllAsync(PagedAssaySuiteResultRequestDto input)
    {
        var query = _repository.GetAllIncluding(x => x.AssaySuiteFields)
            .AsNoTracking()
            .Include(x => x.AssaySuiteAttributes)
            .ThenInclude(x => x.AssayAttribute)
            .Include(x => x.AssayProjectSuites)
            .ThenInclude(x => x.Project)
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .WhereIf(input.ProjectId.HasValue,
                x => x.AssayProjectSuites.Any(y => input.ProjectId != null && y.ProjectId == input.ProjectId.Value))
            .OrderByDescending(r => r.CreationTime);

        var totalCount = await query.CountAsync();

        var assaySuites = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .Select(x => new AssaySuiteDto
            {
                Id = x.Id,
                Name = x.Name,
                AssayAttributes =
                    _mapper.Map<List<AssayAttributeDto>>(x.AssaySuiteAttributes.Select(a => a.AssayAttribute)),
                Projects = x.AssayProjectSuites.Select(p => new ProjectDto
                {
                    Id = p.Project.Id,
                    Name = p.Project.Name,
                    TextColor = p.Project.TextColor,
                    BackgroundColor = p.Project.BackgroundColor,
                    IsActive = p.Project.IsActive
                }).ToList()
            })
            .ToListAsync();

        return new PagedResultDto<AssaySuiteDto>(totalCount, assaySuites);
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<AssaySuiteDto>> GetAllByLoggingViewAsync(
        PagedGeologySuiteByLoggingViewResultRequestDto input)
    {
        var projectIds = await _projectLoggingRepository.GetAll()
            .AsNoTracking()
            .Where(plv => plv.LoggingViewId == input.LoggingViewId)
            .Select(plv => plv.ProjectId)
            .Distinct()
            .ToListAsync();

        var query = _repository.GetAll()
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(projectIds.Any(),
                x => x.AssayProjectSuites.Any(p => projectIds.Contains(p.ProjectId)))
            .OrderBy(r => r.Name);

        var totalCount = await query.CountAsync();

        var assaySuites = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        var assaySuitesDto = _mapper.Map<List<AssaySuiteDto>>(assaySuites);
        return new PagedResultDto<AssaySuiteDto>(totalCount, assaySuitesDto);
    }

    /// <inheritdoc />
    public async Task<AssaySuiteDto> GetAsync(EntityDto<int> input)
    {
        var assaySuite = await ValidateAssaySuiteEntity(input.Id);

        var relate = await _assayAttributeRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.AssaySuiteAttributes.Any(y => y.AssaySuiteId == assaySuite.Id))
            .Select(x => new AssayAttributeDto()
            {
                Id = x.Id,
                Name = x.Name,
                Code = x.Code,
                TextColor = x.TextColor,
                BackgroundColor = x.BackgroundColor,
                Description = x.Description,
                IsActive = x.IsActive,
            })
            .ToListAsync();

        var relateProject = await _projectRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.AssayProjectSuites.Any(y => y.AssaySuiteId == assaySuite.Id))
            .ToListAsync();

        var assaySuiteDto = new AssaySuiteDto
        {
            Id = assaySuite.Id,
            Name = assaySuite.Name,
            AssayAttributes = relate,
            Projects = _mapper.Map<List<ProjectDto>>(relateProject),
            AssaySuiteFields = _mapper.Map<List<AssaySuiteFieldDto>>(assaySuite.AssaySuiteFields)
        };

        return assaySuiteDto;
    }

    /// <inheritdoc />
    public async Task<AssaySuiteDto> UpdateAsync(UpdateAssaySuiteDto input)
    {
        var assaySuite = await ValidateAssaySuiteEntity(input.Id);

        if (input.ProjectIds is { Count: > 0 })
        {
            var assignProject = new AssignProjectAssaySuiteDto
            {
                AssaySuiteId = assaySuite.Id,
                ProjectIds = input.ProjectIds,
            };
            await AssignProjectAssaySuiteAsync(assignProject);
        }

        assaySuite.Name = input.Name;

        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task DeleteAsync(EntityDto<int> input)
    {
        var assaySuite = await ValidateAssaySuiteEntity(input.Id);

        var isUsedInAssayData =
            await _assayDataRepository.FirstOrDefaultAsync(x => x.AssaySuiteId == assaySuite.Id);

        if (isUsedInAssayData != null)
        {
            throw new UserFriendlyException(
                "Cannot delete Assay Suite because it is associated with one or more AssayData.");
        }

        await _repository.DeleteAsync(input.Id);
    }

    private async Task<AssaySuite> ValidateAssaySuiteEntity(int id)
    {
        var assaySuite =
            await _repository.GetAll()
                .Include(x => x.AssaySuiteFields)
                .ThenInclude(x => x.AssayAttribute)
                .FirstOrDefaultAsync(x => x.Id == id);

        if (assaySuite == null)
        {
            throw new EntityNotFoundException(typeof(AssaySuite), id);
        }

        return assaySuite;
    }

    private async Task AssignProjectAssaySuiteAsync(AssignProjectAssaySuiteDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var existingProjects = await _assayProjectSuiteRepository.GetAllListAsync(x =>
            x.AssaySuiteId == input.AssaySuiteId);

        var existingIds = existingProjects.Select(x => x.ProjectId).ToList();

        var projectsToAdd = input.ProjectIds.Except(existingIds).ToList();
        var projectsToDelete = existingIds.Except(input.ProjectIds).ToList();

        foreach (var assayProjectSuite in projectsToAdd.Select(projectId =>
                     new AssayProjectSuite
                     {
                         ProjectId = projectId,
                         AssaySuiteId = input.AssaySuiteId,
                         TenantId = _abpSession.GetTenantId()
                     }))
        {
            await _assayProjectSuiteRepository.InsertAsync(assayProjectSuite);
        }

        foreach (var projectId in projectsToDelete)
        {
            await _assayProjectSuiteRepository.DeleteAsync(x => x.ProjectId == projectId && x.AssaySuiteId == input.AssaySuiteId);
        }
    }
}