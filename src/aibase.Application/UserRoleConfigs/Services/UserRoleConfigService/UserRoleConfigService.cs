using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.Authorization.Users;
using aibase.UserRoleConfigs.Dto;
using aibase.Users.Dto;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.UserRoleConfigs.Services.UserRoleConfigService;

/// <inheritdoc />
public class UserRoleConfigService : IUserRoleConfigService
{
    private readonly IRepository<UserRoleConfig, int> _repository;
    private readonly IRepository<User, long> _userRepository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="repository"></param>
    /// <param name="userRepository"></param>
    /// <param name="unitOfWorkManager"></param>
    /// <param name="abpSession"></param>
    /// <param name="mapper"></param>
    public UserRoleConfigService(
        IRepository<UserRoleConfig, int> repository,
        IRepository<User, long> userRepository,
        IUnitOfWorkManager unitOfWorkManager,
        IAbpSession abpSession,
        IMapper mapper
        )
    {
        _repository = repository;
        _userRepository = userRepository;
        _unitOfWorkManager = unitOfWorkManager;
        _abpSession = abpSession;
        _mapper = mapper;
    }

    /// <inheritdoc />
    public async Task<UserRoleConfig> CreateAsync(CreateUserRoleConfigDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var userRoleConfig = new UserRoleConfig
        {
            Name = input.Name,
            Description = input.Description,
            Functions = input.Functions ?? [],
            IsActive = input.IsActive,
            TenantId = _abpSession.GetTenantId(),
        };
        await _repository.InsertAsync(userRoleConfig);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        return userRoleConfig;
    }

    /// <inheritdoc />
    public async Task<UserRoleConfigDto> UpdateAsync(UpdateUserRoleConfigDto input)
    {
        var userRoleConfig = await ValidateUserRoleConfigEntity(input.Id);

        userRoleConfig.Name = input.Name ?? userRoleConfig.Name;
        userRoleConfig.Description = input.Description ?? userRoleConfig.Description;
        userRoleConfig.IsActive = input.IsActive ?? userRoleConfig.IsActive;
        userRoleConfig.Functions = input.Functions ?? userRoleConfig.Functions;

        await _unitOfWorkManager.Current.SaveChangesAsync();

        return await GetAsync(input.Id);
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<UserRoleConfigDto>> GetAllAsync(PagedUserRoleConfigResultRequestDto input)
    {
        var query = _repository.GetAll()
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .OrderBy(r => r.Name);

        var totalCount = await query.CountAsync();

        var userRoleConfigs = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        return new PagedResultDto<UserRoleConfigDto>(totalCount, _mapper.Map<List<UserRoleConfigDto>>(userRoleConfigs));
    }

    /// <inheritdoc />
    public async Task<UserRoleConfigDto> GetAsync(int id)
    {
        var userRoleConfig = await ValidateUserRoleConfigEntity(id);
        return _mapper.Map<UserRoleConfigDto>(userRoleConfig);
    }

    /// <inheritdoc />
    public async Task AddUserAsync(AddRemoveUserDto input)
    {
        foreach (var userId in input.UserIds)
        {
            var user = await _userRepository.GetAsync(userId);
            user.UserRoleConfigId = input.UserRoleConfigId;
            
            await _userRepository.UpdateAsync(user);
        }
    }

    /// <inheritdoc />
    public async Task RemoveUserAsync(AddRemoveUserDto input)
    {
        foreach (var userId in input.UserIds)
        {
            var user = await _userRepository.GetAsync(userId);
            user.UserRoleConfigId = null;
            
            await _userRepository.UpdateAsync(user);
        }
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<UserDto>> GetUsersByRoleAsync(PagedUserByRoleResultRequestDto input)
    {
        var query = _userRepository.GetAll()
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .Where(x => x.UserRoleConfigId == input.UserRoleConfigId);
            
        var totalCount = await query.CountAsync();

        var users = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        return new PagedResultDto<UserDto>(totalCount, _mapper.Map<List<UserDto>>(users));
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<UserDto>> GetUsersExceptRoleAsync(PagedUserByRoleResultRequestDto input)
    {
        var query = _userRepository.GetAll()
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .Where(x => x.UserRoleConfigId != input.UserRoleConfigId);
            
        var totalCount = await query.CountAsync();

        var users = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();
        
        return new PagedResultDto<UserDto>(totalCount, _mapper.Map<List<UserDto>>(users));
    }

    private async Task<UserRoleConfig> ValidateUserRoleConfigEntity(int id)
    {
        var userRoleConfig = await _repository.FirstOrDefaultAsync(x => x.Id == id);
        if (userRoleConfig == null)
        {
            throw new EntityNotFoundException(typeof(UserRoleConfig), id);
        }

        return userRoleConfig;
    }
}