using System.Collections.Generic;
using aibase.ImportMappingTemplates;

namespace aibase.ImportDataServices.Dto;

/// <summary>
/// 
/// </summary>
public class SaveTemplateDto
{
    /// <summary>
    /// 
    /// </summary>
    public int? Id { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string Name { get; set; }= string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public ImportFileType ImportFileType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int SuiteId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<MappingFieldDto> Fields { get; set; } = [];
}

/// <summary>
/// 
/// </summary>
public class MappingFieldDto
{
    /// <summary>
    /// 
    /// </summary>
    public string SystemFieldName { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public string FileColumnName { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public int Sequence { get; set; }
}