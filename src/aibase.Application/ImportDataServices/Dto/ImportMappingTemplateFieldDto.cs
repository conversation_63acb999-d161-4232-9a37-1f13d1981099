using Abp.Application.Services.Dto;
using aibase.ImportMappingTemplates;
using AutoMapper;

namespace aibase.ImportDataServices.Dto;

/// <inheritdoc />
[AutoMap(typeof(ImportMappingTemplateField))]
public class ImportMappingTemplateFieldDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public int ImportMappingTemplateId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string SystemFieldName { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public string FileColumnName { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public int Sequence { get; set; }
}