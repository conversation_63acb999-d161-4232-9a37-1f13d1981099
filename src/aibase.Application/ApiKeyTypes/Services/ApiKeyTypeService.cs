using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.APIKeys;
using aibase.ApiKeyTypes.Dto;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.ApiKeyTypes.Services;

/// <inheritdoc />
public class ApiKeyTypeService : IApiKeyTypeService
{
    private readonly IRepository<APIKeyTypes, int> _repository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;


    /// <summary>
    /// 
    /// </summary>
    /// <param name="repository"></param>
    /// <param name="unitOfWorkManager"></param>
    /// <param name="abpSession"></param>
    /// <param name="mapper"></param>
    public ApiKeyTypeService(IRepository<APIKeyTypes, int> repository, IUnitOfWorkManager unitOfWorkManager, IAbpSession abpSession, IMapper mapper)
    {
        _repository = repository;
        _unitOfWorkManager = unitOfWorkManager;
        _abpSession = abpSession;
        _mapper = mapper;
    }

    /// <inheritdoc />
    public async Task<APIKeyTypes> CreateAsync(CreateApiKeyTypeDto input)
    {
        if (_abpSession.TenantId != null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }
        
        var apiKeyTypes = new APIKeyTypes
        {
            Code = input.Code,
            Description = input.Description,
            Prefix = input.Prefix,
        };

        await _repository.InsertAsync(apiKeyTypes);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        return apiKeyTypes;
    }

    /// <inheritdoc />
    public async Task<ApiKeyTypeDto> UpdateAsync(UpdateApiKeyTypeDto input)
    {
        var apiKeyTypes = await ValidateApiKeyTypesEntity(input.Id);
        
        apiKeyTypes.Code = input.Code ?? apiKeyTypes.Code;
        apiKeyTypes.Description = input.Description ?? apiKeyTypes.Description;
        apiKeyTypes.Prefix = input.Prefix ?? apiKeyTypes.Prefix;

        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<ApiKeyTypeDto>> GetAllAsync(PagedApiKeyTypeResultRequestDto input)
    {
        var query = _repository.GetAll()
            .AsNoTracking()
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Description.ToLower().Contains(input.Keyword.ToLower()));

        var totalCount = await query.CountAsync();

        var apiKeyTypes = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        return new PagedResultDto<ApiKeyTypeDto>(totalCount, _mapper.Map<List<ApiKeyTypeDto>>(apiKeyTypes));
    }

    /// <inheritdoc />
    public async Task<ApiKeyTypeDto> GetAsync(EntityDto<int> input)
    {
        var apiKeyTypes = await ValidateApiKeyTypesEntity(input.Id);
        return _mapper.Map<ApiKeyTypeDto>(apiKeyTypes);
    }
    
    private async Task<APIKeyTypes> ValidateApiKeyTypesEntity(int id)
    {
        var apiKeyTypes = await _repository.FirstOrDefaultAsync(x => x.Id == id);

        if (apiKeyTypes == null)
        {
            throw new EntityNotFoundException(typeof(APIKeyTypes), id);
        }

        return apiKeyTypes;
    }
}