using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.SourceTypeWorkflowEntity;
using aibase.SourceTypeWorkflows.Dto;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.SourceTypeWorkflows.Services.SourceTypeWorkflowService;

/// <inheritdoc />
public class SourceTypeWorkflowService : ISourceTypeWorkflowService
{
    private readonly IRepository<SourceTypeWorkflow, int> _repository;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="repository"></param>
    /// <param name="abpSession"></param>
    /// <param name="mapper"></param>
    public SourceTypeWorkflowService(IRepository<SourceTypeWorkflow, int> repository, IAbpSession abpSession, IMapper mapper)
    {
        _repository = repository;
        _abpSession = abpSession;
        _mapper = mapper;
    }

    /// <inheritdoc />
    public async Task<SourceTypeWorkflow> CreateAsync(CreateSourceTypeDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }
        
        var sourceType = new SourceTypeWorkflow()
        {
            Name = input.Name,
            Type = input.Type,
        };

        await _repository.InsertAsync(sourceType);
        return sourceType;
    }

    /// <inheritdoc />
    public async Task<SourceTypeDto> UpdateAsync(SourceTypeDto input)
    {
        var sourceType = await ValidateSourceTypeWorkflowEntity(input.Id);
        
        sourceType.Name = input.Name;
        sourceType.Type = input.Type;
        
        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<SourceTypeDto>> GetAllAsync(PagedSourceTypeResultRequestDto input)
    {
        var query =  _repository.GetAll()
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .OrderByDescending(r => r.CreationTime);
        
        var totalCount = await query.CountAsync();

        var sourceType = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        return new PagedResultDto<SourceTypeDto>(totalCount, _mapper.Map<List<SourceTypeDto>>(sourceType));
    }

    /// <inheritdoc />
    public async Task<SourceTypeDto> GetAsync(EntityDto<int> input)
    {
        var sourceType = await ValidateSourceTypeWorkflowEntity(input.Id);
        return _mapper.Map<SourceTypeDto>(sourceType);
    }
    
    private async Task<SourceTypeWorkflow> ValidateSourceTypeWorkflowEntity(int id)
    {
        var sourceTypeWorkflow = await _repository.FirstOrDefaultAsync(x => x.Id == id);

        if (sourceTypeWorkflow == null)
        {
            throw new EntityNotFoundException(typeof(SourceTypeWorkflow), id);
        }

        return sourceTypeWorkflow;
    }
}