using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.Polygons.Dto;
using aibase.ProjectEntity;
using aibase.Projects.Services;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.Polygons.Services.PolygonService;

/// <inheritdoc />
public class PolygonService : IPolygonService
{
    private readonly IRepository<Polygon, int> _repository;
    private readonly IRepository<Project, int> _projectRepository;
    private readonly IAbpSession _abpSession;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="abpSession"></param>
    /// <param name="repository"></param>
    /// <param name="projectRepository"></param>
    /// <param name="unitOfWorkManager"></param>
    /// <param name="mapper"></param>
    public PolygonService(
        IRepository<Polygon, int> repository,
        IRepository<Project, int> projectRepository,
        IAbpSession abpSession,
        IUnitOfWorkManager unitOfWorkManager,
        IMapper mapper)
    {
        _repository = repository;
        _projectRepository = projectRepository;
        _abpSession = abpSession;
        _unitOfWorkManager = unitOfWorkManager;
        _mapper = mapper;
    }

    /// <inheritdoc />
    public async Task<Polygon> CreateAsync(CreatePolygonDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var polygon = new Polygon
        {
            Name = input.Name,
            Type = input.Type,
            Description = input.Description ?? input.Name,
            Coordinates = input.Coordinates,
            TenantId = _abpSession.GetTenantId(),
        };

        await _repository.InsertAsync(polygon);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        if (input.ProjectId == null) return polygon;

        var project = await _projectRepository.GetAsync((int)input.ProjectId);
        switch (input.Type)
        {
            case PolygonType.BoundingBox:
                project.BoundingBoxIds = ProjectCommon.AddPolygonIdToList(project.BoundingBoxIds, polygon.Id);
                break;
            case PolygonType.BoundingRows:
                project.BoundingRowsIds =
                    ProjectCommon.AddPolygonIdToList(project.BoundingRowsIds, polygon.Id);
                break;
            default:
                throw new ArgumentException("Invalid type value.");
        }

        project.Name = project.Name;
        project.Code = project.Code;
        project.Description = project.Description;
        project.BackgroundColor = project.BackgroundColor;
        project.TextColor = project.TextColor;
        project.BoundingBoxIds = project.BoundingBoxIds;
        project.BoundingRowsIds = project.BoundingRowsIds;

        await _projectRepository.UpdateAsync(project);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        return polygon;
    }

    /// <inheritdoc />
    public Task<PagedResultDto<PolygonDto>> GetAllAsync(PagedPolygonResultRequestDto input)
    {
        var query = _repository.GetAll()
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(),
                x => input.Keyword != null && (x.Name.ToLower().Contains(input.Keyword.ToLower()) ||
                                               x.Description.ToLower().Contains(input.Keyword.ToLower())))
            .WhereIf(input.Type.HasValue, x => x.Type == input.Type)
            .OrderByDescending(r => r.CreationTime);

        var totalCount = query.Count();

        var polygon = query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToList();

        return Task.FromResult(new PagedResultDto<PolygonDto>(totalCount, _mapper.Map<List<PolygonDto>>(polygon)));
    }

    /// <inheritdoc />
    public async Task<PolygonDto> GetAsync(EntityDto<int> input)
    {
        var polygon = await ValidatePolygonEntity(input.Id);
        return _mapper.Map<PolygonDto>(polygon);
    }

    /// <inheritdoc />
    public async Task<PolygonDto> UpdateAsync(UpdatePolygonDto input)
    {
        var polygon = await ValidatePolygonEntity(input.Id);
        
        polygon.Name = input.Name ?? polygon.Name;
        polygon.Type = input.Type ?? polygon.Type;
        polygon.Description = input.Description ?? polygon.Description;
        polygon.Coordinates = input.Coordinates ?? polygon.Coordinates;
        
        return await GetAsync(input);
    }
    
    private async Task<Polygon> ValidatePolygonEntity(int id)
    {
        var polygon = await _repository.FirstOrDefaultAsync(x => x.Id == id);
        if (polygon == null)
        {
            throw new EntityNotFoundException(typeof(Polygon), id);
        }
        
        return polygon;
    }
}