using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Dependency;
using aibase.RqdCalculations.Dto.CalculationRqd;

namespace aibase.RqdCalculations.Services.CalculationRqdService;

/// <summary>
/// 
/// </summary>
public interface ICalculationRqdService : ITransientDependency
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagedResultDto<RqdCalculationResultDto>> GetRqdCalculationResultAsync(PagedRqdCalculationResultResultRequestDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagedResultDto<RqdPercentResultDto>> GetRqdCalculationResultByDrillHoleAsync(PagedRqdCalculationByDrillHoleResultRequestDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task ExecuteRqdCalculationByDrillHoleAsync(ExecuteRqdCalculationDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task CalculateRqdByDrillHoleAsync(ExecuteRqdCalculationDto input);
}