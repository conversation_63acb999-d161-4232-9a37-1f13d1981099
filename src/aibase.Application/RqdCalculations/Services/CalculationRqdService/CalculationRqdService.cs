using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.EntityFrameworkCore.Repositories;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.DrillHoleEntity;
using aibase.DrillHoles.Dto.CalculationRecovery;
using aibase.DrillHoles.Services.RecoveryService.Handler;
using aibase.GeotechDatas;
using aibase.ImageCrops.Dto;
using aibase.ImageEntity;
using aibase.Images.Dto;
using aibase.Images.Services.UploadService.Handler;
using aibase.ImageSubtypes.Dto;
using aibase.ImageTypes.Dto;
using aibase.RockLines;
using aibase.RqdCalculations.Dto.CalculationRqd;
using aibase.RqdCalculations.Services.CalculationRqdService.Handler;
using aibase.RqdPercentResults;
using aibase.Structures;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace aibase.RqdCalculations.Services.CalculationRqdService;

/// <inheritdoc />
public class CalculationRqdService : ICalculationRqdService
{
    private readonly IRepository<RqdCalculationProject, int> _rqdCalculationProjectRepository;
    private readonly IRepository<RqdCalculationResult, int> _rqdCalculationResultRepository;
    private readonly IRepository<Image, int> _imageRepository;
    private readonly IRepository<DrillHole, int> _drillholeRepository;
    private readonly IRepository<GeotechData, int> _geotechDataRepository;
    private readonly IRepository<RqdCalculation, int> _rqdCalculationRepository;
    private readonly IRepository<RqdPercentResult, int> _rqdPercentResultRepository;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    public CalculationRqdService(
        IRepository<RqdCalculationProject, int> rqdCalculationProjectRepository,
        IRepository<RqdCalculationResult, int> rqdCalculationResultRepository,
        IRepository<Image, int> imageRepository,
        IRepository<DrillHole, int> drillholeRepository,
        IRepository<GeotechData, int> geotechDataRepository,
        IAbpSession abpSession,
        IMapper mapper, IRepository<RqdCalculation, int> rqdCalculationRepository, 
        IRepository<RqdPercentResult, int> rqdPercentResultRepository)
    {
        _rqdCalculationProjectRepository = rqdCalculationProjectRepository;
        _imageRepository = imageRepository;
        _drillholeRepository = drillholeRepository;
        _geotechDataRepository = geotechDataRepository;
        _abpSession = abpSession;
        _rqdCalculationResultRepository = rqdCalculationResultRepository;
        _mapper = mapper;
        _rqdCalculationRepository = rqdCalculationRepository;
        _rqdPercentResultRepository = rqdPercentResultRepository;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<RqdCalculationResultDto>> GetRqdCalculationResultAsync(
        PagedRqdCalculationResultResultRequestDto input)
    {
        var query = _rqdCalculationResultRepository.GetAllIncluding(x => x.RqdCalculation, x => x.DrillHole)
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .Where(x => x.RqdCalculationId == input.RqdCalculationId)
            .WhereIf(input.DrillHoleId.HasValue,
                x => x.DrillHoleId == input.DrillHoleId);

        var totalCount = await query.CountAsync();

        var rqdCalculationResults = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        return new PagedResultDto<RqdCalculationResultDto>(totalCount,
            _mapper.Map<List<RqdCalculationResultDto>>(rqdCalculationResults));
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<RqdPercentResultDto>> GetRqdCalculationResultByDrillHoleAsync(PagedRqdCalculationByDrillHoleResultRequestDto input)
    {
        // Find the RQD calculation where IsDisplay is true and CountingMethod is RockSegments for the current tenant
        var displayRqdCalculation = await _rqdCalculationRepository.GetAll()
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .Where(x => x.CountingMethod == CountingMethod.RockSegments && 
                         x.IsDisplay == true)
            .FirstOrDefaultAsync();

        if (displayRqdCalculation == null)
        {
            throw new UserFriendlyException("No RQD calculation with display enabled found. Please set a RockSegments calculation as display.");
        }
        
        var rqdCalculationId = input.RqdCalculationId ?? displayRqdCalculation.Id;

        // Use the found RQD calculation ID along with the provided DrillHoleId to retrieve results
        var query = _rqdPercentResultRepository.GetAllIncluding(x => x.DrillHole, x=> x.RqdCalculation)
            .AsNoTracking()
            .Where(x => x.RqdCalculationId == rqdCalculationId && 
                         x.DrillHoleId == input.DrillHoleId);

        var totalCount = await query.CountAsync();
        
        var rqdCalculationResults = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        return new PagedResultDto<RqdPercentResultDto>(totalCount, _mapper.Map<List<RqdPercentResultDto>>(rqdCalculationResults));
    }

    /// <inheritdoc />
    public async Task ExecuteRqdCalculationByDrillHoleAsync(ExecuteRqdCalculationDto input)
    {
        var drillhole = await _drillholeRepository.FirstOrDefaultAsync(x => x.Id == input.DrillHoleId);
        if (drillhole == null)
        {
            throw new UserFriendlyException("Drillhole not found");
        }

        var rqdCalculations = await _rqdCalculationProjectRepository.GetAll()
            .AsNoTracking()
            .Include(x => x.RqdCalculation)
            .ThenInclude(x => x.Structure)
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(input.RqdCalculationId.HasValue, x => x.RqdCalculationId == input.RqdCalculationId)
            .Where(x => x.ProjectId == drillhole.ProjectId)
            .Select(x => x.RqdCalculation)
            .Distinct()
            .ToListAsync();
        var rqdCalculationIds = rqdCalculations.Select(x => x.Id).ToList();

        await _rqdCalculationResultRepository.DeleteAsync(c =>
            rqdCalculationIds.Contains(c.RqdCalculationId) && c.DrillHoleId == drillhole.Id);

        var rqdStructureIds = rqdCalculations.Select(x => x.StructureId).Distinct().ToList();

        var geotechDataAll = await _geotechDataRepository.GetAll()
            .Where(x => rqdStructureIds.Contains(x.StructureId) && x.DrillHoleId == drillhole.Id)
            .ToListAsync();
        
        var images = await _imageRepository.GetAll()
            .AsNoTracking()
            .Include(x => x.ImageType)
            .Include(x => x.CroppedImages)
            .ThenInclude(x => x.RockLines)
            .Where(x => x.DrillHoleId == drillhole.Id && x.ImageType != null && x.ImageType.IsStandard)
            .Select(x => new ImageDto()
            {
                Id = x.Id,
                DepthFrom = x.DepthFrom,
                DepthTo = x.DepthTo,
                CroppedImages = _mapper.Map<List<ImageCropDto>>(x.CroppedImages),
                OcrResult = x.OcrResult
            })
            .OrderBy(x => x.DepthFrom)
            .ToListAsync();
        if (images.Count == 0)
        {
            throw new UserFriendlyException("No standard images found");
        }

        var depthFromDrillhole = images.Min(x => x.DepthFrom);
        var depthToDrillhole = images.Max(x => x.DepthTo);

        var rowsByImage = new List<List<RowSegmentRqdDto>>();
        foreach (var image in images)
        {
            // Image crop
            var imageCrops = image.CroppedImages
                .Where(x => x.Type is ImageConstSettings.ImageCropRow or ImageConstSettings.ImageCropRowLower)
                .OrderBy(x => x.DepthFrom)
                .ToList();
            // var rockLines = imageCrops.SelectMany(x => x.RockLines).ToList();
            var imageCropCoordinates = imageCrops.Select(x => x.Coordinate).ToList();
            var boundingRows = imageCropCoordinates.OfType<string>()
                .Select(JsonConvert.DeserializeObject<CoordinateBoundingDto>)
                .OfType<CoordinateBoundingDto>().ToList();
            
            if (boundingRows.Count == 0)
            {
                continue;
            }

            // OCR
            var ocrResult = JsonConvert.DeserializeObject<List<OcrResultRqdDto>>(image.OcrResult ?? "[]")
                            ?? [];

            for (var i = 0; i < imageCrops.Count; i++)
            {
                var coordinateImageCrop =
                    JsonConvert.DeserializeObject<CoordinateBoundingDto>(imageCrops[i].Coordinate ?? "") ??
                    new CoordinateBoundingDto();
                var selectedY = coordinateImageCrop.Y + coordinateImageCrop.Height / 2;

                foreach (var geotech in geotechDataAll)
                {
                    if (imageCrops[i].Id == geotech.ImageCropId)
                    {
                        var flagGeotechData = new OcrResultRqdDto
                        {
                            GeotechDataId = geotech.Id,
                            Type = "geotech",
                            X = geotech.X,
                            Y = selectedY,
                            Width = 1,
                            Height = 1,
                            RowIndex = i,
                            Text = $"{image.DepthFrom}",
                        };
                        ocrResult.Add(flagGeotechData);
                    }

                    if (geotech.ImageCropIdTo != null && imageCrops[i].Id == geotech.ImageCropIdTo)
                    {
                        var flagGeotechData = new OcrResultRqdDto
                        {
                            GeotechDataId = geotech.Id,
                            Type = "geotech",
                            X = (double)geotech.XTo,
                            Y = selectedY,
                            Width = 1,
                            Height = 1,
                            RowIndex = i,
                            Text = $"{image.DepthFrom}",
                        };
                        ocrResult.Add(flagGeotechData);
                    }
                }
            }

            var rowSegments = CalculationRqdHandler.MergeDataRowSegmentOcr(boundingRows, ocrResult);

            for (var i = 0; i < imageCrops.Count; i++)
            {
                var boundingSegments = imageCrops[i].RockLines
                    .Where(x => x.Type == RockLineType.Rqd)
                    .Select(x => new CoordinateV2Dto
                    {
                        X = Math.Min(x.StartX, x.EndX) + rowSegments[i].X,
                        Y = (rowSegments[i].Y + rowSegments[i].Height) / 2,
                        Width = Math.Abs(x.EndX - x.StartX),
                        Height = 1
                    }).ToList();
                rowSegments[i].BoundingSegment = boundingSegments;
                rowSegments[i].DepthFrom = imageCrops[i].DepthFrom ?? 0;
                rowSegments[i].DepthTo = imageCrops[i].DepthTo ?? 0;
            }
            rowsByImage.Add(rowSegments);
        }
        var horizontalRows = CalculationRqdHandler.AlignSegmentsHorizontally(rowsByImage);

        var firstRow = horizontalRows.First();
        var lastRow = horizontalRows.Last();

        var firstOcrBlock = new OcrBlockRqdDto
        {
            X = firstRow.X,
            Y = firstRow.Y,
            Width = 100,
            Height = 100,
            Type = "wooden",
            Value = firstRow.DepthFrom
        };
        var lastOcrBlock = new OcrBlockRqdDto
        {
            X = lastRow.X + lastRow.Width,
            Y = lastRow.Y,
            Width = 100,
            Height = 100,
            Type = "wooden",
            Value = lastRow.DepthTo
        };

        var horizontalOcrResults = horizontalRows
            .SelectMany(row => row.OcrResults)
            .ToList();

        var horizontalOcrBlocks = horizontalOcrResults
            .Where(item =>
                double.TryParse(item.text, NumberStyles.Any, CultureInfo.InvariantCulture, out _))
            .Select(item => new OcrBlockRqdDto
            {
                X = item.x,
                Y = item.y,
                Width = item.width,
                Height = item.height,
                Type = item.type,
                Value = double.Parse(item.text, CultureInfo.InvariantCulture),
                GeotechDataId = item.GeotechDataId
            })
            .Where(ob => ob.Value >= firstOcrBlock.Value && ob.Value <= lastOcrBlock.Value)
            .ToList();

        var fullHorizontalOcrBlocks = new List<OcrBlockRqdDto>();
        if (horizontalOcrBlocks.Count > 0 && (horizontalOcrBlocks[0].Value > firstOcrBlock.Value ||
                                              horizontalOcrBlocks[0].Type != firstOcrBlock.Type))
        {
            fullHorizontalOcrBlocks.Add(firstOcrBlock);
        }

        fullHorizontalOcrBlocks.AddRange(horizontalOcrBlocks);
        if (horizontalOcrBlocks.Count > 0 && (horizontalOcrBlocks[^1].Value < lastOcrBlock.Value ||
                                              horizontalOcrBlocks[0].Type != firstOcrBlock.Type))
        {
            fullHorizontalOcrBlocks.Add(lastOcrBlock);
        }

        foreach (var rqdCalculation in rqdCalculations)
        {
            // Rock Segments
            var boundingSegments = horizontalRows.SelectMany(x => x.BoundingSegment).ToList();

            var minimumSegmentLength = 0.0;
            if (rqdCalculation.MinimumWidth != null)
            {
                var depth = depthToDrillhole - depthFromDrillhole;
                var depthRatio = (double)rqdCalculation.MinimumWidth / depth;
                minimumSegmentLength = depthRatio * (lastRow.X - firstRow.X);
            }

            var boundingSegmentsFilter = boundingSegments.Where(x => x.Width > minimumSegmentLength).ToList();

            // Structures
            var structureCountType = rqdCalculation.Structure.Selector;

            var structures = fullHorizontalOcrBlocks.Where(x => x.Type == "geotech").ToList();
            var mergeStructures = CalculationRqdHandler.ProcessGeotechData(structures);
            var boundingStructures = mergeStructures.Select(x => new CoordinateV2Dto
            {
                X = x.X,
                Y = x.Y,
                Width = x.Width,
                Height = x.Height,
            }).ToList();

            if (structureCountType == StructureSelector.Points)
            {
                boundingStructures = boundingStructures.Where(x => x.Width <= 1.0).ToList();
            }
            else if (structureCountType == StructureSelector.Interval)
            {
                boundingStructures = boundingStructures.Where(x => x.Width > minimumSegmentLength).ToList();
            }

            var boundingCount = rqdCalculation.CountingMethod == CountingMethod.Structures
                ? boundingStructures
                : boundingSegmentsFilter;

            switch (rqdCalculation.CalculationType)
            {
                case CalculationType.Block:
                {
                    var rqdBlocks = fullHorizontalOcrBlocks
                        .Where(x => x.Type is "wooden" or "Wooden")
                        .Select(x => new RqdBlockDto
                        {
                            X = x.X,
                            Depth = x.Value,
                        }).ToList();

                    await InsertRqdCalculationResults(rqdBlocks, boundingCount, drillhole.Id, rqdCalculation.Id);
                    break;
                }
                case CalculationType.Distance:
                {
                    var intervalDistance = rqdCalculation.Interval;
                    if (rqdCalculation.Interval == null)
                    {
                        throw new UserFriendlyException("Interval is required to calculate type Distance");
                    }

                    var rqdBlocks = new List<RqdBlockDto>
                    {
                        new()
                        {
                            X = firstRow.X,
                            Depth = depthFromDrillhole
                        },
                        new()
                        {
                            X = lastRow.X,
                            Depth = depthToDrillhole
                        }
                    };

                    var interpolatedPoints =
                        CalculationRqdHandler.GenerateInterpolatedPoints(rqdBlocks, (double)intervalDistance);
                    await InsertRqdCalculationResults(interpolatedPoints, boundingCount, drillhole.Id,
                        rqdCalculation.Id);

                    break;
                }
            }
        }
    }

    /// <inheritdoc />
    public async Task CalculateRqdByDrillHoleAsync(ExecuteRqdCalculationDto input)
    {
        var drillHole = await _drillholeRepository.GetAllIncluding(x => x.Project)
                            .FirstOrDefaultAsync(x => x.Id == input.DrillHoleId)
                        ?? throw new EntityNotFoundException(typeof(DrillHole), input.DrillHoleId);
        var coreTray = drillHole.Project.CoreTrayLength;
        
        var rqds = await _rqdCalculationProjectRepository.GetAll()
            .AsNoTracking()
            .Include(x => x.RqdCalculation)
            .ThenInclude(x => x.Structure)
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(input.RqdCalculationId.HasValue, x => x.RqdCalculationId == input.RqdCalculationId)
            .Where(x => x.ProjectId == drillHole.ProjectId && x.RqdCalculation.CalculationType == CalculationType.Block)
            .Select(x => x.RqdCalculation)
            .Distinct()
            .ToListAsync();
        var rqdIds = rqds.Select(x => x.Id).ToList();

        await _rqdPercentResultRepository.DeleteAsync(c =>
            rqdIds.Contains(c.RqdCalculationId) && c.DrillHoleId == drillHole.Id);
        
        var imageAll = await _imageRepository.GetAll()
            .AsNoTracking()
            .Include(x => x.ImageType)
            .Include(x => x.ImageSubtype)
            .Include(x => x.CroppedImages)
            .ThenInclude(x => x.RockLines)
            .Where(x => x.DrillHoleId == input.DrillHoleId &&
                        x.ImageType != null &&
                        x.ImageType.IsStandard &&
                        x.ImageCategory == ImageCategoryNew.Drilling)
            .Select(x => new ImageDto
            {
                Id = x.Id,
                DepthFrom = x.DepthFrom,
                DepthTo = x.DepthTo,
                ImageType = _mapper.Map<ImageTypeDto>(x.ImageType),
                ImageSubtype = _mapper.Map<ImageSubtypeDto>(x.ImageSubtype),
                CroppedImages = _mapper.Map<List<ImageCropDto>>(x.CroppedImages),
                SegmentResult = x.SegmentResult,
                OcrResult = x.OcrResult
            })
            .OrderBy(x => x.DepthFrom)
            .ToListAsync();
        var wetImages = imageAll.Where(x => x.ImageSubtype is { IsWet: true }).ToList();
        var dryImages = imageAll.Where(x => x.ImageSubtype is { IsDry: true }).ToList();
        
        var images = wetImages.Count > 0 ? wetImages : dryImages;
        if (images == null || images.Count == 0)
        {
            throw new UserFriendlyException("No images found for the drill hole.");
        }
        
        var depthFromDrillhole = images.Min(x => x.DepthFrom);
        var depthToDrillhole = images.Max(x => x.DepthTo);

        var rowsByImage = new List<List<RowSegmentDto>>();
        foreach (var image in images)
        {
            var imageCrops = image.CroppedImages
                .Where(x => x.Type is ImageConstSettings.ImageCropRow or ImageConstSettings.ImageCropRowLower)
                .OrderBy(x => x.DepthFrom)
                .ToList();

            var segmentResult =
                JsonConvert.DeserializeObject<List<SegmentResultRecoveryDto>>(image.SegmentResult ?? "[]")
                ?? [];
            var segmentCores = segmentResult.Where(x => x.Class == "core").ToList();

            var ocrResult = JsonConvert.DeserializeObject<List<OcrResultRecoveryDto>>(image.OcrResult ?? "[]")
                            ?? [];

            if (imageCrops.Count == 0 || segmentCores.Count == 0)
            {
                continue;
            }

            var rowSegments = RecoveryHandler.MergeDataRowSegmentOcr(imageCrops, segmentCores, ocrResult);

            for (var i = 0; i < imageCrops.Count; i++)
            {
                var intersectionPoints = imageCrops[i].RockLines
                    .Where(x => x.Type == RockLineType.Rqd)
                    .Select(x => new LineCoordinateDto
                    {
                        StartX = x.StartX + rowSegments[i].X,
                        EndX = x.EndX + rowSegments[i].X,
                    }).ToList();
                rowSegments[i].IntersectionPoints = intersectionPoints;
                rowSegments[i].DepthFrom = imageCrops[i].DepthFrom ?? 0;
                rowSegments[i].DepthTo = imageCrops[i].DepthTo ?? 0;
            }

            rowsByImage.Add(rowSegments);
        }
        
        var horizontalRows = RecoveryHandler.AlignSegmentsHorizontally(rowsByImage);
        var verticalRows = RecoveryHandler.AlignSegmentsVertical(rowsByImage);
        
        if (verticalRows == null || verticalRows.Count == 0)
        {
            throw new UserFriendlyException("No vertical row segments found for calculation.");
        }

        var firstRow = horizontalRows.First();
        var lastRow = horizontalRows.Last();

        foreach (var rqd in rqds)
        {
            var minimumSegmentLength = 0.0;
            if (rqd.MinimumWidth != null)
            {
                var depth = depthToDrillhole - depthFromDrillhole;
                var depthRatio = (double)rqd.MinimumWidth / depth;
                minimumSegmentLength = depthRatio * (lastRow.X - firstRow.X);
            }
            
            var filterHorizontalRows = horizontalRows.Select(item => new RowSegmentDto
            {
                X = item.X,
                Y = item.Y,
                Width = item.Width,
                Height = item.Height,
                DepthFrom = item.DepthFrom,
                DepthTo = item.DepthTo,
                Id = item.Id,
                OcrResults = item.OcrResults,
                MiddleLine = item.MiddleLine,
                Points = item.Points,
                IntersectionPoints = item.IntersectionPoints.Where(bs => bs.EndX - bs.StartX > minimumSegmentLength).ToList(),
                BoundingSegment = item.BoundingSegment
            }).ToList();
            var filterVerticalRows = verticalRows.Select(item => new RowSegmentDto
            {
                X = item.X,
                Y = item.Y,
                Width = item.Width,
                Height = item.Height,
                DepthFrom = item.DepthFrom,
                DepthTo = item.DepthTo,
                Id = item.Id,
                OcrResults = item.OcrResults,
                MiddleLine = item.MiddleLine,
                Points = item.Points,
                IntersectionPoints = item.IntersectionPoints.Where(bs => bs.EndX - bs.StartX > minimumSegmentLength).ToList(),
                BoundingSegment = item.BoundingSegment
            }).ToList();
            
            var boundingSegmentsFilter = filterHorizontalRows
                .SelectMany(x => x.IntersectionPoints)
                .Where(x => x.EndX - x.StartX > minimumSegmentLength)
                .ToList();
            
            var firstOcrBlock = new OcrBlockRecoveryDto
            {
                X = firstRow.X,
                Y = firstRow.Y,
                Width = 100,
                Height = 100,
                Value = firstRow.DepthFrom
            };
            var lastOcrBlock = new OcrBlockRecoveryDto
            {
                X = lastRow.X + lastRow.Width,
                Y = lastRow.Y,
                Width = 100,
                Height = 100,
                Value = lastRow.DepthTo
            };
            
            var horizontalOcrResults = filterHorizontalRows
                .SelectMany(row => row.OcrResults)
                .Where(r => r is { Type: "wooden" or "Wooden" })
                .ToList();

            var verticalOcrResults = filterVerticalRows
                .SelectMany(row => row.OcrResults)
                .Where(r => r is { Type: "wooden" or "Wooden" })
                .ToList();
            
            var horizontalOcrBlocks = horizontalOcrResults
                .Where(item => double.TryParse(item.Text, NumberStyles.Any, CultureInfo.InvariantCulture, out _))
                .Select(item => new OcrBlockRecoveryDto
                {
                    X = item.X,
                    OriginalX = item.OriginalX,
                    Y = item.Y,
                    Width = item.Width,
                    Height = item.Height,
                    Value = double.Parse(item.Text, CultureInfo.InvariantCulture),
                    RowIndex = item.RowIndex ?? -1,
                    ImageCropId = item.ImageCropId
                })
                .Where(ob => ob.Value >= firstOcrBlock.Value && ob.Value <= lastOcrBlock.Value)
                .ToList();
            var verticalOcrBlocks = verticalOcrResults
                .Where(item => double.TryParse(item.Text, NumberStyles.Any, CultureInfo.InvariantCulture, out _))
                .Select(item => new OcrBlockRecoveryDto
                {
                    X = item.X,
                    OriginalX = item.OriginalX,
                    Y = item.Y,
                    Width = item.Width,
                    Height = item.Height,
                    Value = double.Parse(item.Text, CultureInfo.InvariantCulture),
                    RowIndex = item.RowIndex ?? -1,
                    ImageCropId = item.ImageCropId,
                    id = item.Id
                })
                .Where(ob => ob.Value >= firstOcrBlock.Value && ob.Value <= lastOcrBlock.Value)
                .ToList();
            
            var fullHorizontalOcrBlocks = new List<OcrBlockRecoveryDto>();
            if (horizontalOcrBlocks.Count > 0 && horizontalOcrBlocks[0].Value > firstOcrBlock.Value)
            {
                fullHorizontalOcrBlocks.Add(firstOcrBlock);
            }

            fullHorizontalOcrBlocks.AddRange(horizontalOcrBlocks);
            if (horizontalOcrBlocks.Count > 0 && horizontalOcrBlocks[^1].Value < lastOcrBlock.Value)
            {
                fullHorizontalOcrBlocks.Add(lastOcrBlock);
            }

            var fullVerticalOcrBlocks = new List<OcrBlockRecoveryDto>();
            if (verticalOcrBlocks.Count > 0 && verticalOcrBlocks[0].Value > firstOcrBlock.Value)
            {
                fullVerticalOcrBlocks.Add(firstOcrBlock);
            }

            fullVerticalOcrBlocks.AddRange(verticalOcrBlocks);
            if (verticalOcrBlocks.Count > 0 && verticalOcrBlocks[^1].Value < lastOcrBlock.Value)
            {
                fullVerticalOcrBlocks.Add(lastOcrBlock);
            }
            
            var filterVerticalOcrBlocks = RecoveryHandler.FilterValidData(fullVerticalOcrBlocks);
            var filterHorizontalOcrBlocks = RecoveryHandler.FilterValidData(fullHorizontalOcrBlocks);

            var lengthRows = RecoveryHandler.SegmentLengthRows(filterHorizontalRows, filterHorizontalOcrBlocks, coreTray);
            var resultDepths = RecoveryHandler.CalculateDepths(filterHorizontalOcrBlocks);
            var resultLengths = RecoveryHandler.CalculateLengths(lengthRows);

            if (filterVerticalOcrBlocks.Count != resultLengths.Count + 1 || resultDepths.Count != resultLengths.Count)
            {
                throw new UserFriendlyException("Mismatch between calculated segments and OCR blocks.");
            }
            
            var rqdPercentResults = new List<RqdPercentResult>();
            for (var i = 0; i < resultLengths.Count; i++)
            {
                var fromDepth = filterHorizontalOcrBlocks[i].X;
                var toDepth = filterHorizontalOcrBlocks[i + 1].X;
                
                var count = boundingSegmentsFilter.Count(b => b.StartX >= fromDepth && b.EndX <= toDepth);
                var recoveryPercentage = resultLengths[i] == 0 ? 0 : (resultLengths[i] / resultDepths[i]) * 100;

                var recoveryResult = new RqdPercentResult
                {
                    DrillHoleId = input.DrillHoleId,
                    OcrValueFrom = filterVerticalOcrBlocks[i].Value,
                    FromX = filterHorizontalOcrBlocks[i].OriginalX,
                    FromY = filterHorizontalOcrBlocks[i].Y,
                    FromImageCropId = filterHorizontalOcrBlocks[i].ImageCropId,
                    FromRowIndex = filterVerticalOcrBlocks[i].RowIndex,
                    FromOcrId = filterVerticalOcrBlocks[i].id ?? "",
                    OcrValueTo = filterVerticalOcrBlocks[i + 1].Value,
                    ToX = filterHorizontalOcrBlocks[i + 1].OriginalX,
                    ToY = filterHorizontalOcrBlocks[i + 1].Y,
                    ToImageCropId = filterHorizontalOcrBlocks[i + 1].ImageCropId,
                    ToRowIndex = filterVerticalOcrBlocks[i + 1].RowIndex,
                    ToOcrId = filterVerticalOcrBlocks[i + 1].id ?? "",
                    Length = resultLengths[i],
                    DepthInterval = resultDepths[i],
                    Total = recoveryPercentage,
                    RqdCalculationId = rqd.Id,
                    NumberOfPieces = count
                };

                rqdPercentResults.Add(recoveryResult);
            }
            
            var filteredItems = rqdPercentResults.Where(x => !x.OcrValueFrom.Equals(x.OcrValueTo)).ToList();
            await _rqdPercentResultRepository.InsertRangeAsync(filteredItems);
        }
    }

    private async Task InsertRqdCalculationResults(List<RqdBlockDto> blocks, List<CoordinateV2Dto> rockSegments,
        int drillHoleId,
        int rqdCalculationId)
    {
        for (var i = 0; i < blocks.Count - 1; i++)
        {
            var fromDepth = blocks[i].X;
            var toDepth = blocks[i + 1].X;

            var count = rockSegments.Count(b => b.X + b.Width >= fromDepth && b.X + b.Width <= toDepth);

            var rqdCalculationResult = new RqdCalculationResult
            {
                DrillHoleId = drillHoleId,
                RqdCalculationId = rqdCalculationId,
                FromDepth = blocks[i].Depth,
                ToDepth = blocks[i + 1].Depth,
                Total = count,
                TenantId = _abpSession.GetTenantId()
            };
            await _rqdCalculationResultRepository.InsertAsync(rqdCalculationResult);
        }
    }
}
