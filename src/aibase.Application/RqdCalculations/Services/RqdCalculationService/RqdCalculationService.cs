using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.ProjectEntity;
using aibase.Projects.Dto;
using aibase.RqdCalculations.Dto;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.RqdCalculations.Services.RqdCalculationService;

/// <inheritdoc />
public class RqdCalculationService : IRqdCalculationService
{
    private readonly IRepository<RqdCalculation, int> _repository;
    private readonly IRepository<RqdCalculationProject, int> _rqdCalculationProjectRepository;
    private readonly IRepository<Project, int> _projectRepository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    public RqdCalculationService(
        IRepository<RqdCalculation, int> repository,
        IRepository<RqdCalculationProject, int> rqdCalculationProjectRepository, 
        IRepository<Project, int> projectRepository,
        IUnitOfWorkManager unitOfWorkManager,
        IAbpSession abpSession, 
        IMapper mapper)
    {
        _repository = repository;
        _rqdCalculationProjectRepository = rqdCalculationProjectRepository;
        _unitOfWorkManager = unitOfWorkManager;
        _abpSession = abpSession;
        _mapper = mapper;
        _projectRepository = projectRepository;
    }

    /// <inheritdoc />
    public async Task<RqdCalculation> CreateAsync(CreateRqdCalculationDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var tenantId = _abpSession.GetTenantId();

        var existingRqdCalculation =
            await _repository.FirstOrDefaultAsync(d => d.TenantId == tenantId && d.Name == input.Name);
        if (existingRqdCalculation != null)
        {
            throw new UserFriendlyException(
                $"The Rqd Calculation with the name {existingRqdCalculation.Name} already exists.");
        }

        // Handle IsDisplay logic for RockSegments counting method
        if (input.IsDisplay == true)
        {
            await SetAllOtherRockSegmentsCalculationsAsNotDisplayAsync(tenantId);
        }

        var rqdCalculation = new RqdCalculation
        {
            Name = input.Name,
            IsActive = input.IsActive,
            CalculationType = input.CalculationType,
            CountingMethod = CountingMethod.RockSegments,
            Interval = input.Interval,
            StructureId = input.StructureTypeId,
            MinimumWidth = input.MinimumWidth,
            IsDisplay = input.IsDisplay,
            TenantId = tenantId,
        };

        await _repository.InsertAsync(rqdCalculation);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        if (input.ProjectIds.Count == 0) return rqdCalculation;
        var assignProject = new AssignRqdCalculationProjectDto
        {
            RqdCalculationId = rqdCalculation.Id,
            ProjectIds = input.ProjectIds,
        };
        await AssignProjectAsync(assignProject);

        return rqdCalculation;
    }

    /// <inheritdoc />
    public async Task<RqdCalculationDto> UpdateAsync(UpdateRqdCalculationDto input)
    {
        var rqdCalculation = await ValidateRqdCalculationEntity(input.Id);

        if (input.IsActive == false)
        {
            var rqdCalculationUsed =
                await _rqdCalculationProjectRepository.FirstOrDefaultAsync(x => x.RqdCalculationId == input.Id);
            if (rqdCalculationUsed != null)
            {
                throw new UserFriendlyException("Cannot deactivate this record because it is already in use.");
            }
        }

        // Handle IsDisplay logic for RockSegments counting method
        if (input.IsDisplay == true)
        {
            await SetAllOtherRockSegmentsCalculationsAsNotDisplayAsync(rqdCalculation.TenantId, input.Id);
        }

        if (input.ProjectIds is { Count: > 0 })
        {
            var assignProject = new AssignRqdCalculationProjectDto
            {
                RqdCalculationId = rqdCalculation.Id,
                ProjectIds = input.ProjectIds,
            };
            await AssignProjectAsync(assignProject);
        }

        rqdCalculation.Name = input.Name ?? rqdCalculation.Name;
        rqdCalculation.IsActive = input.IsActive ?? rqdCalculation.IsActive;
        rqdCalculation.CalculationType = input.CalculationType ?? rqdCalculation.CalculationType;
        rqdCalculation.Interval = input.Interval;
        rqdCalculation.StructureId = input.StructureTypeId ?? rqdCalculation.StructureTypeId;
        rqdCalculation.MinimumWidth = input.MinimumWidth ?? rqdCalculation.MinimumWidth;
        
        // Only set IsDisplay if CountingMethod is RockSegments
        if (rqdCalculation.CountingMethod == CountingMethod.RockSegments)
        {
            rqdCalculation.IsDisplay = input.IsDisplay;
        }
        else
        {
            rqdCalculation.IsDisplay = null;
        }

        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<RqdCalculationDto>> GetAllAsync(PagedRqdCalculationResultRequestDto input)
    {
        var query = _repository.GetAllIncluding(x => x.Structure)
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
            .WhereIf(input.ProjectId.HasValue,
                x => x.RqdCalculationProjects.Any(p => p.ProjectId == input.ProjectId.Value))
            .OrderBy(r => r.Name);

        var totalCount = await query.CountAsync();

        var rqdCalculations = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        return new PagedResultDto<RqdCalculationDto>(totalCount, _mapper.Map<List<RqdCalculationDto>>(rqdCalculations));
    }


    /// <inheritdoc />
    public async Task<RqdCalculationDto> GetAsync(EntityDto<int> input)
    {
        var rqdCalculation = await ValidateRqdCalculationEntity(input.Id);

        var relateProject = await _projectRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.RqdCalculationProjects.Any(y => y.RqdCalculationId == rqdCalculation.Id))
            .ToListAsync();
        var projectsDto = _mapper.Map<List<ProjectDto>>(relateProject);

        var rqdCalculationDto = _mapper.Map<RqdCalculationDto>(rqdCalculation);
        rqdCalculationDto.Projects = projectsDto;

        return rqdCalculationDto;
    }


    private async Task<RqdCalculation> ValidateRqdCalculationEntity(int id)
    {
        var rqdCalculation = await _repository.GetAllIncluding(x => x.Structure)
            .FirstOrDefaultAsync(x => x.Id == id);

        if (rqdCalculation == null)
        {
            throw new EntityNotFoundException(typeof(RqdCalculation), id);
        }

        return rqdCalculation;
    }

    private async Task AssignProjectAsync(AssignRqdCalculationProjectDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var existingProjects = await _rqdCalculationProjectRepository.GetAllListAsync(x =>
            x.RqdCalculationId == input.RqdCalculationId);

        var existingIds = existingProjects.Select(x => x.ProjectId).ToList();

        var projectsToAdd = input.ProjectIds.Except(existingIds).ToList();
        var projectsToDelete = existingIds.Except(input.ProjectIds).ToList();

        foreach (var rqdCalculationProject in projectsToAdd.Select(projectId =>
                     new RqdCalculationProject
                     {
                         ProjectId = projectId,
                         RqdCalculationId = input.RqdCalculationId,
                     }))
        {
            await _rqdCalculationProjectRepository.InsertAsync(rqdCalculationProject);
        }

        foreach (var projectId in projectsToDelete)
        {
            await _rqdCalculationProjectRepository.DeleteAsync(x => x.ProjectId == projectId && x.RqdCalculationId == input.RqdCalculationId);
        }
    }

    /// <summary>
    /// Sets IsDisplay to false for all RqdCalculations with CountingMethod of RockSegments for the specified tenant
    /// </summary>
    /// <param name="tenantId">The tenant ID</param>
    /// <param name="excludeId">Optional ID to exclude from the update (for UpdateAsync scenario)</param>
    private async Task SetAllOtherRockSegmentsCalculationsAsNotDisplayAsync(int tenantId, int? excludeId = null)
    {
        var query = _repository.GetAll()
            .Where(x => x.TenantId == tenantId && x.IsDisplay == true);

        if (excludeId.HasValue)
        {
            query = query.Where(x => x.Id != excludeId.Value);
        }

        var displayRecords = await query.ToListAsync();

        foreach (var record in displayRecords)
        {
            record.IsDisplay = false;
        }
    }
}
