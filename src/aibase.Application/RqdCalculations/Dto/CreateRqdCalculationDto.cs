using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace aibase.RqdCalculations.Dto
{
    /// <summary>
    /// 
    /// </summary>
    public class CreateRqdCalculationDto
    {
        /// <summary>
        /// 
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 
        /// </summary>
        [Required]
        public bool IsActive { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Required]
        public CalculationType CalculationType { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "Interval must be greater than 0")]
        public double? Interval { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int? StructureTypeId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Range(0.01, double.MaxValue, ErrorMessage = "Minimum width must be greater than 0")]
        public decimal? MinimumWidth { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public bool? IsDisplay { get; set; }
        
        /// <summary>
        ///
        /// </summary>
        public List<int> ProjectIds { get; set; } = [];
    }
}