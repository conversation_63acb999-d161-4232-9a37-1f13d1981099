using System.Collections.Generic;
using aibase.Images.Dto;
using aibase.Models.Dto;

namespace aibase.RqdCalculations.Dto.CalculationRqd;

/// <summary>
/// 
/// </summary>
public class RowSegmentRqdDto
{
    /// <summary>
    /// 
    /// </summary>
    public double X { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double Y { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double Width { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double Height { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double DepthFrom { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double DepthTo { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<OcrResultV2Dto> OcrResults { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    public List<CoordinateV2Dto> BoundingSegment { get; set; } = [];
}