namespace aibase.RqdCalculations.Dto.CalculationRqd;

/// <summary>
/// 
/// </summary>
public class OcrResultRqdDto
{
    /// <summary>
    /// 
    /// </summary>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public double X { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double Y { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double Width { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double Height { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string Text { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public int? RowIndex { get; set; } = -1;

    /// <summary>
    /// 
    /// </summary>
    public int GeotechDataId { get; set; }
}