namespace aibase.RqdCalculations.Dto.CalculationRqd;

/// <summary>
/// 
/// </summary>
public class OcrBlockRqdDto
{
    /// <summary>
    /// 
    /// </summary>
    public double X { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double Y { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double Width { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double Height { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double Value { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public int GeotechDataId { get; set; }
}