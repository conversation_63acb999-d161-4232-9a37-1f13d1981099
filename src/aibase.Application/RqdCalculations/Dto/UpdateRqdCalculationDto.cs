using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Abp.Application.Services.Dto;
using AutoMapper;

namespace aibase.RqdCalculations.Dto
{
    /// <inheritdoc />
    [AutoMap(typeof(RqdCalculation))]
    public class UpdateRqdCalculationDto : EntityDto<int>
    {
        /// <summary>
        /// 
        /// </summary>
        [StringLength(200)]
        public string? Name { get; set; } = string.Empty;

        /// <summary>
        /// 
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public CalculationType? CalculationType { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "Interval must be greater than 0")]
        public double? Interval { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int? StructureTypeId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Range(0.01, double.MaxValue, ErrorMessage = "Minimum width must be greater than 0")]
        public decimal? MinimumWidth { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public bool? IsDisplay { get; set; }
        
        /// <summary>
        ///
        /// </summary>
        public List<int> ProjectIds { get; set; } = [];
    }
}