using System.Collections.Generic;
using Abp.Application.Services.Dto;
using aibase.Projects.Dto;
using aibase.Structures.Dto;
using aibase.StructureTypes.Dto;
using AutoMapper;

namespace aibase.RqdCalculations.Dto
{
    /// <inheritdoc />
    [AutoMap(typeof(RqdCalculation))]
    public class RqdCalculationDto : EntityDto<int>
    {
        /// <summary>
        /// 
        /// </summary>
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// 
        /// </summary>
        public bool IsActive { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public CalculationType CalculationType { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public double? Interval { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public StructureTypeDto? StructureType { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public StructureDto? Structure { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public decimal? MinimumWidth { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public bool? IsDisplay { get; set; }
        
        /// <summary>
        ///
        /// </summary>
        public List<ProjectDto> Projects { get; set; } = [];
    }
}