using System.Threading.Tasks;
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using aibase.RqdCalculations.Dto;
using aibase.RqdCalculations.Dto.CalculationRqd;

namespace aibase.RqdCalculations
{
    /// <inheritdoc />
    public interface IRqdCalculationAppService : IAsyncCrudAppService<
        RqdCalculationDto,
        int,
        PagedRqdCalculationResultRequestDto,
        CreateRqdCalculationDto,
        UpdateRqdCalculationDto>
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task ExecuteRqdCalculationByDrillHoleAsync(ExecuteRqdCalculationDto input);
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<PagedResultDto<RqdCalculationResultDto>> GetRqdCalculationResultAsync(PagedRqdCalculationResultResultRequestDto input);
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<PagedResultDto<RqdPercentResultDto>> GetRqdCalculationResultByDrillHoleAsync(PagedRqdCalculationByDrillHoleResultRequestDto input);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task CalculateRqdByDrillHoleAsync(ExecuteRqdCalculationDto input);
    }
}