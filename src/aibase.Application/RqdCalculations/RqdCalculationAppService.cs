using System.Threading.Tasks;
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using aibase.RqdCalculations.Dto;
using aibase.RqdCalculations.Dto.CalculationRqd;
using aibase.RqdCalculations.Services.CalculationRqdService;
using aibase.RqdCalculations.Services.RqdCalculationService;

namespace aibase.RqdCalculations
{
    /// <summary>
    /// 
    /// </summary>
    [AbpAuthorize]
    public class RqdCalculationAppService :
        AsyncCrudAppService<
            RqdCalculation,
            RqdCalculationDto,
            int,
            PagedRqdCalculationResultRequestDto,
            CreateRqdCalculationDto,
            UpdateRqdCalculationDto>,
        IRqdCalculationAppService
    {
        private readonly IRqdCalculationService _rqdCalculationService;
        private readonly ICalculationRqdService _calculationRqdService;

        /// <inheritdoc />
        public RqdCalculationAppService(
            IRepository<RqdCalculation, int> repository,
            IRqdCalculationService rqdCalculationService, ICalculationRqdService calculationRqdService)
            : base(repository)
        {
            _rqdCalculationService = rqdCalculationService;
            _calculationRqdService = calculationRqdService;
        }

        /// <inheritdoc />
        public override async Task<RqdCalculationDto> CreateAsync(CreateRqdCalculationDto input)
        {
            var rqdCalculation = await _rqdCalculationService.CreateAsync(input);
            return MapToEntityDto(rqdCalculation);
        }

        /// <inheritdoc />
        public override async Task<RqdCalculationDto> UpdateAsync(UpdateRqdCalculationDto input)
        {
            return await _rqdCalculationService.UpdateAsync(input);
        }

        /// <inheritdoc />
        public async Task ExecuteRqdCalculationByDrillHoleAsync(ExecuteRqdCalculationDto input)
        {
            await _calculationRqdService.ExecuteRqdCalculationByDrillHoleAsync(input);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<RqdCalculationResultDto>> GetRqdCalculationResultAsync(
            PagedRqdCalculationResultResultRequestDto input)
        {
            return await _calculationRqdService.GetRqdCalculationResultAsync(input);
        }

        /// <inheritdoc />
        public async Task<PagedResultDto<RqdPercentResultDto>> GetRqdCalculationResultByDrillHoleAsync(PagedRqdCalculationByDrillHoleResultRequestDto input)
        {
            return await _calculationRqdService.GetRqdCalculationResultByDrillHoleAsync(input);

        }

        /// <inheritdoc />
        public async Task CalculateRqdByDrillHoleAsync(ExecuteRqdCalculationDto input)
        {
            await _calculationRqdService.CalculateRqdByDrillHoleAsync(input);
        }

        /// <inheritdoc />
        public override async Task<PagedResultDto<RqdCalculationDto>> GetAllAsync(
            PagedRqdCalculationResultRequestDto input)
        {
            return await _rqdCalculationService.GetAllAsync(input);
        }

        /// <inheritdoc />
        public override async Task<RqdCalculationDto> GetAsync(EntityDto<int> input)
        {
            return await _rqdCalculationService.GetAsync(input);
        }
    }
}