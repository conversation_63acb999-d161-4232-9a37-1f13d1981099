using Abp.Application.Services.Dto;
using Abp.AutoMapper;

namespace aibase.Prospects.Dto;

/// <summary>
/// 
/// </summary>
[AutoMap(typeof(Prospect))]
public class UpdateProspectDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public string? Name { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public string? Description { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public string? BackgroundColor { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public string? TextColor { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public bool? IsActive { get; set; } = true;
    
    /// <summary>
    /// 
    /// </summary>
    public int? ProjectId { get; set; }
}