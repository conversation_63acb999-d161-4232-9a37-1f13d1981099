using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.DrillHoleEntity;
using aibase.ImageEntity;
using aibase.IsolationTenant;
using aibase.Prospects.Dto;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace aibase.Prospects.Services.ProspectService;

/// <inheritdoc />
public class ProspectService : IProspectService
{
    private readonly IRepository<Prospect, int> _repository;
    private readonly IRepository<DrillHole, int> _drillHoleRepository;
    private readonly IRepository<Image, int> _imageRepository;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;
    private readonly ITenantEntityHelper _tenantEntityHelper;

    /// <summary>
    /// 
    /// </summary>
    public ProspectService(
        IRepository<Prospect, int> repository,
        IRepository<DrillHole, int> drillHoleRepository,
        IRepository<Image, int> imageRepository,
        IAbpSession abpSession,
        IMapper mapper,
        ITenantEntityHelper tenantEntityHelper
    )
    {
        _repository = repository;
        _drillHoleRepository = drillHoleRepository;
        _imageRepository = imageRepository;
        _abpSession = abpSession;
        _mapper = mapper;
        _tenantEntityHelper = tenantEntityHelper;
    }

    /// <inheritdoc />
    public async Task<Prospect> CreateAsync(CreateProspectDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        await _tenantEntityHelper.ValidateEntityAsync(input);


        // Check if the name already exists
        var existingProspect =
            await _repository.FirstOrDefaultAsync(d => d.TenantId == _abpSession.TenantId && d.Name == input.Name);
        if (existingProspect != null)
        {
            throw new UserFriendlyException($"The prospect with the name {existingProspect.Name} already exists.");
        }

        var prospect = new Prospect()
        {
            Name = input.Name,
            Description = input.Description,
            BackgroundColor = input.BackgroundColor,
            TextColor = input.TextColor,
            IsActive = input.IsActive,
            ProjectId = input.ProjectId,
            TenantId = _abpSession.GetTenantId(),
        };

        await _repository.InsertAsync(prospect);

        return prospect;
    }

    /// <inheritdoc />
    public Task<PagedResultDto<ProspectDto>> GetAllAsync(PagedProspectResultRequestDto input)
    {
        int[]? projectIds = null;
        if (!string.IsNullOrEmpty(input.ProjectIds))
        {
            try
            {
                projectIds = JsonConvert.DeserializeObject<int[]>(input.ProjectIds);
            }
            catch (JsonException ex)
            {
                throw new ArgumentException("Invalid ProjectIds format", nameof(input.ProjectIds), ex);
            }
        }

        var query = _repository.GetAllIncluding(x => x.Project)
            .AsNoTracking()
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .WhereIf(projectIds != null && projectIds.Length != 0, x => projectIds != null && projectIds.Contains(x.ProjectId));

        var queryProjectActive = query.Where(x => x.Project.IsActive == true);

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.SortField) && !string.IsNullOrWhiteSpace(input.SortOrder))
        {
            queryProjectActive = queryProjectActive.ApplySorting(input.SortField, input.SortOrder, r => r.CreationTime);
        }
        else
        {
            queryProjectActive = queryProjectActive.ApplySorting("Project.Name", "asc", r => r.CreationTime);
            queryProjectActive = queryProjectActive.OrderBy(x => x.Name); // Default sorting
        }

        var totalCount = queryProjectActive.Count();

        var prospects = queryProjectActive
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToList();

        var prospectDto = _mapper.Map<List<ProspectDto>>(prospects);

        return Task.FromResult(new PagedResultDto<ProspectDto>(totalCount, prospectDto));
    }

    /// <inheritdoc />
    public async Task<ProspectDto> GetAsync(EntityDto<int> input)
    {
        var prospect = await ValidateProspectEntity(input.Id);
        return _mapper.Map<ProspectDto>(prospect);
    }

    /// <inheritdoc />
    public async Task<ProspectDto> UpdateAsync(UpdateProspectDto input)
    {
        var prospect = await ValidateProspectEntity(input.Id);
        
        // if (input.IsActive.HasValue && !input.IsActive.Value)
        // {
        //     var hasDrillHoles = await _drillHoleRepository.GetAll()
        //         .AnyAsync(x => x.ProspectId == input.Id);

        //     if (hasDrillHoles)
        //     {
        //         throw new UserFriendlyException("Cannot deactivate Prospect as it is being used in DrillHoles.");
        //     }
        // }

        if (input.ProjectId is > 0 && input.ProjectId != prospect.ProjectId)
        {
            // Update DrillHoles
            var drillHoles = await _drillHoleRepository.GetAllListAsync(d =>
                d.ProspectId == prospect.Id);

            foreach (var drillHole in drillHoles)
            {
                drillHole.ProjectId = (int)input.ProjectId;
            }

            // Update Images
            var images = await _imageRepository.GetAllListAsync(i =>
                i.ProspectId == prospect.Id);

            foreach (var image in images)
            {
                image.ProjectId = (int)input.ProjectId;
            }
        }

        prospect.Name = input.Name ?? prospect.Name;
        prospect.Description = input.Description ?? prospect.Description;
        prospect.BackgroundColor = input.BackgroundColor ?? prospect.BackgroundColor;
        prospect.TextColor = input.TextColor ?? prospect.TextColor;
        prospect.ProjectId = input.ProjectId ?? prospect.ProjectId;
        prospect.IsActive = input.IsActive ?? prospect.IsActive;

        return await GetAsync(input);
    }   

    private async Task<Prospect> ValidateProspectEntity(int id)
    {
        var prospect = await _repository.GetAllIncluding(x => x.Project)
            .FirstOrDefaultAsync(x => x.Id == id && x.TenantId == _abpSession.GetTenantId());

        if (prospect == null)
        {
            throw new EntityNotFoundException(typeof(Prospect), id);
        }

        return prospect;
    }
}