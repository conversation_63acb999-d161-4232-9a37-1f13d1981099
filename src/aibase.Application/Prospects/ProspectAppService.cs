using System.Threading.Tasks;
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using aibase.Prospects.Dto;
using aibase.Prospects.Services.ProspectService;

namespace aibase.Prospects;

/// <summary>
/// 
/// </summary>
[AbpAuthorize]
public class ProspectAppService : AsyncCrudAppService<Prospect, ProspectDto, int, PagedProspectResultRequestDto,
    CreateProspectDto, UpdateProspectDto>, IProspectAppService
{
    private readonly IProspectService _prospectService;

    /// <inheritdoc />
    public ProspectAppService(
        IRepository<Prospect, int> repository, 
        IProspectService prospectService) : base(repository)
    {
        _prospectService = prospectService;
    }

    /// <inheritdoc />
    public override async Task<ProspectDto> CreateAsync(CreateProspectDto input)
    {
        var prospect = await _prospectService.CreateAsync(input);
        return MapToEntityDto(prospect);
    }

    /// <inheritdoc />
    public override async Task<PagedResultDto<ProspectDto>> GetAllAsync(PagedProspectResultRequestDto input)
    {
        return await _prospectService.GetAllAsync(input);
    }

    /// <inheritdoc />
    public override async Task<ProspectDto> GetAsync(EntityDto<int> input)
    {
        return await _prospectService.GetAsync(input);
    }

    /// <inheritdoc />
    public override async Task<ProspectDto> UpdateAsync(UpdateProspectDto input)
    {
        return await _prospectService.UpdateAsync(input);
    }
}