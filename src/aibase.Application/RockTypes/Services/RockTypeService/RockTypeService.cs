using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.RockGroupRockTypes;
using aibase.RockTypeNumbers;
using aibase.RockTypes.Dto;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.RockTypes.Services.RockTypeService;

/// <inheritdoc />
public class RockTypeService : IRockTypeService
{
    private readonly IRepository<RockType, int> _repository;
    private readonly IRepository<RockGroupRockType, int> _rockGroupRockTypeRepository;
    private readonly IRepository<RockTypeNumber, int> _rockTypeNumberRepository;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;
    private readonly IUnitOfWorkManager _unitOfWorkManager;

    /// <summary>
    /// 
    /// </summary>
    public RockTypeService(
        IRepository<RockType, int> repository,
        IAbpSession abpSession,
        IMapper mapper,
        IUnitOfWorkManager unitOfWorkManager, IRepository<RockGroupRockType, int> rockGroupRockTypeRepository, IRepository<RockTypeNumber, int> rockTypeNumberRepository)
    {
        _repository = repository;
        _abpSession = abpSession;
        _mapper = mapper;
        _unitOfWorkManager = unitOfWorkManager;
        _rockGroupRockTypeRepository = rockGroupRockTypeRepository;
        _rockTypeNumberRepository = rockTypeNumberRepository;
    }

    /// <inheritdoc />
    public async Task<RockType> CreateAsync(CreateRockTypeDto input, bool returnExist = false)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var tenantId = _abpSession.GetTenantId();

        var existingRockType =
            await _repository.FirstOrDefaultAsync(d => d.TenantId == tenantId && d.Name == input.Name);
        if (existingRockType != null)
        {
            if (returnExist)
            {
                return existingRockType;
            }

            throw new UserFriendlyException($"The Rock Type with the name {existingRockType.Name} already exists.");
        }

        var rockType = new RockType()
        {
            Name = input.Name,
            Code = input.Code,
            Description = input.Description,
            IsActive = input.IsActive,
            RockStyleId = input.RockStyleId,
            TenantId = tenantId,
        };

        await _repository.InsertAsync(rockType);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        return rockType;
    }

    /// <inheritdoc />
    public async Task<RockTypeDto> UpdateAsync(UpdateRockTypeDto input)
    {
        var rockType = await ValidateRockTypeEntity(input.Id);

        rockType.Name = input.Name ?? rockType.Name;
        rockType.Code = input.Code ?? rockType.Code;
        rockType.Description = input.Description ?? rockType.Description;
        rockType.IsActive = input.IsActive ?? rockType.IsActive;
        rockType.RockStyleId = input.RockStyleId ?? rockType.RockStyleId;

        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<RockTypeDto>> GetAllAsync(PagedRockTypeResultRequestDto input)
    {
        var keyword = input.Keyword?.Trim().ToLower();
        var name = input.Name?.Trim().ToLower();

        var query = _repository.GetAllIncluding(x => x.RockStyle)
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(!keyword.IsNullOrWhiteSpace(), x =>
                keyword != null && (x.Name.ToLower().Contains(keyword) ||
                                    x.Description.ToLower().Contains(keyword) ||
                                    x.Code.ToLower().Contains(keyword)))
            .WhereIf(!name.IsNullOrWhiteSpace(), x => name != null && x.Name.ToLower().Contains(name))
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
            .OrderBy(r => r.Name);
        
        var totalCount = await query.CountAsync();

        var rockTypes = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        return new PagedResultDto<RockTypeDto>(totalCount, _mapper.Map<List<RockTypeDto>>(rockTypes));
    }

    /// <inheritdoc />
    public async Task<RockTypeDto> GetAsync(EntityDto<int> input)
    {
        var rockType = await ValidateRockTypeEntity(input.Id);
        return _mapper.Map<RockTypeDto>(rockType);
    }

    /// <inheritdoc />
    public async Task DeleteAsync(EntityDto<int> input)
    {
        var rockType = await ValidateRockTypeEntity(input.Id);

        var rockGroupUsed = await _rockGroupRockTypeRepository.FirstOrDefaultAsync(x =>  x.RockTypeId == input.Id);
        if (rockGroupUsed != null)
        {
            throw new UserFriendlyException("Cannot delete this RockType because it is being used in RockGroup.");
        }
        
        var rockTypeNumberUsed = await _rockTypeNumberRepository.FirstOrDefaultAsync(x =>  x.RockTypeId == input.Id);
        if (rockTypeNumberUsed != null)
        {
            throw new UserFriendlyException("Cannot delete this RockType because it is being used in RockTypeNumber.");
        }
        
        await _repository.DeleteAsync(rockType);
    }

    private async Task<RockType> ValidateRockTypeEntity(int id)
    {
        var rockType = await _repository.FirstOrDefaultAsync(x => x.Id == id);

        if (rockType == null)
        {
            throw new EntityNotFoundException(typeof(RockType), id);
        }

        return rockType;
    }
}