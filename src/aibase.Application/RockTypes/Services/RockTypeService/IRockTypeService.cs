using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Dependency;
using aibase.RockTypes.Dto;

namespace aibase.RockTypes.Services.RockTypeService;

/// <inheritdoc />
public interface IRockTypeService : ITransientDependency
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <param name="returnExist"></param>
    /// <returns></returns>
    Task<RockType> CreateAsync(CreateRockTypeDto input, bool returnExist = false);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<RockTypeDto> UpdateAsync(UpdateRockTypeDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagedResultDto<RockTypeDto>> GetAllAsync(PagedRockTypeResultRequestDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<RockTypeDto> GetAsync(EntityDto<int> input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task DeleteAsync(EntityDto<int> input);
}