using Abp.Application.Services.Dto;
using Abp.AutoMapper;

namespace aibase.RockTypes.Dto;

/// <summary>
/// 
/// </summary>
[AutoMap(typeof(RockType))]
public class UpdateRockTypeDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public string? Name { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public string? Code { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public string? Description { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public bool? IsActive { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int? RockStyleId { get; set; }
}