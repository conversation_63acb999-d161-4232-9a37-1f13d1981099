using System.Collections.Generic;
using aibase.ImageCrops;
using aibase.Models.Dto;

namespace aibase.RockLines.Dto;

/// <summary>
/// 
/// </summary>
public class AutoInsertRockLineDto
{
    /// <summary>
    /// 
    /// </summary>
    public  List<SegmentResultDto> Segments { get; set; } = [];
    
    /// <summary>
    /// 
    /// </summary>
    public  List<SegmentResultDto> SegmentDetails { get; set; } = [];
    
    /// <summary>
    /// 
    /// </summary>
    public  List<ImageCrop> ImageCrops { get; set; } = [];
    
    /// <summary>
    /// 
    /// </summary>
    public int DrillHoleId { get; set; }
}