using Abp.Application.Services.Dto;
using AutoMapper;

namespace aibase.RockLines.Dto;

/// <inheritdoc />
[AutoMap(typeof(RockLine))]
public class RockLineDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public RockLineType Type { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double DepthFrom { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double DepthTo { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double StartX { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double StartY { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double EndX { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double EndY { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int ImageCropId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int RowIndex { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public double Length { get; set; }
}