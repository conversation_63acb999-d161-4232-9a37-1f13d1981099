using System.ComponentModel.DataAnnotations;

namespace aibase.RockLines.Dto;

/// <summary>
/// 
/// </summary>
public class CreateRockLineDto
{
    /// <summary>
    /// 
    /// </summary>
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public RockLineType Type { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public double DepthFrom { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public double DepthTo { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public double StartX { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public double StartY { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public double EndX { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public double EndY { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int ImageCropId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int RowIndex { get; set; }
}