using System.Collections.Generic;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Dependency;
using aibase.ImageCrops;
using aibase.ImageCrops.Dto;
using aibase.RockLines.Dto;

namespace aibase.RockLines.Services;

/// <summary>
/// 
/// </summary>
public interface IRockLineService : ITransientDependency
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<RockLine> CreateAsync(CreateRockLineDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagedResultDto<RockLineDto>> GetAllAsync(PagedRockLineResultRequestDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<RockLineDto> UpdateAsync(UpdateRockLineDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<RockLineDto> GetAsync(EntityDto<int> input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task AutoInsertRockLineAsync(AutoInsertRockLineDto input);
}