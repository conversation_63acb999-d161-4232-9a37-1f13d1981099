using System.ComponentModel.DataAnnotations;
using Abp.Application.Services.Dto;

namespace aibase.DataEntries.Dto;

/// <summary>
/// 
/// </summary>
public class PagedLoggingBarRequestDto : PagedResultRequestDto
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int GeologySuiteId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int DrillholeId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int ImageSubtypeId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public double? DepthFrom { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public double? DepthTo { get; set; }
}