using System;
using aibase.Colours.Dto;
using aibase.GeologyFields;
using aibase.Numbers.Dto;
using aibase.PickLists.Dto;
using aibase.RockTree.Dto;
using aibase.RockTypes.Dto;

namespace aibase.DataEntries.Dto;

/// <summary>
/// 
/// </summary>
public class DataEntryValueDto
{
    /// <summary>
    /// 
    /// </summary>
    public int DataEntryId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int ValueId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int GeologysuiteFieldId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int Sequence { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? FieldName { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public FieldType FieldType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? NumberId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public NumberDto? Number { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double? NumberValue { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? RockTypeId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public RockTypeDto? RockType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? PickListItemId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public PickListItemDto? PickListItem { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? ColourId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public ColourDto? Colour { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public DateTime? DateValue { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? RockNodeId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public RockNodeDto? RockNode { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public TreeNodeDto? TreeNode { get; set; }
}