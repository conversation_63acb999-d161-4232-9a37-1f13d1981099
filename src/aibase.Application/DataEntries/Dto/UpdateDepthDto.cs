using System.ComponentModel.DataAnnotations;

namespace aibase.DataEntries.Dto;

/// <summary>
/// 
/// </summary>
public class UpdateDepthDto
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int Id { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double? DepthFrom { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double? DepthTo { get; set; }
}