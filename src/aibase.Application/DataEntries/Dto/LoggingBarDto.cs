using System.Collections.Generic;
using Abp.Application.Services.Dto;
using AutoMapper;

namespace aibase.DataEntries.Dto;

/// <inheritdoc />
[AutoMap(typeof(DataEntry))]
public class LoggingBarDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public int GeologySuiteId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int DrillHoleId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int ImageSubtypeId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public double DepthFrom { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int StartImageCropId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public double StartX { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public double DepthTo { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int EndImageCropId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public double EndX { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public List<int> BetweenImageCropIds { get; set; } = [];
    
    /// <summary>
    /// 
    /// </summary>
    public int DataEntryId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public List<DataEntryValueDto> DataEntryValues { get; set; } = [];
}