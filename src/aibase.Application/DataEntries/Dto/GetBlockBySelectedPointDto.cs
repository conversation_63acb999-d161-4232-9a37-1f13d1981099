using System.ComponentModel.DataAnnotations;

namespace aibase.DataEntries.Dto;

/// <summary>
/// 
/// </summary>
public class GetBlockBySelectedPointDto
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int GeologySuiteId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int DrillHoleId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int ImageCropId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public double X { get; set; }
}