using System.Collections.Generic;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Dependency;
using aibase.DataEntries.Dto;
using Microsoft.AspNetCore.Mvc;

namespace aibase.DataEntries.Services;

/// <inheritdoc />
public interface IDataEntryService : ITransientDependency
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<DataEntry> CreateDataEntryAsync(CreateDataEntryDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<DataEntryResultDto> GetAllDataEntryAsync(PagedDataEntryRequestDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    Task HandleGetDataEntryAsync(List<DataEntryValueDto> geologySuiteFields, List<DataEntryDto> dataEntryDto);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IActionResult> GetAllDataEntryByDrillholeAsync(PagedDataEntryByDrillholeRequestDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IActionResult> GetDataEntryByLoggingViewAsync(PagedDataEntryByLoggingViewRequestDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateDataEntryValueAsync(UpdateDataEntryValueDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<DataEntry> UpdateDataEntryAsync(UpdateDataEntryDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateDepthDataEntryAsync(UpdateDepthDataEntryDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateDepthAsync(UpdateDepthDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task DeleteGeologyDataPoint(EntityDto<int> input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<ResultUploadDataEntryDto> UploadDataEntryByImportTemplateAsync([FromForm] UploadDataEntryByImportTemplateDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<ResultUploadDataEntryDto> UpdateDataEntryByImportTemplateAsync([FromForm] UploadDataEntryByImportTemplateDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<ResultUploadDataEntryDto> AddAndUpdateDataEntryByImportTemplateAsync([FromForm] UploadDataEntryByImportTemplateDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<string>> ValidationDataEntryDataAsync(UploadDataEntryByImportTemplateDto input);

    /// <summary>
    ///
    /// </summary>
    /// <returns></returns>
    Task DeleteDataToolAsync(DeleteDataToolDto input);


    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task CalculateLoggingBarAsync(CalculateLoggingBarDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagedResultDto<LoggingBarDto>> GetAllLoggingBarAsync(PagedLoggingBarRequestDto input);

    /// <summary>
    /// Get detailed information for a specific data entry
    /// </summary>
    /// <param name="dataEntryId">Data entry ID</param>
    /// <returns>Detailed data entry information</returns>
    Task<DataEntryDto> GetDetailDataEntry(int dataEntryId);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<BlockResultBySelectedPointDto> GetBlockBySelectedPointAsync(GetBlockBySelectedPointDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<double?> GetNextBlockAsync(GetNextBlockDto input);
}
