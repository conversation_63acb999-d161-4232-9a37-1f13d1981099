using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.Colours.Dto;
using aibase.ColourValues;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.Colours.Services.ColourService;

/// <inheritdoc />
public class ColourService : IColourService
{
    private readonly IRepository<Colour, int> _repository;
    private readonly IRepository<ColourValue, int> _valueRepository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="repository"></param>
    /// <param name="valueRepository"></param>
    /// <param name="abpSession"></param>
    /// <param name="mapper"></param>
    /// <param name="unitOfWorkManager"></param>
    public ColourService(
        IRepository<Colour, int> repository, 
        IRepository<ColourValue, int> valueRepository,
        IAbpSession abpSession, 
        IMapper mapper, IUnitOfWorkManager unitOfWorkManager)
    {
        _repository = repository;
        _abpSession = abpSession;
        _mapper = mapper;
        _unitOfWorkManager = unitOfWorkManager;
        _valueRepository = valueRepository;
    }

    /// <inheritdoc />
    public async Task<Colour> CreateAsync(CreateColourDto input, bool returnExist = false)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }
        var tenantId = _abpSession.GetTenantId();

        var existingColour =
            await _repository.FirstOrDefaultAsync(d => d.TenantId == tenantId && d.Name == input.Name);
        if (existingColour != null)
        {
            if (returnExist)
            {
                return existingColour;
            }
            
            throw new UserFriendlyException($"The Colour with the name {existingColour.Name} already exists.");
        }
        
        var colour = new Colour
        {
            Name = input.Name,
            HexCode = input.HexCode,
            IsActive = input.IsActive,
            TenantId = tenantId,
        };

        await _repository.InsertAsync(colour);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        return colour;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<ColourDto>> GetAllAsync(PagedColourResultRequestDto input)
    {
        var query = _repository.GetAll()
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .OrderBy(r => r.Name);

        var totalCount = await query.CountAsync();

        var colours = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        return new PagedResultDto<ColourDto>(totalCount, _mapper.Map<List<ColourDto>>(colours));
    }

    /// <inheritdoc />
    public async Task<ColourDto> GetAsync(EntityDto<int> input)
    {
        var colour = await ValidateColourEntity(input.Id);
        return _mapper.Map<ColourDto>(colour);
    }

    /// <inheritdoc />
    public async Task<ColourDto> UpdateAsync(UpdateColourDto input)
    {
        var colour = await ValidateColourEntity(input.Id);

        if (input.IsActive == false)
        {
            var colourUsed = await _valueRepository.FirstOrDefaultAsync(x => x.ColourId == input.Id);
            if (colourUsed != null)
            {
                throw new UserFriendlyException("Cannot deactivate this record because it is already in use.");
            }
        }
        
        colour.Name = input.Name ?? colour.Name;
        colour.HexCode = input.HexCode ?? colour.HexCode;
        colour.IsActive = input.IsActive ?? colour.IsActive;

        return await GetAsync(input);
    }
    
    private async Task<Colour> ValidateColourEntity(int id)
    {
        var colour = await _repository.FirstOrDefaultAsync(x => x.Id == id);

        if (colour == null)
        {
            throw new EntityNotFoundException(typeof(Colour), id);
        }

        return colour;
    }
}