using Abp.Application.Services.Dto;
using AutoMapper;

namespace aibase.Colours.Dto;

/// <inheritdoc />
[AutoMap(typeof(Colour))]
public class ColourDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public string HexCode { get; set; } = string.Empty;
    
    /// <summary>
    /// 
    /// </summary>
    public bool IsActive { get; set; }
}