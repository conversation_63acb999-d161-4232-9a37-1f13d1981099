using System.Collections.Generic;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Dependency;
using aibase.GeologySuiteFields.Dto;

namespace aibase.GeologySuiteFields.Services.GeologySuiteFieldService;

/// <inheritdoc />
public interface IGeologySuiteFieldService : ITransientDependency
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <param name="returnExist"></param>
    /// <returns></returns>
    Task<GeologySuiteField> CreateAsync(CreateGeologySuiteFieldDto input, bool returnExist = false);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagedResultDto<GeologySuiteFieldDto>> GetAllAsync(PagedGeologySuiteFieldResultRequestDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<GeologySuiteFieldDto> GetAsync(EntityDto<int> input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<GeologySuiteFieldDto> UpdateAsync(UpdateGeologySuiteFieldDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="geologySuiteFields"></param>
    /// <returns></returns>
    Task PopulateRelatedFieldsAsync(List<GeologySuiteFieldDto> geologySuiteFields);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task DeleteAsync(EntityDto<int> input);
}