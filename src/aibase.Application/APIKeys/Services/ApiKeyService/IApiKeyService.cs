using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Dependency;
using aibase.APIKeys.Dto;

namespace aibase.APIKeys.Services.APIKeyService;

/// <inheritdoc />
public interface IApiKeyService : ITransientDependency
{
    /// <summary>
    /// Creates a new API key.
    /// </summary>
    /// <param name="input">The input data for creating the API key.</param>
    /// <returns>The created API key.</returns>
    Task<APIKey> CreateAsync(CreateApiKeyDto input);

    /// <summary>
    /// Gets all API keys with pagination.
    /// </summary>
    /// <param name="input">The input data for pagination and filtering.</param>
    /// <returns>A paged result of API keys.</returns>
    Task<PagedResultDto<ApiKeyDto>> GetAllAsync(PagedApiKeyResultDto input);

    /// <summary>
    /// Gets a specific API key by ID.
    /// </summary>
    /// <param name="input">The ID of the API key to retrieve.</param>
    /// <returns>The API key DTO.</returns>
    Task<ApiKeyDto> GetAsync(EntityDto<int> input);

    /// <summary>
    /// Updates an existing API key.
    /// </summary>
    /// <param name="input">The input data for updating the API key.</param>
    /// <returns>The updated API key DTO.</returns>
    Task<ApiKeyDto> UpdateAsync(UpdateApiKeyDto input);

    /// <summary>
    /// Deletes an API key by ID.
    /// </summary>
    /// <param name="input">The ID of the API key to delete.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    Task DeleteAsync(EntityDto<int> input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task AssignApiKeyTypeAsync(AssignApiKeyTypeDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="keyCode"></param>
    /// <returns></returns>
    Task CheckRoleApiKey(APIKeyCode keyCode);

    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    Task<PagedResultDto<ApiKeyDto>> GetApiKeyByTenantAsync(PagedApiKeyResultByTenantDto input);
}