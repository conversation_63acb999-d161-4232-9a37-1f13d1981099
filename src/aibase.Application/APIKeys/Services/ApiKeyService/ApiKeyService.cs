using System;
using System.Linq;
using System.Security.Cryptography;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.APIKeys.Dto;
using aibase.ApiKeyTypes.Dto;
using aibase.Authorization.Roles;
using aibase.Authorization.Users;
using aibase.MultiTenancy.Dto;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;

namespace aibase.APIKeys.Services.APIKeyService;

/// <inheritdoc />
public class ApiKeyService : IApiKeyService
{
    private readonly IRepository<APIKey, int> _repository;
    private readonly IRepository<APIKeyTypes, int> _apiKeyTypeRepository;
    private readonly IRepository<APIKeyRole, int> _apiKeyRoleRepository;
    private readonly UserManager _userManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IHttpContextAccessor _httpContextAccessor;

    /// <summary>
    /// 
    /// </summary>
    public ApiKeyService(
        IRepository<APIKey, int> repository,
        IRepository<APIKeyTypes, int> apiKeyTypeRepository,
        IRepository<APIKeyRole, int> apiKeyRoleRepository,
        IAbpSession abpSession,
        IMapper mapper,
        IUnitOfWorkManager unitOfWorkManager,
        IHttpContextAccessor httpContextAccessor, UserManager userManager)
    {
        _repository = repository;
        _abpSession = abpSession;
        _mapper = mapper;
        _unitOfWorkManager = unitOfWorkManager;
        _apiKeyRoleRepository = apiKeyRoleRepository;
        _httpContextAccessor = httpContextAccessor;
        _userManager = userManager;
        _apiKeyTypeRepository = apiKeyTypeRepository;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="headerName"></param>
    /// <returns></returns>
    protected string GetHeaderValue(string headerName)
    {
        return _httpContextAccessor.HttpContext?.Request.Headers.TryGetValue(headerName, out var headerValue) !=
               true
            ? ""
            : headerValue.ToString();
    }


    /// <inheritdoc />
    public async Task<APIKey> CreateAsync(CreateApiKeyDto input)
    {
        using (_unitOfWorkManager.Current.DisableFilter(AbpDataFilters.MayHaveTenant))
        {
            if (_abpSession.TenantId != null && _abpSession.TenantId != input.TenantId)
            {
                throw new UserFriendlyException("Cannot create API Key for another Account.");
            }

            var currentUser = await _userManager.GetUserByIdAsync(_abpSession.GetUserId());
            var roles = await _userManager.GetRolesAsync(currentUser);
            var isUser = roles.Contains(StaticRoleNames.Users.User);
            if (isUser)
            {
                throw new UserFriendlyException("The account does not have permission to perform this feature.");
            }

            var apiKeyType = await _apiKeyTypeRepository.FirstOrDefaultAsync(x => input.KeyCode.Contains(x.Id));

            if (apiKeyType == null)
            {
                throw new UserFriendlyException("Invalid API Key Code.");
            }

            var apiKey = new APIKey()
            {
                TenantId = input.TenantId,
                ExpiresAt = input.ExpiresAt,
                IsActive = input.IsActive ?? true,
                Key = GenerateApiKey(apiKeyType.Prefix),
                CreationTime = DateTime.UtcNow
            };
            await _repository.InsertAsync(apiKey);
            await _unitOfWorkManager.Current.SaveChangesAsync();

            var assign = new AssignApiKeyTypeDto
            {
                ApiKeyId = apiKey.Id,
                ApiKeyTypeIds = input.KeyCode
            };
            await AssignApiKeyTypeAsync(assign);

            return apiKey;
        }
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<ApiKeyDto>> GetAllAsync(PagedApiKeyResultDto input)
    {
        if (_abpSession.TenantId != null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var query = _repository.GetAllIncluding(x => x.Tenant)
            .AsNoTracking()
            .Include(x => x.APIKeyRoles)
            .ThenInclude(x => x.ApiKeyType)
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
            .WhereIf(!string.IsNullOrEmpty(input.TenantName),
                x => x.Tenant.Name.ToLower().Contains((input.TenantName ?? "").ToLower()))
            .OrderBy(r => r.CreationTime);

        var totalCount = await query.CountAsync();

        var apiKeys = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .Select(x => new ApiKeyDto
            {
                Id = x.Id,
                IsActive = x.IsActive,
                ExpiresAt = x.ExpiresAt,
                Key = x.Key,
                IsExpried = x.IsExpired,
                IsUnlimited = x.IsUnlimited,
                Tenant = _mapper.Map<TenantDto>(x.Tenant),
                ApiKeyType = x.APIKeyRoles.Select(y => _mapper.Map<ApiKeyTypeDto>(y.ApiKeyType)).ToList(),
            })
            .ToListAsync();

        return new PagedResultDto<ApiKeyDto>(totalCount, apiKeys);
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<ApiKeyDto>> GetApiKeyByTenantAsync(PagedApiKeyResultByTenantDto input)
    {
        using (_unitOfWorkManager.Current.DisableFilter(AbpDataFilters.MayHaveTenant))
        {
            if (_abpSession.TenantId == null)
            {
                throw new UserFriendlyException("The account does not have permission to perform this feature.");
            }

            var currentUser = await _userManager.GetUserByIdAsync(_abpSession.GetUserId());
            var roles = await _userManager.GetRolesAsync(currentUser);
            var isCompany = roles.Contains(StaticRoleNames.Tenants.Admin);

            if (!isCompany)
            {
                throw new UserFriendlyException("The account does not have permission to perform this feature.");
            }

            var query = _repository.GetAllIncluding(x => x.Tenant)
                .AsNoTracking()
                .Include(x => x.APIKeyRoles)
                .ThenInclude(x => x.ApiKeyType)
                .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
                .OrderBy(r => r.CreationTime);

            var totalCount = await query.CountAsync();

            var apiKeysDto = await query
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount)
                .Select(x => new ApiKeyDto
                {
                    Id = x.Id,
                    IsActive = x.IsActive,
                    ExpiresAt = x.ExpiresAt,
                    Key = x.Key,
                    IsExpried = x.IsExpired,
                    IsUnlimited = x.IsUnlimited,
                    Tenant = _mapper.Map<TenantDto>(x.Tenant),
                    ApiKeyType = x.APIKeyRoles.Select(y => _mapper.Map<ApiKeyTypeDto>(y.ApiKeyType)).ToList(),
                }).ToListAsync();

            return new PagedResultDto<ApiKeyDto>(totalCount, apiKeysDto);
        }
    }

    /// <inheritdoc />
    public async Task<ApiKeyDto> GetAsync(EntityDto<int> input)
    {
        var apiKey = await GetApiKeyById(input.Id);
        var apiKeyDto = _mapper.Map<ApiKeyDto>(apiKey);
        apiKeyDto.ApiKeyType = apiKey.APIKeyRoles.Select(y => _mapper.Map<ApiKeyTypeDto>(y.ApiKeyType)).ToList();

        return apiKeyDto;
    }

    /// <inheritdoc />
    public async Task<ApiKeyDto> UpdateAsync(UpdateApiKeyDto input)
    {
        using (_unitOfWorkManager.Current.DisableFilter(AbpDataFilters.MayHaveTenant))
        {
            if (_abpSession.TenantId != null && _abpSession.TenantId != input.TenantId)
            {
                throw new UserFriendlyException("Cannot update API Key for another Account.");
            }
            
            var currentUser = await _userManager.GetUserByIdAsync(_abpSession.GetUserId());
            var roles = await _userManager.GetRolesAsync(currentUser);
            var isUser = roles.Contains(StaticRoleNames.Users.User);
            if (isUser)
            {
                throw new UserFriendlyException("The account does not have permission to perform this feature.");
            }

            var apiKey = await GetApiKeyById(input.Id);

            apiKey.TenantId = input.TenantId ?? apiKey.TenantId;
            apiKey.ExpiresAt = input.ExpiresAt;
            apiKey.IsActive = input.IsActive ?? apiKey.IsActive;
            apiKey.LastModificationTime = DateTime.UtcNow;

            var updateApiKey = await _repository.UpdateAsync(apiKey);

            if (input.KeyCode == null) return _mapper.Map<ApiKeyDto>(updateApiKey);

            var assign = new AssignApiKeyTypeDto
            {
                ApiKeyId = apiKey.Id,
                ApiKeyTypeIds = input.KeyCode
            };
            await AssignApiKeyTypeAsync(assign);

            return _mapper.Map<ApiKeyDto>(updateApiKey);
        }
    }

    /// <inheritdoc />
    public async Task DeleteAsync(EntityDto<int> input)
    {
        using (_unitOfWorkManager.Current.DisableFilter(AbpDataFilters.MayHaveTenant))
        {
            var currentUser = await _userManager.GetUserByIdAsync(_abpSession.GetUserId());
            var roles = await _userManager.GetRolesAsync(currentUser);
            var isUser = roles.Contains(StaticRoleNames.Users.User);
            if (isUser)
            {
                throw new UserFriendlyException("The account does not have permission to perform this feature.");
            }

            // var apiKey = await GetApiKeyById(input.Id);
            await _repository.DeleteAsync(input.Id);
        }
    }

    /// <inheritdoc />
    public async Task AssignApiKeyTypeAsync(AssignApiKeyTypeDto input)
    {
        var existingApiKeyRoles = await _apiKeyRoleRepository.GetAllListAsync(x => x.ApiKeyId == input.ApiKeyId);

        var existingApiKeyTypeIds = existingApiKeyRoles.Select(x => x.ApiKeyTypeId).ToList();

        var apiKeyTypesToAdd = input.ApiKeyTypeIds.Except(existingApiKeyTypeIds).ToList();

        var apiKeyTypesToDelete = existingApiKeyTypeIds.Except(input.ApiKeyTypeIds).ToList();

        foreach (var apiKeyRole in apiKeyTypesToAdd.Select(apiKeyTypeId => new APIKeyRole
                 {
                     ApiKeyTypeId = apiKeyTypeId,
                     ApiKeyId = input.ApiKeyId,
                 }))
        {
            await _apiKeyRoleRepository.InsertAsync(apiKeyRole);
        }

        foreach (var apiKeyRole in apiKeyTypesToDelete
                     .Select(apiKeyTypeId => existingApiKeyRoles.FirstOrDefault(x => x.ApiKeyTypeId == apiKeyTypeId))
                     .OfType<APIKeyRole>())
        {
            await _apiKeyRoleRepository.DeleteAsync(apiKeyRole);
        }
    }

    /// <inheritdoc />
    public async Task CheckRoleApiKey(APIKeyCode keyCode)
    {
        var keyValue = GetHeaderValue("X-API-Key");
        if (string.IsNullOrEmpty(keyValue)) return;

        var apiKey = await _repository.GetAll()
            .Include(x => x.APIKeyRoles)
            .ThenInclude(x => x.ApiKeyType)
            .FirstOrDefaultAsync(x => x.Key == keyValue);

        if (apiKey == null)
        {
            throw new EntityNotFoundException(typeof(APIKey).ToString());
        }

        // Check if API key is active
        if (!apiKey.IsActive)
        {
            throw new UserFriendlyException("This API key is inactive.");
        }

        // Check if API key is expired
        if (apiKey.IsExpired)
        {
            throw new UserFriendlyException("This API key has expired.");
        }

        // Check if key has required role
        var keyRoles = apiKey.APIKeyRoles.Select(x => x.ApiKeyType.Code).ToList();
        if (!keyRoles.Contains(keyCode))
        {
            throw new UserFriendlyException("The API key does not have permission to perform this function.");
        }

        // Update LastUsedAt
        apiKey.LastUsedAt = DateTime.UtcNow;
        await _repository.UpdateAsync(apiKey);
    }

    private async Task<APIKey> GetApiKeyById(int id)
    {
        var apiKey = await _repository.GetAllIncluding(x => x.Tenant)
            .Include(x => x.APIKeyRoles)
            .ThenInclude(x => x.ApiKeyType)
            .FirstOrDefaultAsync(x => x.Id == id);
        if (apiKey == null)
        {
            throw new EntityNotFoundException(typeof(APIKey), id);
        }

        return apiKey;
    }

    private static string GenerateApiKey(string prefix)
    {
        var randomBytes = new byte[32];
        using (var rng = RandomNumberGenerator.Create())
        {
            rng.GetBytes(randomBytes);
        }

        var encodedKey = Base64UrlEncoder.Encode(randomBytes);
        return $"{prefix}{encodedKey}";
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="apiKey"></param>
    /// <param name="keyCode"></param>
    /// <returns></returns>
    public async Task<bool> ValidateKeyAsync(string apiKey, APIKeyCode keyCode)
    {
        var apiKeyEntity = await _repository
            .FirstOrDefaultAsync(x => x.Key == apiKey);

        if (apiKeyEntity == null)
        {
            return false;
        }

        if (!apiKeyEntity.IsActive || apiKeyEntity.ExpiresAt <= DateTime.UtcNow)
        {
            return false;
        }

        return true;
    }
}