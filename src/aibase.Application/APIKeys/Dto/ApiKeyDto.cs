using System;
using System.Collections.Generic;
using Abp.Application.Services.Dto;
using aibase.ApiKeyTypes.Dto;
using aibase.MultiTenancy.Dto;

namespace aibase.APIKeys.Dto;

/// <inheritdoc />
[AutoMapper.AutoMap(typeof(APIKey))]
public class ApiKeyDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public bool IsActive { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public DateTime? ExpiresAt { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public string Key { get; set; } = string.Empty;
    
    /// <summary>
    /// 
    /// </summary>
    public bool IsExpried { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public bool IsUnlimited { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int TenantId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public List<ApiKeyTypeDto> ApiKeyType { get; set; } = [];
    
    /// <summary>
    /// 
    /// </summary>
    public TenantDto Tenant { get; set; }
}
