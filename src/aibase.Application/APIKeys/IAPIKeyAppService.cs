using System.Threading.Tasks;
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using aibase.APIKeys.Dto;

namespace aibase.APIKeys;

/// <inheritdoc />
public interface IApiKeyAppService : IAsyncCrudAppService<ApiKeyDto, int, PagedApiKeyResultDto,
    CreateApiKeyDto, UpdateApiKeyDto>
{
    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    Task<PagedResultDto<ApiKeyDto>> GetApiKeyByTenantAsync(PagedApiKeyResultByTenantDto input);
}