using System.Threading.Tasks;
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using aibase.APIKeys.Dto;
using aibase.APIKeys.Services.APIKeyService;

namespace aibase.APIKeys;

/// <summary>
/// 
/// </summary>
[AbpAuthorize]
public class APIKeyAppService : AsyncCrudAppService<APIKey, ApiKeyDto, int, PagedApiKeyResultDto,
    CreateApiKeyDto, UpdateApiKeyDto>, IApiKeyAppService
{
    private readonly IApiKeyService _apiKeyService;

    /// <inheritdoc />
    public APIKeyAppService(
        IRepository<APIKey> repository,
        IApiKeyService apiKeyService) : base(repository)
    {
        _apiKeyService = apiKeyService;
    }

    /// <inheritdoc />
    public override async Task<ApiKeyDto> CreateAsync(CreateApiKeyDto input)
    {
        var apiKey = await _apiKeyService.CreateAsync(input);
        return MapToEntityDto(apiKey);
    }

    /// <inheritdoc />
    public override async Task<PagedResultDto<ApiKeyDto>> GetAllAsync(PagedApiKeyResultDto input)
    {
        return await _apiKeyService.GetAllAsync(input);
    }

    /// <inheritdoc />
    public override async Task<ApiKeyDto> GetAsync(EntityDto<int> input)
    {
        return await _apiKeyService.GetAsync(input);
    }

    /// <inheritdoc />
    public override async Task<ApiKeyDto> UpdateAsync(UpdateApiKeyDto input)
    {
        return await _apiKeyService.UpdateAsync(input);
    }

    /// <inheritdoc />
    public override async Task DeleteAsync(EntityDto<int> input)
    {
        await _apiKeyService.DeleteAsync(input);
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<ApiKeyDto>> GetApiKeyByTenantAsync(PagedApiKeyResultByTenantDto input)
    {
        return await _apiKeyService.GetApiKeyByTenantAsync(input);
    }
}