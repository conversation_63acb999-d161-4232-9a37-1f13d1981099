using Abp.Application.Services.Dto;
using AutoMapper;

namespace aibase.StructureConditions.Dto;

/// <inheritdoc />
[AutoMap(typeof(StructureCondition))]
public class StructureConditionDto : EntityDto
{
    /// <summary>
    /// Name of the structure condition
    /// </summary>
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// Description of the structure condition
    /// </summary>
    public string Description { get; set; } = string.Empty;
    
    /// <summary>
    /// Whether the structure condition is active
    /// </summary>
    public bool IsActive { get; set; }
}