using Abp.Application.Services.Dto;

namespace aibase.StructureConditions.Dto;

/// <summary>
/// DTO for updating a StructureCondition
/// </summary>
public class UpdateStructureConditionDto : EntityDto
{
    /// <summary>
    /// Name of the structure condition
    /// </summary>
    public string? Name { get; set; } = string.Empty;
    
    /// <summary>
    /// Description of the structure condition
    /// </summary>
    public string? Description { get; set; }
    
    /// <summary>
    /// Whether the structure condition is active
    /// </summary>
    public bool? IsActive { get; set; }
}