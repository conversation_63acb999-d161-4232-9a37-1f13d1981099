using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.StructureConditions.Dto;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.StructureConditions.Services;

/// <inheritdoc />
public class StructureConditionService : IStructureConditionService
{
    private readonly IRepository<StructureCondition, int> _repository;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;
    private readonly IUnitOfWorkManager _unitOfWorkManager;

    /// <summary>
    /// Constructor for StructureConditionService
    /// </summary>
    public StructureConditionService(
        IRepository<StructureCondition, int> repository,
        IAbpSession abpSession,
        IMapper mapper,
        IUnitOfWorkManager unitOfWorkManager)
    {
        _repository = repository;
        _abpSession = abpSession;
        _mapper = mapper;
        _unitOfWorkManager = unitOfWorkManager;
    }

    /// <inheritdoc />
    public async Task<StructureCondition> CreateAsync(CreateStructureConditionDto input, bool returnExist = false)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }
        var tenantId = _abpSession.GetTenantId();

        var existingStructureCondition = await _repository
            .FirstOrDefaultAsync(x => x.TenantId == tenantId && x.Name == input.Name);
        if (existingStructureCondition != null)
        {
            if (returnExist)
            {
                return existingStructureCondition;
            }
            
            throw new UserFriendlyException($"A StructureCondition with the name {existingStructureCondition.Name} already exists.");
        }

        var structureCondition = new StructureCondition()
        {
            Name = input.Name,
            Description = input.Description ?? string.Empty,
            IsActive = input.IsActive,
            TenantId = tenantId
        };

        await _repository.InsertAsync(structureCondition);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        return structureCondition;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<StructureConditionDto>> GetAllAsync(PagedStructureConditionResultRequestDto input)
    {
        var query = _repository.GetAll()
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => 
                input.Keyword != null && (x.Name.ToLower().Contains(input.Keyword.ToLower()) ||
                                          x.Description.ToLower().Contains(input.Keyword.ToLower())))
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
            .OrderBy(r => r.Name);

        var totalCount = await query.CountAsync();

        var structureConditions = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        return new PagedResultDto<StructureConditionDto>(totalCount, _mapper.Map<List<StructureConditionDto>>(structureConditions));
    }

    /// <inheritdoc />
    public async Task<StructureConditionDto> UpdateAsync(UpdateStructureConditionDto input)
    {
        var structureCondition = await ValidateStructureConditionEntity(input.Id);

        structureCondition.Name = input.Name ?? structureCondition.Name;
        structureCondition.Description = input.Description ?? string.Empty;
        structureCondition.IsActive = input.IsActive ?? structureCondition.IsActive;

        await _repository.UpdateAsync(structureCondition);
        return _mapper.Map<StructureConditionDto>(structureCondition);
    }

    /// <inheritdoc />
    public async Task<StructureConditionDto> GetAsync(EntityDto<int> input)
    {
        var structureCondition = await ValidateStructureConditionEntity(input.Id);
        return _mapper.Map<StructureConditionDto>(structureCondition);
    }

    /// <inheritdoc />
    public async Task DeleteAsync(EntityDto<int> input)
    {
        await ValidateStructureConditionEntity(input.Id);
        await _repository.DeleteAsync(input.Id);
    }

    private async Task<StructureCondition> ValidateStructureConditionEntity(int id)
    {
        var structureCondition = await _repository.FirstOrDefaultAsync(x => x.Id == id);

        if (structureCondition == null)
        {
            throw new EntityNotFoundException(typeof(StructureCondition), id);
        }

        return structureCondition;
    }
}