using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.DownholeDatas;
using aibase.GeologySuites.Dto;
using aibase.ProjectEntity;
using aibase.ProjectLoggingViews;
using aibase.Projects.Dto;
using aibase.ProjectSuites;
using aibase.SuiteAttributes;
using aibase.Suites.Dto;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.Suites.Services.SuiteService;

/// <inheritdoc />
public class SuiteService : ISuiteService
{
    private readonly IRepository<Suite, int> _repository;
    private readonly IRepository<SuiteAttribute, int> _suiteAttributeRepository;
    private readonly IRepository<ProjectSuite, int> _projectSuiteRepository;
    private readonly IRepository<ProjectLoggingView, int> _projectLoggingRepository;
    private readonly IRepository<Project, int> _projectRepository;
    private readonly IRepository<DownholeData, int> _downholeDataRepository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    public SuiteService(
        IRepository<Suite, int> repository,
        IRepository<SuiteAttribute, int> suiteAttributeRepository,
        IRepository<ProjectSuite, int> projectSuiteRepository,
        IRepository<ProjectLoggingView, int> projectLoggingRepository,
        IAbpSession abpSession,
        IMapper mapper, IUnitOfWorkManager unitOfWorkManager, IRepository<Project, int> projectRepository,
        IRepository<DownholeData, int> downholeDataRepository)
    {
        _repository = repository;
        _suiteAttributeRepository = suiteAttributeRepository;
        _projectSuiteRepository = projectSuiteRepository;
        _projectLoggingRepository = projectLoggingRepository;
        _abpSession = abpSession;
        _mapper = mapper;
        _unitOfWorkManager = unitOfWorkManager;
        _projectRepository = projectRepository;
        _downholeDataRepository = downholeDataRepository;
    }

    /// <inheritdoc />
    public async Task<Suite> CreateAsync(CreateSuiteDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var tenantId = _abpSession.GetTenantId();

        var existingSuite =
            await _repository.FirstOrDefaultAsync(d => d.TenantId == tenantId && d.Name == input.Name);
        if (existingSuite != null)
        {
            throw new UserFriendlyException(
                $"The Suite with the name {existingSuite.Name} already exists.");
        }

        var suite = new Suite
        {
            Name = input.Name,
            TenantId = _abpSession.GetTenantId(),
        };

        await _repository.InsertAsync(suite);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        if (input.ProjectIds.Count == 0) return suite;

        var assignProject = new AssignProjectSuiteDto
        {
            SuiteId = suite.Id,
            ProjectIds = input.ProjectIds,
        };
        await AssignProjectSuiteAsync(assignProject);

        return suite;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<SuiteDto>> GetAllAsync(PagedSuiteResultRequestDto input)
    {
        var query = _repository.GetAll()
            .AsNoTracking()
            .Include(x => x.SuiteProjects)
            .ThenInclude(x => x.Project)
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .OrderByDescending(r => r.CreationTime);

        var totalCount = await query.CountAsync();

        var suites = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .Select(x => new SuiteDto
            {
                Id = x.Id,
                Name = x.Name,
                Projects = x.SuiteProjects.Select(p => new ProjectDto
                {
                    Id = p.Project.Id,
                    Name = p.Project.Name,
                    TextColor = p.Project.TextColor,
                    BackgroundColor = p.Project.BackgroundColor,
                    IsActive = p.Project.IsActive
                }).ToList()
            })
            .ToListAsync();

        return new PagedResultDto<SuiteDto>(totalCount, _mapper.Map<List<SuiteDto>>(suites));
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<SuiteDto>> GetAllByLoggingViewAsync(
        PagedGeologySuiteByLoggingViewResultRequestDto input)
    {
        var projectIds = await _projectLoggingRepository.GetAll()
            .AsNoTracking()
            .Where(plv => plv.LoggingViewId == input.LoggingViewId)
            .Select(plv => plv.ProjectId)
            .Distinct()
            .ToListAsync();

        var query = _repository.GetAll()
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(projectIds.Any(),
                x => x.SuiteProjects.Any(p => projectIds.Contains(p.ProjectId)))
            .OrderBy(r => r.Name);

        var totalCount = await query.CountAsync();

        var suites = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        var suitesDto = _mapper.Map<List<SuiteDto>>(suites);
        return new PagedResultDto<SuiteDto>(totalCount, suitesDto);
    }

    /// <inheritdoc />
    public async Task<SuiteDto> UpdateAsync(UpdateSuiteDto input)
    {
        var suite = await ValidateSuiteEntity(input.Id);

        if (input.ProjectIds is { Count: > 0 })
        {
            var assignProject = new AssignProjectSuiteDto
            {
                SuiteId = suite.Id,
                ProjectIds = input.ProjectIds,
            };
            await AssignProjectSuiteAsync(assignProject);
        }

        suite.Name = input.Name;

        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task<SuiteDto> GetAsync(EntityDto<int> input)
    {
        var suite = await ValidateSuiteEntity(input.Id);

        var relateProject = await _projectRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.ProjectSuites.Any(y => y.SuiteId == suite.Id))
            .ToListAsync();

        var suiteDto = _mapper.Map<SuiteDto>(suite);
        suiteDto.Projects = _mapper.Map<List<ProjectDto>>(relateProject);

        return suiteDto;
    }

    /// <inheritdoc />
    public async Task DeleteAsync(EntityDto<int> input)
    {
        var suite = await ValidateSuiteEntity(input.Id);
        var isUsedInAttribute = await _suiteAttributeRepository.FirstOrDefaultAsync(sa => sa.SuiteId == input.Id);
        var isUsedInProject = await _projectSuiteRepository.FirstOrDefaultAsync(sp => sp.SuiteId == input.Id);

        if (isUsedInAttribute != null || isUsedInProject != null)
        {
            throw new UserFriendlyException(
                "Cannot delete Suite because it is associated with one or more Attributes or Projects.");
        }

        var isUsedInDownholeData =
            await _downholeDataRepository.FirstOrDefaultAsync(x => x.SuiteId == suite.Id);

        if (isUsedInDownholeData != null)
        {
            throw new UserFriendlyException(
                "Cannot delete Assay Suite because it is associated with one or more GeophysicsData.");
        }

        await _repository.DeleteAsync(input.Id);
    }

    /// <inheritdoc />
    public async Task<List<SuiteDto>> GetSuiteByProjectIdAsync(EntityDto<int> input)
    {
        var projectSuites = await _projectSuiteRepository.GetAll()
            .Where(sa => sa.ProjectId == input.Id)
            .Include(sa => sa.Suite)
            .ToListAsync();

        var suites = projectSuites.Select(sa => new SuiteDto
        {
            Id = sa.Suite.Id,
            Name = sa.Suite.Name,
        }).ToList();

        return suites;
    }

    private async Task<Suite> ValidateSuiteEntity(int id)
    {
        var suite = await _repository.FirstOrDefaultAsync(x => x.Id == id);
        if (suite == null)
        {
            throw new EntityNotFoundException(typeof(Suite), id);
        }

        return suite;
    }

    private async Task AssignProjectSuiteAsync(AssignProjectSuiteDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var existingProjects = await _projectSuiteRepository.GetAllListAsync(x =>
            x.SuiteId == input.SuiteId);

        var existingIds = existingProjects.Select(x => x.ProjectId).ToList();

        var projectsToAdd = input.ProjectIds.Except(existingIds).ToList();
        var projectsToDelete = existingIds.Except(input.ProjectIds).ToList();

        foreach (var projectSuite in projectsToAdd.Select(projectId =>
                     new ProjectSuite
                     {
                         ProjectId = projectId,
                         SuiteId = input.SuiteId,
                         TenantId = _abpSession.GetTenantId()
                     }))
        {
            await _projectSuiteRepository.InsertAsync(projectSuite);
        }

        foreach (var projectId in projectsToDelete)
        {
            await _projectSuiteRepository.DeleteAsync(x => x.ProjectId == projectId && x.SuiteId == input.SuiteId);
        }
    }
}