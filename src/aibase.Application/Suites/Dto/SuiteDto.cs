﻿using System.Collections.Generic;
using Abp.Application.Services.Dto;
using aibase.Projects.Dto;
using AutoMapper;

namespace aibase.Suites.Dto;

/// <inheritdoc />
[AutoMap(typeof(Suite))]
public class SuiteDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public string Name { get; set; } = string.Empty;
        
    /// <summary>
    ///
    /// </summary>
    public List<ProjectDto> Projects { get; set; } = [];
}