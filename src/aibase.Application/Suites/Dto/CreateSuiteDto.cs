﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using aibase.IsolationTenant;
using aibase.ProjectEntity;

namespace aibase.Suites.Dto;

/// <summary>
/// 
/// </summary>
public class CreateSuiteDto
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    ///
    /// </summary>
    [CheckTenantEntity(typeof(Project), isOptional: true)]
    public List<int> ProjectIds { get; set; } = [];
}