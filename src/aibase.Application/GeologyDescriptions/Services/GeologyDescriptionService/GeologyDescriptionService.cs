using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.GeologyDescriptions.Dto;
using aibase.GeologyFields;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.GeologyDescriptions.Services.GeologyDescriptionService;

/// <inheritdoc />
public class GeologyDescriptionService : IGeologyDescriptionService
{
    private readonly IRepository<GeologyDescription, int> _repository;
    private readonly IRepository<GeologyField, int> _geologyFieldRepository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="repository"></param>
    /// <param name="geologyFieldRepository"></param>
    /// <param name="abpSession"></param>
    /// <param name="mapper"></param>
    /// <param name="unitOfWorkManager"></param>
    public GeologyDescriptionService(
        IRepository<GeologyDescription, int> repository, 
        IRepository<GeologyField, int> geologyFieldRepository,
        IAbpSession abpSession,
        IMapper mapper, IUnitOfWorkManager unitOfWorkManager)
    {
        _repository = repository;
        _abpSession = abpSession;
        _mapper = mapper;
        _unitOfWorkManager = unitOfWorkManager;
        _geologyFieldRepository = geologyFieldRepository;
    }

    /// <inheritdoc />
    public async Task<GeologyDescription> CreateAsync(CreateGeologyDescriptionDto input, bool returnExist = false)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }
        var tenantId = _abpSession.GetTenantId();

        var existingGeologyDescription =
            await _repository.FirstOrDefaultAsync(d => d.TenantId == tenantId && d.Name == input.Name);
        if (existingGeologyDescription != null)
        {
            if (returnExist)
            {
                return existingGeologyDescription;
            }
            
            throw new UserFriendlyException($"The Geology Description with the name {existingGeologyDescription.Name} already exists.");
        }
        
        var geologyDescription = new GeologyDescription()
        {
            Name = input.Name,
            FieldHeight = input.FieldHeight,
            IsActive = input.IsActive,
            TenantId = tenantId,
        };

        await _repository.InsertAsync(geologyDescription);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        return geologyDescription;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<GeologyDescriptionDto>> GetAllAsync(PagedGeologyDescriptionResultRequestDto input)
    {
        var query = _repository.GetAll()
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .OrderBy(r => r.Name);

        var totalCount = await query.CountAsync();

        var descriptions = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        var descriptionDto = _mapper.Map<List<GeologyDescriptionDto>>(descriptions);

        return new PagedResultDto<GeologyDescriptionDto>(totalCount, descriptionDto);
    }

    /// <inheritdoc />
    public async Task<GeologyDescriptionDto> GetAsync(EntityDto<int> input)
    {
        var geologyDescription = await ValidateGeologyDescriptionEntity(input.Id);
        return _mapper.Map<GeologyDescriptionDto>(geologyDescription);
    }

    /// <inheritdoc />
    public async Task<GeologyDescriptionDto> UpdateAsync(UpdateGeologyDescriptionDto input)
    {
        var geologyDescription = await ValidateGeologyDescriptionEntity(input.Id);

        if (input.IsActive == false)
        {
            var geologyDateUsed =
                await _geologyFieldRepository.FirstOrDefaultAsync(x =>
                    x.Type == FieldType.Description && x.GeologyDescriptionId == input.Id);
            if (geologyDateUsed != null)
            {
                throw new UserFriendlyException("Cannot deactivate this record because it is already in use.");
            }
        }

        geologyDescription.Name = input.Name ?? geologyDescription.Name;
        geologyDescription.FieldHeight = input.FieldHeight ?? geologyDescription.FieldHeight;
        geologyDescription.IsActive = input.IsActive ?? geologyDescription.IsActive;

        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task DeleteAsync(EntityDto<int> input)
    {
        var geologyDescription = await ValidateGeologyDescriptionEntity(input.Id);

        var geologyFieldUsed = await _geologyFieldRepository.FirstOrDefaultAsync(x =>
            x.Type == FieldType.PickList && x.PickListId == input.Id);
        if (geologyFieldUsed != null)
        {
            throw new UserFriendlyException("Cannot delete this GeologyDescription because it is being used in GeologyField.");
        }
        
        await _repository.DeleteAsync(geologyDescription);
    }

    private async Task<GeologyDescription> ValidateGeologyDescriptionEntity(int id)
    {
        var geologyDescription = await _repository.FirstOrDefaultAsync(x => x.Id == id);

        if (geologyDescription == null)
        {
            throw new EntityNotFoundException(typeof(GeologyDescription), id);
        }

        return geologyDescription;
    }
}