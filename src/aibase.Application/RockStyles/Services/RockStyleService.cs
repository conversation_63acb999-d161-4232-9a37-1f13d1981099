using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.RockStyles.Dto;
using aibase.RockTypes;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.RockStyles.Services;

/// <inheritdoc />
public class RockStyleService : IRockStyleService
{
    private readonly IRepository<RockStyle, int> _repository;
    private readonly IRepository<RockType, int> _rockTypeRepository;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;
    private readonly IUnitOfWorkManager _unitOfWorkManager;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="repository"></param>
    /// <param name="rockTypeRepository"></param>
    /// <param name="abpSession"></param>
    /// <param name="mapper"></param>
    /// <param name="unitOfWorkManager"></param>
    public RockStyleService(IRepository<RockStyle, int> repository, IRepository<RockType, int> rockTypeRepository,
        IAbpSession abpSession, IMapper mapper, IUnitOfWorkManager unitOfWorkManager)
    {
        _repository = repository;
        _rockTypeRepository = rockTypeRepository;
        _abpSession = abpSession;
        _mapper = mapper;
        _unitOfWorkManager = unitOfWorkManager;
    }

    /// <inheritdoc />
    public async Task<RockStyle> CreateAsync(CreateRockStyleDto input, bool returnExist = false)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }
        var tenantId = _abpSession.GetTenantId();

        var existingRockStyle = await _repository
            .FirstOrDefaultAsync(x => x.Name == input.Name && x.TenantId == tenantId);
        if (existingRockStyle != null)
        {
            if (returnExist)
            {
                return existingRockStyle;
            }
            
            throw new UserFriendlyException($"A Drawing Style with the name {existingRockStyle.Name} already exists.");
        }

        var rockStyle = new RockStyle()
        {
            Name = input.Name,
            FillColor = input.FillColor,
            FillTexture = input.FillTexture,
            FillTransparency = input.FillTransparency,
            LineColor = input.LineColor,
            LineStyle = input.LineStyle,
            LineThickness = input.LineThickness,
            IsActive = input.IsActive,
            TenantId = tenantId,
        };

        await _repository.InsertAsync(rockStyle);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        return rockStyle;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<RockStyleDto>> GetAllAsync(PagedRockStyleResultRequestDto input)
    {
        var query = _repository.GetAll()
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
            .OrderBy(r => r.Name);

        var totalCount = await query.CountAsync();

        var rockStyles = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        return new PagedResultDto<RockStyleDto>(totalCount, _mapper.Map<List<RockStyleDto>>(rockStyles));
    }

    /// <inheritdoc />
    public async Task<RockStyleDto> UpdateAsync(UpdateRockStyleDto input)
    {
        var rockStyle = await ValidateRockStyleEntity(input.Id);

        if (input.IsActive == false)
        {
            var rockType = await _rockTypeRepository.FirstOrDefaultAsync(x => x.RockStyleId == input.Id);

            if (rockType != null)
            {
                throw new UserFriendlyException(
                    "Cannot deactivate a RockStyle that is already used in a RockType.");
            }
        }

        rockStyle.Name = input.Name ?? rockStyle.Name;
        rockStyle.FillColor = input.FillColor ?? rockStyle.FillColor;
        rockStyle.FillTexture = input.FillTexture ?? rockStyle.FillTexture;
        rockStyle.FillTransparency = input.FillTransparency ?? rockStyle.FillTransparency;
        rockStyle.LineColor = input.LineColor ?? rockStyle.LineColor;
        rockStyle.LineStyle = input.LineStyle ?? rockStyle.LineStyle;
        rockStyle.LineThickness = input.LineThickness ?? rockStyle.LineThickness;
        rockStyle.IsActive = input.IsActive ?? rockStyle.IsActive;

        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task<RockStyleDto> GetAsync(EntityDto<int> input)
    {
        var rockStyle = await ValidateRockStyleEntity(input.Id);
        return _mapper.Map<RockStyleDto>(rockStyle);
    }

    /// <inheritdoc />
    public async Task DeleteAsync(EntityDto<int> input)
    {
        await ValidateRockStyleEntity(input.Id);
        var isUsedInRockType = await _rockTypeRepository.FirstOrDefaultAsync(x => x.RockStyleId == input.Id);

        if (isUsedInRockType != null)
        {
            throw new UserFriendlyException(
                "Cannot delete RockStyle because it is associated with one or more RockTypes.");
        }

        await _repository.DeleteAsync(input.Id);
    }

    private async Task<RockStyle> ValidateRockStyleEntity(int id)
    {
        var rockStyle = await _repository.FirstOrDefaultAsync(x => x.Id == id);

        if (rockStyle == null)
        {
            throw new EntityNotFoundException(typeof(RockStyle), id);
        }

        return rockStyle;
    }
}