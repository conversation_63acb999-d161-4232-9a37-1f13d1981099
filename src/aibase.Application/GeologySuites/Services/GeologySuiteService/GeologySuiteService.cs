using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.Authorization.Roles;
using aibase.Authorization.Users;
using aibase.DataEntries;
using aibase.GeologyFields.Dto;
using aibase.GeologyProjectSuites;
using aibase.GeologySuiteFields.Dto;
using aibase.GeologySuiteFields.Services.GeologySuiteFieldService;
using aibase.GeologySuites.Dto;
using aibase.ProjectEntity;
using aibase.ProjectLoggingViews;
using aibase.Projects.Dto;
using aibase.WorkRoleSuites;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.GeologySuites.Services.GeologySuiteService;

/// <inheritdoc />
public class GeologySuiteService : IGeologySuiteService
{
    private readonly IRepository<GeologySuite, int> _repository;
    private readonly IRepository<GeologyProjectSuite, int> _geologyProjectSuiteRepository;
    private readonly IRepository<WorkRoleSuite, int> _workRoleSuiteRepository;
    private readonly IRepository<ProjectLoggingView, int> _projectLoggingRepository;
    private readonly IRepository<Project, int> _projectRepository;
    private readonly IRepository<DataEntry, int> _dataEntryRepository;
    private readonly UserManager _userManager;
    private readonly IGeologySuiteFieldService _geologySuiteFieldService;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    public GeologySuiteService(
        IRepository<GeologySuite, int> repository,
        IRepository<GeologyProjectSuite, int> geologyProjectSuiteRepository,
        IRepository<WorkRoleSuite, int> workRoleSuiteRepository,
        IRepository<ProjectLoggingView, int> projectLoggingRepository,
        UserManager userManager,
        IGeologySuiteFieldService geologySuiteFieldService,
        IAbpSession abpSession,
        IMapper mapper,
        IUnitOfWorkManager unitOfWorkManager, IRepository<Project, int> projectRepository,
        IRepository<DataEntry, int> dataEntryRepository)
    {
        _repository = repository;
        _geologyProjectSuiteRepository = geologyProjectSuiteRepository;
        _geologySuiteFieldService = geologySuiteFieldService;
        _workRoleSuiteRepository = workRoleSuiteRepository;
        _projectLoggingRepository = projectLoggingRepository;
        _userManager = userManager;
        _abpSession = abpSession;
        _mapper = mapper;
        _unitOfWorkManager = unitOfWorkManager;
        _projectRepository = projectRepository;
        _dataEntryRepository = dataEntryRepository;
    }

    /// <inheritdoc />
    public async Task<GeologySuite> CreateAsync(CreateGeologySuiteDto input, bool returnExist = false)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var tenantId = _abpSession.GetTenantId();

        var existingGeologySuite =
            await _repository.FirstOrDefaultAsync(d => d.TenantId == tenantId && d.Name == input.Name);
        if (existingGeologySuite != null)
        {
            if (returnExist)
            {
                return existingGeologySuite;
            }

            throw new UserFriendlyException(
                $"The Geology Suite with the name {existingGeologySuite.Name} already exists.");
        }

        var geologySuite = new GeologySuite
        {
            Name = input.Name,
            IsActive = input.IsActive,
            IsGeotech = input.IsGeotech ?? false,
            TenantId = tenantId,
        };

        await _repository.InsertAsync(geologySuite);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        if (input.ProjectIds.Count == 0) return geologySuite;

        var geologyProjectSuite = new AssignProjectGeologySuiteDto()
        {
            GeologySuiteId = geologySuite.Id,
            ProjectIds = input.ProjectIds,
        };
        await AssignProjectGeologySuiteAsync(geologyProjectSuite);

        return geologySuite;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<GeologySuiteDto>> GetAllAsync(PagedGeologySuiteResultRequestDto input)
    {
        using (_unitOfWorkManager.Current.DisableFilter(AbpDataFilters.MayHaveTenant))
        {
            if (_abpSession.TenantId == null)
            {
                throw new UserFriendlyException("The account does not have permission to perform this feature.");
            }

            var currentUser = await _userManager.GetUserByIdAsync(_abpSession.GetUserId());
            var roles = await _userManager.GetRolesAsync(currentUser);
            var isCompany = roles.Contains(StaticRoleNames.Tenants.Admin);

            var geologySuiteIds = await _workRoleSuiteRepository.GetAll()
                .AsNoTracking()
                .Where(x => x.WorkRoleId == currentUser.WorkRoleId)
                .Select(x => x.GeologySuiteId)
                .ToListAsync();

            var query = _repository.GetAll()
                .Include(x => x.GeologySuiteFields)
                .ThenInclude(x => x.GeologyField)
                .Include(x => x.GeologyProjectSuites)
                .ThenInclude(x => x.Project)
                .AsNoTracking()
                .WhereIf(!isCompany, x => geologySuiteIds.Contains(x.Id))
                .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
                .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
                .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
                .WhereIf(input.ProjectId.HasValue, x => x.GeologyProjectSuites.Any(p => p.ProjectId == input.ProjectId))
                .OrderBy(r => r.Name);

            var totalCount = await query.CountAsync();

            var geologySuites = await query
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount)
                .Select(x => new GeologySuiteDto
                {
                    Id = x.Id,
                    Name = x.Name,
                    IsActive = x.IsActive,
                    IsGeotech = x.IsGeotech,
                    GeologySuiteFields = x.GeologySuiteFields.Select(f => new GeologySuiteFieldDto
                    {
                        Id = f.Id,
                        GeologyField = new GeologyFieldDto
                        {
                            Id = f.GeologyField.Id,
                            Name = f.GeologyField.Name
                        }
                    }).ToList(),
                    Projects = x.GeologyProjectSuites.Select(p => new ProjectDto
                    {
                        Id = p.Project.Id,
                        Name = p.Project.Name,
                        TextColor = p.Project.TextColor,
                        BackgroundColor = p.Project.BackgroundColor,
                        IsActive = p.Project.IsActive
                    }).ToList()
                })
                .ToListAsync();

            return new PagedResultDto<GeologySuiteDto>(totalCount, geologySuites);
        }
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<GeologySuiteDto>> GetAllByLoggingViewAsync(
        PagedGeologySuiteByLoggingViewResultRequestDto input)
    {
        var projectIds = await _projectLoggingRepository.GetAll()
            .AsNoTracking()
            .Where(plv => plv.LoggingViewId == input.LoggingViewId)
            .Select(plv => plv.ProjectId)
            .Distinct()
            .ToListAsync();

        var query = _repository.GetAll()
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(projectIds.Count != 0,
                x => x.GeologyProjectSuites.Any(p => projectIds.Contains(p.ProjectId)))
            .OrderBy(r => r.Name);

        var totalCount = await query.CountAsync();

        var geologySuites = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        var geologySuiteDto = _mapper.Map<List<GeologySuiteDto>>(geologySuites);
        return new PagedResultDto<GeologySuiteDto>(totalCount, geologySuiteDto);
    }

    /// <inheritdoc />
    public async Task<GeologySuiteDto> GetAsync(EntityDto<int> input)
    {
        var geologySuite = await ValidateGeologySuiteEntity(input.Id);

        var relateProject = await _projectRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.GeologyProjectSuites.Any(y => y.GeologySuiteId == geologySuite.Id))
            .ToListAsync();

        var geologySuiteDto = _mapper.Map<GeologySuiteDto>(geologySuite);
        geologySuiteDto.Projects = _mapper.Map<List<ProjectDto>>(relateProject);

        if (geologySuiteDto.GeologySuiteFields != null)
        {
            await _geologySuiteFieldService.PopulateRelatedFieldsAsync(geologySuiteDto.GeologySuiteFields);
        }

        geologySuiteDto.GeologySuiteFields = geologySuiteDto.GeologySuiteFields?
            .OrderBy(field => field.Sequence)
            .ThenBy(field => field.Name)
            .ToList() ?? [];

        return geologySuiteDto;
    }

    /// <inheritdoc />
    public async Task<GeologySuiteDto> UpdateAsync(UpdateGeologySuiteDto input)
    {
        var geologySuite = await ValidateGeologySuiteEntity(input.Id);

        if (input.ProjectIds != null)
        {
            var geologyProjectSuite = new AssignProjectGeologySuiteDto()
            {
                GeologySuiteId = geologySuite.Id,
                ProjectIds = input.ProjectIds,
            };
            await AssignProjectGeologySuiteAsync(geologyProjectSuite);
        }


        geologySuite.Name = input.Name ?? geologySuite.Name;
        geologySuite.IsActive = input.IsActive ?? geologySuite.IsActive;
        geologySuite.IsGeotech = input.IsGeotech ?? geologySuite.IsActive;

        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task DeleteAsync(EntityDto<int> input)
    {
        await ValidateGeologySuiteEntity(input.Id);

        var isUsedInProject =
            await _geologyProjectSuiteRepository.FirstOrDefaultAsync(x => x.GeologySuiteId == input.Id);

        if (isUsedInProject != null)
        {
            throw new UserFriendlyException(
                "Cannot delete Geology Suite because it is associated with one or more Projects.");
        }

        var isUsedInDataEntry =
            await _dataEntryRepository.FirstOrDefaultAsync(x => x.GeologySuiteId == input.Id);

        if (isUsedInDataEntry != null)
        {
            throw new UserFriendlyException(
                "Cannot delete Geology Suite because it is associated with one or more DataEntry.");
        }

        await _repository.DeleteAsync(input.Id);
    }

    private async Task<GeologySuite> ValidateGeologySuiteEntity(int id)
    {
        var geologySuite = await _repository.GetAllIncluding(x => x.GeologySuiteFields)
            .Include(x => x.GeologySuiteFields)
            .ThenInclude(x => x.GeologyField)
            .FirstOrDefaultAsync(x => x.Id == id);

        if (geologySuite == null)
        {
            throw new EntityNotFoundException(typeof(GeologySuite), id);
        }

        return geologySuite;
    }

    private async Task AssignProjectGeologySuiteAsync(AssignProjectGeologySuiteDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var existingProjects = await _geologyProjectSuiteRepository.GetAllListAsync(x =>
            x.GeologySuiteId == input.GeologySuiteId);

        var existingIds = existingProjects.Select(x => x.ProjectId).ToList();

        var projectsToAdd = input.ProjectIds.Except(existingIds).ToList();
        var projectsToDelete = existingIds.Except(input.ProjectIds).ToList();

        foreach (var projectGeologySuite in projectsToAdd.Select(projectId =>
                     new GeologyProjectSuite
                     {
                         ProjectId = projectId,
                         GeologySuiteId = input.GeologySuiteId,
                         TenantId = _abpSession.GetTenantId()
                     }))
        {
            await _geologyProjectSuiteRepository.InsertAsync(projectGeologySuite);
        }

        foreach (var projectId in projectsToDelete)
        {
            await _geologyProjectSuiteRepository.DeleteAsync(x =>
                x.GeologySuiteId == input.GeologySuiteId && x.ProjectId == projectId);
        }
    }
}