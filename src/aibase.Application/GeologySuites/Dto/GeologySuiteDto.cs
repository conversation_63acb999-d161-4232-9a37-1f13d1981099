using System.Collections.Generic;
using Abp.Application.Services.Dto;
using aibase.GeologySuiteFields.Dto;
using aibase.Projects.Dto;
using AutoMapper;

namespace aibase.GeologySuites.Dto
{
    /// <inheritdoc />
    [AutoMap(typeof(GeologySuite))]
    public class GeologySuiteDto : EntityDto
    {
        /// <summary>
        /// 
        /// </summary>
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// 
        /// </summary>
        public bool IsActive { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public bool IsGeotech { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public List<GeologySuiteFieldDto>? GeologySuiteFields { get; set; }
        
        /// <summary>
        ///
        /// </summary>
        public List<ProjectDto> Projects { get; set; } = [];
    }
}