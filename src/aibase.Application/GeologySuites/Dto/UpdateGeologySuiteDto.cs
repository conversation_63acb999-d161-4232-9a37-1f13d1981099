using System.Collections.Generic;
using Abp.Application.Services.Dto;
using Abp.AutoMapper;

namespace aibase.GeologySuites.Dto
{
    /// <summary>
    /// 
    /// </summary>
    [AutoMap(typeof(GeologySuite))]
    public class UpdateGeologySuiteDto : EntityDto
    {
        /// <summary>
        /// 
        /// </summary>
        public string? Name { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public bool? IsActive { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public bool? IsGeotech { get; set; }
        
        /// <summary>
        ///
        /// </summary>
        public List<int>? ProjectIds { get; set; } = [];
    }
}