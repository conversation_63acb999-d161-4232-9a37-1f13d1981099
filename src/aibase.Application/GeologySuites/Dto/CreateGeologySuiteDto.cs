using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace aibase.GeologySuites.Dto
{
    /// <summary>
    /// 
    /// </summary>
    public class CreateGeologySuiteDto
    {
        /// <summary>
        /// 
        /// </summary>
        [Required]
        public string Name { get; set; } = string.Empty;
    
        /// <summary>
        /// 
        /// </summary>
        [Required] 
        public bool IsActive { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public bool? IsGeotech { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public List<int> ProjectIds { get; set; } = [];
    }
}