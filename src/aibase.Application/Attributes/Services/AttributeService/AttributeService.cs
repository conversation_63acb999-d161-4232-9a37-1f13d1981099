using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.Attributes.Dto;
using aibase.DownholeDatas;
using aibase.ProjectSuites;
using aibase.SuiteAttributes;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.Attributes.Services.AttributeService;

/// <inheritdoc />
public class AttributeService : IAttributeService
{
    private readonly IRepository<Attribute, int> _repository;
    private readonly IRepository<SuiteAttribute, int> _suiteAttributeRepository;
    private readonly IRepository<ProjectSuite, int> _projectSuiteRepository;
    private readonly IRepository<DownholeData, int> _downholeDataRepository;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="repository"></param>
    /// <param name="suiteAttributeRepository"></param>
    /// <param name="projectSuiteRepository"></param>
    /// <param name="downholeDataRepository"></param>
    /// <param name="abpSession"></param>
    /// <param name="mapper"></param>
    public AttributeService(
        IRepository<Attribute, int> repository,
        IRepository<SuiteAttribute, int> suiteAttributeRepository,
        IRepository<ProjectSuite, int> projectSuiteRepository,
        IRepository<DownholeData, int> downholeDataRepository,
        IAbpSession abpSession,
        IMapper mapper)
    {
        _repository = repository;
        _suiteAttributeRepository = suiteAttributeRepository;
        _projectSuiteRepository = projectSuiteRepository;
        _downholeDataRepository = downholeDataRepository;
        _abpSession = abpSession;
        _mapper = mapper;
    }

    /// <inheritdoc />
    public async Task<Attribute> CreateAsync(CreateAttributeDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var attribute = new Attribute
        {
            Name = input.Name,
            Code = input.Code,
            MinInterval = input.MinInterval,
            MaxValue = input.MaxValue,
            MinValue = input.MinValue,
            TextColor = input.TextColor,
            BackgroundColor = input.BackgroundColor,
            IsActive = input.IsActive,
            TenantId = _abpSession.GetTenantId(),
        };
        await _repository.InsertAsync(attribute);

        return attribute;
    }

    /// <inheritdoc />
    public async Task<AttributeDto> UpdateAsync(UpdateAttributeDto input)
    {
        var attribute = await ValidateAttributeEntity(input.Id);

        attribute.Name = input.Name ?? attribute.Name;
        attribute.Code = input.Code ?? attribute.Code;
        attribute.MinInterval = input.MinInterval ?? attribute.MinInterval;
        attribute.MaxValue = input.MaxValue ?? attribute.MaxValue;
        attribute.MinValue = input.MinValue ?? attribute.MinValue;
        attribute.TextColor = input.TextColor ?? attribute.TextColor;
        attribute.BackgroundColor = input.BackgroundColor ?? attribute.BackgroundColor;
        attribute.IsActive = input.IsActive ?? attribute.IsActive;

        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<AttributeDto>> GetAllAsync(PagedAttributeResultRequestDto input)
    {
        var query = _repository.GetAll()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .OrderByDescending(r => r.CreationTime);

        var totalCount = await query.CountAsync();

        var attributes = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        return new PagedResultDto<AttributeDto>(totalCount, _mapper.Map<List<AttributeDto>>(attributes));
    }

    /// <inheritdoc />
    public async Task<AttributeDto> GetAsync(EntityDto<int> input)
    {
        var attribute = await ValidateAttributeEntity(input.Id);
        return _mapper.Map<AttributeDto>(attribute);
    }

    /// <inheritdoc />
    public async Task DeleteAsync(EntityDto<int> input)
    {
        var attribute = await ValidateAttributeEntity(input.Id);

        var isUsedInSuite = await _suiteAttributeRepository.FirstOrDefaultAsync(sa => sa.AttributeId == input.Id);
        if (isUsedInSuite != null)
        {
            throw new UserFriendlyException(
                "Cannot delete Attribute because it is associated with one or more Suites.");
        }

        await _repository.DeleteAsync(attribute.Id);
    }

    /// <inheritdoc />
    public async Task<List<AttributeDto>> GetAttributesBySuiteIdAsync(EntityDto<int> input)
    {
        var suiteAttributes = await _suiteAttributeRepository.GetAll()
            .Where(sa => sa.SuiteId == input.Id)
            .Include(sa => sa.Attribute)
            .ToListAsync();

        var attributes = suiteAttributes.Select(sa => new AttributeDto
        {
            Id = sa.Attribute.Id,
            Name = sa.Attribute.Name,
            Code = sa.Attribute.Code,
            MinInterval = sa.Attribute.MinInterval,
            MaxValue = sa.Attribute.MaxValue,
            MinValue = sa.Attribute.MinValue,
            BackgroundColor = sa.Attribute.BackgroundColor,
            TextColor = sa.Attribute.TextColor,
            IsActive = sa.Attribute.IsActive
        }).ToList();

        return attributes;
    }

    /// <inheritdoc />
    public async Task<List<AttributeDto>> GetAttributesByProjectIdAsync(EntityDto<int> input, string? drillHole)
    {
        var projectSuites = await _projectSuiteRepository.GetAll()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .Where(ps => ps.ProjectId == input.Id)
            .ToListAsync();

        var suiteIds = projectSuites.Select(ps => ps.SuiteId).ToList();
        var allAttributes = await _suiteAttributeRepository.GetAll()
            .Where(sa => suiteIds.Contains(sa.SuiteId))
            .Select(sa => sa.Attribute)
            .ToListAsync();

        var attributeNameByDrillHole = await _downholeDataRepository.GetAll()
            .Where(x => x.ProjectId == input.Id && x.DrillHole == drillHole)
            .GroupBy(x => x.AttributeName)
            .Select(g => g.Key)
            .ToListAsync();

        return allAttributes
            .Where(sa => attributeNameByDrillHole.Contains(sa.Name))
            .Select(sa => new AttributeDto
            {
                Id = sa.Id,
                Name = sa.Name,
                Code = sa.Code,
                MinInterval = sa.MinInterval,
                MaxValue = sa.MaxValue,
                MinValue = sa.MinValue,
                BackgroundColor = sa.BackgroundColor,
                TextColor = sa.TextColor,
                IsActive = sa.IsActive
            }).ToList();
    }

    private async Task<Attribute> ValidateAttributeEntity(int id)
    {
        var attribute = await _repository.FirstOrDefaultAsync(x => x.Id == id);

        if (attribute == null)
        {
            throw new EntityNotFoundException(typeof(Attribute), id);
        }

        return attribute;
    }
}