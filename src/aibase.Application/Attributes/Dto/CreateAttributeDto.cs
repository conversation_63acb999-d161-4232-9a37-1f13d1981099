﻿using System.ComponentModel.DataAnnotations;

namespace aibase.Attributes.Dto;

/// <summary>
/// 
/// </summary>
public class CreateAttributeDto
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [Required]
    [MaxLength(3)]
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public double MinInterval { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public double MinValue { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public double MaxValue { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public string TextColor { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public string BackgroundColor { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public bool IsActive { get; set; }

}