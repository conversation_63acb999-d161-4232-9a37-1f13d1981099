﻿using Abp.Application.Services.Dto;
using AutoMapper;

namespace aibase.Attributes.Dto;

/// <inheritdoc />
[AutoMap(typeof(Attribute))]
public class UpdateAttributeDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public string? Name { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public string? Code { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public double? MinInterval { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public double? MinValue { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public double? MaxValue { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public string? TextColor { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public string? BackgroundColor { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public bool? IsActive { get; set; }
}