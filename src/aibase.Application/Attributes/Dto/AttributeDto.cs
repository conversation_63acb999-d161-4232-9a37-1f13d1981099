﻿using Abp.Application.Services.Dto;
using AutoMapper;

namespace aibase.Attributes.Dto;

/// <inheritdoc />
[AutoMap(typeof(Attribute))]
public class AttributeDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public string Name { get; set; } = string.Empty;
        
    /// <summary>
    /// 
    /// </summary>
    public string Code { get; set; } = string.Empty;
        
    /// <summary>
    /// 
    /// </summary>
    public double MinInterval { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public double MinValue { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public double MaxValue { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public string TextColor { get; set; } = string.Empty;
        
    /// <summary>
    /// 
    /// </summary>
    public string BackgroundColor { get; set; } = string.Empty;
        
    /// <summary>
    /// 
    /// </summary>
    public bool IsActive { get; set; }
}