﻿using Abp.Application.Services.Dto;
using aibase.SourceTypeWorkflowEntity;
using aibase.StepWorkflowEntity;
using AutoMapper;
using aibase.Polygons.Dto;
using System;
using System.Collections.Generic;

namespace aibase.StepWorkflows.Dto;

/// <inheritdoc />
[AutoMap(typeof(StepWorkflow))]
public class StepDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public string Name { get; set; } = string.Empty;
        
    /// <summary>
    /// 
    /// </summary>
    public int WorkflowId { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public ProcessType ProcessType { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public AiModelType? ModelId { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public ToolType? ToolType { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public int? PolygonId { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public DataSourceType DataSourceType { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public string DataValue { get; set; } = string.Empty;
        
    /// <summary>
    /// 
    /// </summary>
    public OutputType OutputType { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public int? BoundingBoxId { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public PolygonDto? BoundingBox {  get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public int? BoundingRowsId { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public PolygonDto? BoundingRows { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public BoundingRowOption? BoundingRowOption { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Boolean? SegmentFlag { get; set; }


    /// <summary>
    /// 
    /// </summary>
    public string? Prompt { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public bool IsCropAdditional { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public List<int> ImageTypesAdditional { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    public List<int> ImageSubtypesAdditional { get; set; } = [];
}