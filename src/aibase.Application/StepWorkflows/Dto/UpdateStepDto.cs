﻿using System;
using System.Collections.Generic;
using Abp.Application.Services.Dto;
using aibase.SourceTypeWorkflowEntity;
using aibase.StepWorkflowEntity;
using AutoMapper;

namespace aibase.StepWorkflows.Dto;

/// <inheritdoc />
[AutoMap(typeof(StepWorkflow))]
public class UpdateStepDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? WorkflowId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public ProcessType? ProcessType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public AiModelType? ModelId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public ToolType? ToolType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? PolygonId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public DataSourceType? DataSourceType { get; set; }
        
    public Boolean? SegmentFlag { get; set; } = false;

    /// <summary>
    /// 
    /// </summary>
    public string? DataValue { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public OutputType? OutputType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? BoundingBoxId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? BoundingBox {  get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? BoundingRowsId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? BoundingRows { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public BoundingRowOption? BoundingRowOption { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public string? Prompt { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public bool? IsCropAdditional { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public List<int>? ImageTypesAdditional { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    public List<int>? ImageSubtypesAdditional { get; set; } = [];
}