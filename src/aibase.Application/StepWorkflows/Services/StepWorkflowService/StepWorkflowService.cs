using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.StepWorkflowEntity;
using aibase.StepWorkflows.Dto;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.StepWorkflows.Services.StepWorkflowService;

/// <inheritdoc />
public class StepWorkflowService : IStepWorkflowService
{
    private readonly IRepository<StepWorkflow, int> _repository;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="repository"></param>
    /// <param name="abpSession"></param>
    /// <param name="mapper"></param>
    public StepWorkflowService(IRepository<StepWorkflow, int> repository, IAbpSession abpSession, IMapper mapper)
    {
        _repository = repository;
        _abpSession = abpSession;
        _mapper = mapper;
    }

    /// <inheritdoc />
    public async Task<StepWorkflow> CreateAsync(CreateStepDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }
        
        var step = new StepWorkflow
        {
            Name = input.Name,
            WorkflowId = input.WorkflowId,
            ProcessType = input.ProcessType,
            ModelId = input.ModelId,
            ToolType = input.ToolType,
            PolygonId = input.PolygonId,
            DataSourceType = input.DataSourceType,
            SegmentFlag = input.SegmentFlag,
            DataValue = input.DataValue,
            OutputType = input.OutputType,
            BoundingBoxId = input.BoundingBoxId,
            BoundingRowsId = input.BoundingRowsId,
            BoundingRowOption = input.BoundingRowOption ?? BoundingRowOption.Polygon,
            Prompt = input.Prompt,
            IsCropAdditional = input.IsCropAdditional,
            ImageTypesAdditional  = input.ImageTypesAdditional,
            ImageSubtypesAdditional = input.ImageSubtypesAdditional
        };

        await _repository.InsertAsync(step);
        return step;
    }

    /// <inheritdoc />
    public async Task<StepDto> UpdateAsync(UpdateStepDto input)
    {
        var step = await ValidateStepWorkflowEntity(input.Id);
        
        step.Name = input.Name ?? step.Name;
        step.WorkflowId = input.WorkflowId ?? step.WorkflowId;
        step.ProcessType = input.ProcessType ?? step.ProcessType;
        step.ModelId = input.ModelId ?? step.ModelId;
        step.ToolType = input.ToolType ?? step.ToolType;
        step.PolygonId = input.PolygonId ?? step.PolygonId;
        step.DataSourceType = input.DataSourceType ?? step.DataSourceType;
        step.SegmentFlag = input.SegmentFlag ?? step.SegmentFlag;
        step.DataValue = input.DataValue ?? step.DataValue;
        step.OutputType = input.OutputType ?? step.OutputType;
        step.BoundingBoxId = input.BoundingBoxId ?? step.BoundingBoxId;
        step.BoundingRowsId = input.BoundingRowsId ?? step.BoundingRowsId;
        step.BoundingRowOption = input.BoundingRowOption ?? step.BoundingRowOption;
        step.Prompt = input.Prompt ?? step.Prompt;
        step.IsCropAdditional = input.IsCropAdditional ?? step.IsCropAdditional;
        step.ImageTypesAdditional = input.ImageTypesAdditional;
        step.ImageSubtypesAdditional = input.ImageSubtypesAdditional;
        
        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<StepDto>> GetAllAsync(PagedStepResultRequestDto input)
    {
        var query = _repository.GetAllIncluding(x => x.BoundingBox, x => x.BoundingRows)
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .WhereIf(input.WorkFlowId.HasValue, x => x.WorkflowId == input.WorkFlowId)
            .OrderBy(r => r.CreationTime);

        var totalCount = await query.CountAsync();

        var step = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        var stepsDto = _mapper.Map<List<StepDto>>(step);

        return new PagedResultDto<StepDto>(totalCount, stepsDto);
    }

    /// <inheritdoc />
    public async Task<StepDto> GetAsync(EntityDto<int> input)
    {
        var step = await ValidateStepWorkflowEntity(input.Id);
        return _mapper.Map<StepDto>(step);
    }
    
    private async Task<StepWorkflow> ValidateStepWorkflowEntity(int id)
    {
        var stepWorkflow = await _repository.GetAllIncluding(x => x.BoundingBox, x => x.BoundingRows).FirstOrDefaultAsync(x => x.Id == id);

        if (stepWorkflow == null)
        {
            throw new EntityNotFoundException(typeof(StepWorkflow), id);
        }

        return stepWorkflow;
    }
}