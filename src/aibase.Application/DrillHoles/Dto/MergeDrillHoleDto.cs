using System.ComponentModel.DataAnnotations;

namespace aibase.DrillHoles.Dto;

/// <summary>
/// 
/// </summary>
public class MergeDrillHoleDto
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int MasterDrillHoleId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int SourceDrillHoleId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool? IsInactivate { get; set; }
}