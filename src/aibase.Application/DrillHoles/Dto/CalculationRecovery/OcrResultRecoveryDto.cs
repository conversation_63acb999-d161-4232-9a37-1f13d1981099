namespace aibase.DrillHoles.Dto.CalculationRecovery;

/// <summary>
/// 
/// </summary>
public class OcrResultRecoveryDto
{
    /// <summary>
    /// 
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public double X { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double OriginalX { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double Y { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double Width { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double Height { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string Text { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public double Probability { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? RowIndex { get; set; } = -1;

    /// <summary>
    /// 
    /// </summary>
    public int ImageCropId { get; set; }
}