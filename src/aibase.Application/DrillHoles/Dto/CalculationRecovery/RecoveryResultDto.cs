using Abp.Application.Services.Dto;
using aibase.RecoveryResults;
using AutoMapper;

namespace aibase.DrillHoles.Dto.CalculationRecovery;

/// <summary>
/// 
/// </summary>
[AutoMap(typeof(RecoveryResult))]
public class RecoveryResultDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public double OcrValueFrom { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double FromX { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double FromY { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int FromImageCropId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int FromRowIndex { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string FromOcrId { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public double OcrValueTo { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double ToX { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double ToY { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int ToImageCropId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int ToRowIndex { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? ToOcrId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double Length { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double Recovery { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double DepthInterval { get; set; }
}