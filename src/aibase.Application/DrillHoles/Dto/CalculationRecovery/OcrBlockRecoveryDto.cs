namespace aibase.DrillHoles.Dto.CalculationRecovery;

/// <summary>
/// 
/// </summary>
public class OcrBlockRecoveryDto
{
    /// <summary>
    /// 
    /// </summary>
    public double X { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double OriginalX { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double Y { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double Width { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double Height { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double Value { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int RowIndex { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int ImageCropId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? id { get; set; }
}