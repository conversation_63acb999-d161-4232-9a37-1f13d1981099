﻿using System.Collections.Generic;
using aibase.Images.Dto;

namespace aibase.DrillHoles.Dto.CalculationRecovery;

/// <summary>
/// 
/// </summary>
public class RowSegmentDto
{
    /// <summary>
    /// 
    /// </summary>
    public double X { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double Y { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double Width { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double Height { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double DepthFrom { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double DepthTo { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public object? Id { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<OcrResultRecoveryDto> OcrResults { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    public LineCoordinateDto MiddleLine { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<List<List<double>>> Points { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    public List<LineCoordinateDto> IntersectionPoints { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    public List<CoordinateV2Dto> BoundingSegment { get; set; } = [];
}