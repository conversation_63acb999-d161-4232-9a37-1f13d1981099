﻿using Abp.Application.Services.Dto;
using aibase.DrillHoleEntity;

namespace aibase.DrillHoles.Dto;

/// <inheritdoc />
public class PagedDrillHoleResultRequestDto : PagedResultRequestDto
{
    /// <summary>
    /// 
    /// </summary>
    public string? Keyword { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public DrillHoleStatus? DrillHoleStatus { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? ProjectIds { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? ProspectIds { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? SortField { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? SortOrder { get; set; }
}