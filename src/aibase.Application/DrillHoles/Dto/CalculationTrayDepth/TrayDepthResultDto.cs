using Abp.Application.Services.Dto;
using aibase.TrayDepthResults;
using AutoMapper;

namespace aibase.DrillHoles.Dto.CalculationTrayDepth;

/// <inheritdoc />
[AutoMap(typeof(TrayDepthResult))]
public class TrayDepthResultDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public int DrillHoleId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int TrayNumber { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double StartDepth { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double EndDepth { get; set; }
}