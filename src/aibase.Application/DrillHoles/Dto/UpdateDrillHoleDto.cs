﻿using System.ComponentModel.DataAnnotations;
using Abp.Application.Services.Dto;
using aibase.DrillHoleEntity;
using aibase.IsolationTenant;
using aibase.ProjectEntity;
using aibase.Prospects;
using AutoMapper;

namespace aibase.DrillHoles.Dto;

/// <inheritdoc />
[AutoMap(typeof(DrillHole))]
public class UpdateDrillHoleDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public DrillHoleStatus? DrillHoleStatus { get; set; }

    /// <summary>
    /// Height above sea level
    /// </summary>
    public decimal? Elevation { get; set; }

    /// <summary>
    /// Y coordinate in projected coordinate system
    /// </summary>
    public decimal? Northing { get; set; }

    /// <summary>
    /// X coordinate in projected coordinate system
    /// </summary>
    public decimal? Easting { get; set; }

    /// <summary>
    /// Geographic longitude coordinate
    /// </summary>
    public decimal? Longitude { get; set; }

    /// <summary>
    /// Geographic latitude coordinate
    /// </summary>
    public decimal? Latitude { get; set; }

    /// <summary>
    /// Angle of the drill hole from horizontal
    /// </summary>
    public decimal? Dip { get; set; }

    /// <summary>
    /// Direction angle from true north
    /// </summary>
    public decimal? Azimuth { get; set; }

    /// <summary>
    ///
    /// </summary>
    public double? MaxDepth { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    [CheckTenantEntity(typeof(Project), isOptional: false)]
    public int ProjectId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    [CheckTenantEntity(typeof(Prospect), isOptional: false)]
    public int ProspectId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int OriginalImages { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? CroppedRows { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool? IsExport { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool? IsActive { get; set; }
}