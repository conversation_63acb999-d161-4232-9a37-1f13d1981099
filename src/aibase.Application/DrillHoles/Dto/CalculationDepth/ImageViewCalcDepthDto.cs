using System.Collections.Generic;

namespace aibase.DrillHoles.Dto.CalculationDepth;

/// <summary>
/// 
/// </summary>
public class ImageViewCalcDepthDto
{
    /// <summary>
    /// 
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double DepthFrom { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double DepthTo { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? OcrResult { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? SegmentResult { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<CroppedImageViewCalcDepthDto> CroppedImages { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    public int GetIndex(List<ImageViewCalcDepthDto> list) => list.IndexOf(this);
}