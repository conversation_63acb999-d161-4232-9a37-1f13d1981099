using System.Collections.Generic;

namespace aibase.DrillHoles.Dto.CalculationDepth;

/// <summary>
/// 
/// </summary>
public class CalculateDepthInListImageDto
{
    /// <summary>
    /// 
    /// </summary>
    public List<ImageViewCalcDepthDto> Images { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    public int ImageCropId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double X { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double CoreTray { get; set; }
}