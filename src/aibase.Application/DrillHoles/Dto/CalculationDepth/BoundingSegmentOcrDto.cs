using System.Collections.Generic;
using aibase.Models.Dto;

namespace aibase.DrillHoles.Dto.CalculationDepth;

/// <summary>
/// 
/// </summary>
public class BoundingSegmentOcrDto
{
    /// <summary>
    /// 
    /// </summary>
    public double X { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double Y { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double Width { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double Height { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int RowIndex { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<OcrResultV2Dto> OcrResults { get; set; } = [];
}