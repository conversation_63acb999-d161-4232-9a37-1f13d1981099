using System.Collections.Generic;
using System.Threading.Tasks;
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using aibase.DrillHoles.Dto;
using aibase.DrillHoles.Dto.CalculationDepth;
using aibase.DrillHoles.Dto.CalculationRecovery;
using aibase.DrillHoles.Dto.CalculationTrayDepth;

namespace aibase.DrillHoles;

/// <inheritdoc />
public interface IDrillHoleAppService : IAsyncCrudAppService<DrillHoleDto, int, PagedDrillHoleResultRequestDto,
    CreateDrillHoleDto, UpdateDrillHoleDto>
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="name"></param>
    /// <returns></returns>
    Task<DrillHoleDto> GetByNameAsync(string name);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagedResultDto<RecoveryResultDto>> GetRecoveryByDrillHoleAsync(PagedRecoveryHoleResultRequestDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<RecoveryResultDto>> CalculateRecoveryAsync(CalculateRecoveryDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<double?> CalculateDepthBySelectPointAsync(CalculateDepthBySelectPointDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagedResultDto<DrillHoleDto>> GetDrillHoleByProjectProspectAsync(GetDrillHoleByProjectProspectDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<RecoveryResultDto> UpdateRecoveryResultAsync(UpdateRecoveryResultDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task DeleteRecoveryResultAsync(EntityDto<int> input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<DrillHoleStatusDto> GetDrillHoleStatusAsync(EntityDto<int> input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<int> MergeImageDrillHoleAsync(MergeDrillHoleDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> CheckOverlapDrillHoleAsync(CheckOverlapDrillHoleDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagedResultDto<TrayDepthResultDto>> GetTrayDepthResultAsync(PagedTrayDepthResultRequestDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<TrayDepthResultDto>> CalculationTrayDepthAsync(CalculationTrayDepthDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<DrillHoleCheckProspectResultDto> DrillHoleCheckProspectAsync(DrillHoleCheckProspectDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="drillHoleId"></param>
    /// <returns></returns>
    Task<List<TotalImageTypeByDrillHoleDto>> GetTotalImageTypeByDrillHoleAsync(int drillHoleId);
}