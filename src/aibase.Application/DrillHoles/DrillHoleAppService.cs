﻿using System.Collections.Generic;
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using aibase.DrillHoleEntity;
using aibase.DrillHoles.Dto;
using System.Threading.Tasks;
using aibase.APIKeys;
using aibase.APIKeys.Services.ApiKeyAuthentication;
using aibase.APIKeys.Services.APIKeyService;
using aibase.DrillHoles.Dto.CalculationDepth;
using aibase.DrillHoles.Dto.CalculationRecovery;
using aibase.DrillHoles.Dto.CalculationTrayDepth;
using aibase.DrillHoles.Services.CalculationDepthService;
using aibase.DrillHoles.Services.CalculationTrayDepthService;
using aibase.DrillHoles.Services.DrillHoleService;
using aibase.DrillHoles.Services.RecoveryService;

namespace aibase.DrillHoles;

/// <summary>
/// 
/// </summary>
[AbpAuthorize]
public class DrillHoleAppService : AsyncCrudAppService<DrillHole, DrillHoleDto, int, PagedDrillHoleResultRequestDto,
    CreateDrillHoleDto, UpdateDrillHoleDto>, IDrillHoleAppService
{
    private readonly IDrillHoleService _drillHoleService;
    private readonly IRecoveryService _recoveryService;
    private readonly ICalculationDepthService _calculationDepthService;
    private readonly IApiKeyService _apiKeyService;
    private readonly ICalculationTrayDepthService _calculationTrayDepthService;

    /// <inheritdoc />
    public DrillHoleAppService(
        IRepository<DrillHole, int> repository,
        IDrillHoleService drillHoleService,
        IApiKeyService apiKeyService,
        IRecoveryService recoveryService, 
        ICalculationDepthService calculationDepthService,
        ICalculationTrayDepthService calculationTrayDepthService) : base(repository)
    {
        _drillHoleService = drillHoleService;
        _apiKeyService = apiKeyService;
        _recoveryService = recoveryService;
        _calculationDepthService = calculationDepthService;
        _calculationTrayDepthService = calculationTrayDepthService;
    }


    /// <inheritdoc />
    [ApiKeyAuthorize]
    public override async Task<DrillHoleDto> CreateAsync(CreateDrillHoleDto input)
    {
        await _apiKeyService.CheckRoleApiKey(APIKeyCode.CreateDrillhole);
        return await _drillHoleService.CreateAsync(input);
    }

    /// <inheritdoc />
    [ApiKeyAuthorize]
    public override async Task<DrillHoleDto> UpdateAsync(UpdateDrillHoleDto input)
    {
        await _apiKeyService.CheckRoleApiKey(APIKeyCode.UpdateDrillhole);
        return await _drillHoleService.UpdateAsync(input);
    }

    /// <inheritdoc />
    [AbpAuthorize]
    public async Task<DrillHoleDto> GetByNameAsync(string name)
    {
        return await _drillHoleService.GetAsync(name);
    }

    /// <inheritdoc />
    [ApiKeyAuthorize]
    public async Task<PagedResultDto<RecoveryResultDto>> GetRecoveryByDrillHoleAsync(
        PagedRecoveryHoleResultRequestDto input)
    {
        return await _recoveryService.GetRecoveryByDrillHoleAsync(input);
    }

    /// <inheritdoc />
    [ApiKeyAuthorize]
    public async Task<List<RecoveryResultDto>> CalculateRecoveryAsync(CalculateRecoveryDto input)
    {
        return await _recoveryService.CalculateRecoveryAsync(input);
    }

    /// <inheritdoc />
    [ApiKeyAuthorize]
    public async Task<double?> CalculateDepthBySelectPointAsync(CalculateDepthBySelectPointDto input)
    {
        return await _calculationDepthService.CalculateDepthAsync(input);
    }

    /// <inheritdoc />
    [ApiKeyAuthorize]
    public async Task<PagedResultDto<DrillHoleDto>> GetDrillHoleByProjectProspectAsync(
        GetDrillHoleByProjectProspectDto input)
    {
        await _apiKeyService.CheckRoleApiKey(APIKeyCode.GetDrillhole);
        return await _drillHoleService.GetDrillHoleByProjectProspectAsync(input);
    }

    /// <inheritdoc />
    public async Task<RecoveryResultDto> UpdateRecoveryResultAsync(UpdateRecoveryResultDto input)
    {
        return await _recoveryService.UpdateRecoveryResultAsync(input);
    }

    /// <inheritdoc />
    public async Task DeleteRecoveryResultAsync(EntityDto<int> input)
    {
        await _recoveryService.DeleteRecoveryResultAsync(input);
    }

    /// <inheritdoc />
    public async Task<DrillHoleStatusDto> GetDrillHoleStatusAsync(EntityDto<int> input)
    {
        return await _drillHoleService.GetDrillHoleStatusAsync(input);
    }

    /// <inheritdoc />
    public async Task<int> MergeImageDrillHoleAsync(MergeDrillHoleDto input)
    {
        return await _drillHoleService.MergeImageDrillHoleAsync(input);
    }

    /// <inheritdoc />
    public async Task<bool> CheckOverlapDrillHoleAsync(CheckOverlapDrillHoleDto input)
    {
        return await _drillHoleService.CheckOverlapDrillHoleAsync(input);
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<TrayDepthResultDto>> GetTrayDepthResultAsync(
        PagedTrayDepthResultRequestDto input)
    {
        return await _calculationTrayDepthService.GetTrayDepthResultAsync(input);
    }

    /// <inheritdoc />
    public async Task<List<TrayDepthResultDto>> CalculationTrayDepthAsync(CalculationTrayDepthDto input)
    {
        return await _calculationTrayDepthService.CalculationTrayDepthAsync(input);
    }

    /// <inheritdoc />
    public async Task<DrillHoleCheckProspectResultDto> DrillHoleCheckProspectAsync(DrillHoleCheckProspectDto input)
    {
        return await _drillHoleService.DrillHoleCheckProspectAsync(input);
    }

    /// <inheritdoc />
    public async Task<List<TotalImageTypeByDrillHoleDto>> GetTotalImageTypeByDrillHoleAsync(int drillHoleId)
    {
        return await _drillHoleService.GetTotalImageTypeByDrillHoleAsync(drillHoleId);
    }

    /// <inheritdoc />
    [ApiKeyAuthorize]
    public override async Task<PagedResultDto<DrillHoleDto>> GetAllAsync(PagedDrillHoleResultRequestDto input)
    {
        await _apiKeyService.CheckRoleApiKey(APIKeyCode.GetDrillhole);
        return await _drillHoleService.GetAllAsync(input);
    }

    /// <summary>
    /// 
    /// </summary>
    [AbpAuthorize]
    public async Task UpdateTotalImageAsync()
    {
        await _drillHoleService.UpdateTotalImageAsync();
    }

    /// <inheritdoc />
    [AbpAuthorize]
    public override async Task<DrillHoleDto> GetAsync(EntityDto<int> input)
    {
        return await _drillHoleService.GetAsync(input);
    }
}