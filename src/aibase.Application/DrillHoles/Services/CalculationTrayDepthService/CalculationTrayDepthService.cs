using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.UI;
using aibase.DrillHoleEntity;
using aibase.DrillHoles.Dto.CalculationDepth;
using aibase.DrillHoles.Dto.CalculationTrayDepth;
using aibase.DrillHoles.Services.CalculationDepthService;
using aibase.ImageEntity;
using aibase.Images.Services.UploadService.Handler;
using aibase.TrayDepthResults;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.DrillHoles.Services.CalculationTrayDepthService;

/// <summary>
///
/// </summary>
public class CalculationTrayDepthService : ICalculationTrayDepthService
{
    private readonly IRepository<TrayDepthResult, int> _trayDepthResultRepository;
    private readonly IRepository<DrillHole, int> _drillholeRepository;
    private readonly IRepository<Image, int> _imageRepository;
    private readonly IRepository<RockLines.RockLine, int> _rockLineRepository;
    private readonly IMapper _mapper;
    private readonly ICalculationDepthService _calculationDepthService;

    /// <summary>
    ///
    /// </summary>
    public CalculationTrayDepthService(
        IRepository<TrayDepthResult, int> trayDepthResultRepository,
        IMapper mapper,
        IRepository<DrillHole, int> drillholeRepository,
        IRepository<Image, int> imageRepository,
        IRepository<RockLines.RockLine, int> rockLineRepository,
        ICalculationDepthService calculationDepthService)
    {
        _trayDepthResultRepository = trayDepthResultRepository;
        _drillholeRepository = drillholeRepository;
        _imageRepository = imageRepository;
        _rockLineRepository = rockLineRepository;
        _mapper = mapper;
        _calculationDepthService = calculationDepthService;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<TrayDepthResultDto>> GetTrayDepthResultAsync(PagedTrayDepthResultRequestDto input)
    {
        var query = _trayDepthResultRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.DrillHoleId == input.DrillHoleId);

        var totalCount = await query.CountAsync();

        var recoveries = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        var recoveriesDto = _mapper.Map<List<TrayDepthResultDto>>(recoveries);

        return new PagedResultDto<TrayDepthResultDto>(totalCount, recoveriesDto);
    }

    /// <inheritdoc />
    public async Task<List<TrayDepthResultDto>> CalculationTrayDepthAsync(CalculationTrayDepthDto input)
    {
        // 1. Validate drill hole exists
        var drillHole = await _drillholeRepository.GetAllIncluding(x => x.Project)
                            .AsNoTracking()
                            .Select(x => new { x.Id, x.Project.CoreTrayLength })
                            .FirstOrDefaultAsync(x => x.Id == input.DrillHoleId)
                        ?? throw new EntityNotFoundException(typeof(DrillHole), input.DrillHoleId);

        var coreTray = drillHole.CoreTrayLength;

        // Fetch all images for the drill hole
        var images = await _imageRepository.GetAllIncluding(x => x.ImageType)
            .AsNoTracking()
            .Where(x => x.DrillHoleId == input.DrillHoleId && x.ImageType != null && x.ImageType.IsStandard)
            .OrderBy(x => x.DepthFrom)
            .Select(x => new ImageViewCalcDepthDto
            {
                Id = x.Id,
                DepthFrom = x.DepthFrom,
                DepthTo = x.DepthTo,
                OcrResult = x.OcrResult,
                SegmentResult = x.SegmentResult,
                CroppedImages = x.CroppedImages
                    .Where(c => c.Type == ImageConstSettings.ImageCropRow ||
                                c.Type == ImageConstSettings.ImageCropRowLower)
                    .OrderBy(c => c.DepthFrom)
                    .Select(c => new CroppedImageViewCalcDepthDto
                    {
                        Id = c.Id,
                        Coordinate = c.Coordinate,
                        Type = c.Type,
                        DepthFrom = c.DepthFrom
                    })
                    .ToList()
            })
            .ToListAsync();

        if (images == null || images.Count == 0)
        {
            throw new UserFriendlyException("No images found for the drill hole.");
        }

        // Prefetch all rock lines of type Recovery for this drill hole's images
        var cropIds = images
            .SelectMany(image => image.CroppedImages)
            .Where(crop => crop.Type is ImageConstSettings.ImageCropRow or ImageConstSettings.ImageCropRowLower)
            .Select(crop => crop.Id)
            .ToList();

        var rockLines = await _rockLineRepository.GetAll()
            .AsNoTracking()
            .Where(rl => cropIds.Contains(rl.ImageCropId) && rl.Type == RockLines.RockLineType.Recovery)
            .ToListAsync();

        // Group rock lines by image crop ID for efficient lookup
        var rockLinesByCropId = rockLines.GroupBy(rl => rl.ImageCropId)
            .ToDictionary(g => g.Key, g => g.ToList());

        // 1. Initialize result list
        var trayDepthResults = new List<TrayDepthResult>();

        // 2. Loop through each image to calculate tray depth
        foreach (var image in images)
        {
            // Get the cropped image rows sorted by depth
            var imageCrops = image.CroppedImages
                .Where(x => x.Type is ImageConstSettings.ImageCropRow or ImageConstSettings.ImageCropRowLower)
                .OrderBy(x => x.DepthFrom)
                .ToList();

            if (imageCrops.Count == 0)
            {
                continue; // Skip if no cropped rows
            }

            // 3. Get the first and last cropped rows
            var firstCrop = imageCrops.FirstOrDefault();
            var lastCrop = imageCrops.LastOrDefault();

            if (firstCrop == null || lastCrop == null)
            {
                continue;
            }

            // Find rock lines for the first and last crops
            if (!rockLinesByCropId.TryGetValue(firstCrop.Id, out var firstRowRockLines) || firstRowRockLines.Count == 0)
            {
                continue; // Skip if no rock lines for first crop
            }

            if (!rockLinesByCropId.TryGetValue(lastCrop.Id, out var lastRowRockLines) || lastRowRockLines.Count == 0)
            {
                continue; // Skip if no rock lines for last crop
            }

            // Get the minimum StartX from all rock lines in the first row
            var startPoint = new CalculateDepthInListImageDto
            {
                Images = images,
                ImageCropId = firstCrop.Id,
                X = firstRowRockLines.Min(rl => rl.StartX),
                CoreTray = coreTray 
            };

            // Get the maximum EndX from all rock lines in the last row
            var endPoint = new CalculateDepthInListImageDto
            {
                Images = images,
                ImageCropId = lastCrop.Id,
                X = lastRowRockLines.Max(rl => rl.EndX),
                CoreTray = coreTray
            };

            // Calculate the depth for start and end points sequentially to avoid DbContext concurrency issues
            var startDepth = _calculationDepthService.CalculateDepthInListImage(startPoint);
            var endDepth = _calculationDepthService.CalculateDepthInListImage(endPoint);

            // Skip if either depth calculation failed
            if (startDepth == null || endDepth == null)
            {
                continue;
            }

            // Create tray depth result
            trayDepthResults.Add(new TrayDepthResult
            {
                DrillHoleId = input.DrillHoleId,
                TrayNumber = trayDepthResults.Count + 1,
                StartDepth = startDepth.Value,
                EndDepth = endDepth.Value
            });
        }

        // 4. Delete existing tray depth results for this drill hole
        await _trayDepthResultRepository.DeleteAsync(c => c.DrillHoleId == input.DrillHoleId);

        // Insert all new tray depth results
        if (trayDepthResults.Count != 0)
        {
            foreach (var trayResult in trayDepthResults)
            {
                await _trayDepthResultRepository.InsertAsync(trayResult);
            }
        }

        return _mapper.Map<List<TrayDepthResultDto>>(trayDepthResults);
    }

}