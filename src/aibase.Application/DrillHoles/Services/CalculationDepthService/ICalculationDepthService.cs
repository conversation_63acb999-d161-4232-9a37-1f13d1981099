using System.Threading.Tasks;
using Abp.Dependency;
using aibase.DrillHoles.Dto.CalculationDepth;

namespace aibase.DrillHoles.Services.CalculationDepthService;

/// <inheritdoc />
public interface ICalculationDepthService : ITransientDependency
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<double?> CalculateDepthAsync(CalculateDepthBySelectPointDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    double? CalculateDepthInListImage(CalculateDepthInListImageDto input);
}