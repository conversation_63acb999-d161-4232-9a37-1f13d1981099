using System.Collections.Generic;
using System.Linq;
using aibase.DrillHoles.Dto.CalculationDepth;
using aibase.Models.Dto;

namespace aibase.DrillHoles.Services.CalculationDepthService.Handler;

/// <summary>
/// 
/// </summary>
public static class CalculationDepthHandler
{
    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    public static int? IsPointInAnyPolygon(List<SegmentResultDto> segmentResult, double selectedX, double selectedY)
    {
        return segmentResult
            .FirstOrDefault(x =>
                x.BoundingSegment.X <= selectedX && x.BoundingSegment.X + x.BoundingSegment.Width >= selectedX
                                                 && x.BoundingSegment.Y <= selectedY &&
                                                 x.BoundingSegment.Y + x.BoundingSegment.Height >= selectedY
            )?.rowIndex;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="segments"></param>
    /// <returns></returns>
    public static List<BoundingSegmentOcrDto> AlignBoundingSegmentOcrs(List<BoundingSegmentOcrDto> segments)
    {
        var baseX = segments[0].X;
        var baseY = segments[0].Y;

        double cumulativeXOffset = 0;

        foreach (var segment in segments)
        {
            segment.X = baseX + cumulativeXOffset;
            segment.Y = baseY;

            foreach (var ocr in segment.OcrResults)
            {
                ocr.x += cumulativeXOffset;
                ocr.y = baseY;
            }

            cumulativeXOffset += segment.Width;
        }

        return segments;
    }
}