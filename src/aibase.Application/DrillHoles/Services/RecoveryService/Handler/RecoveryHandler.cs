using System;
using System.Collections.Generic;
using System.Linq;
using Abp.UI;
using aibase.DrillHoles.Dto.CalculationRecovery;
using aibase.ImageCrops.Dto;
using aibase.Images.Dto;
using Newtonsoft.Json;

namespace aibase.DrillHoles.Services.RecoveryService.Handler;

/// <summary>
/// 
/// </summary>
public static class RecoveryHandler
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="imageCrops"></param>
    /// <param name="segments"></param>
    /// <param name="ocrResults"></param>
    /// <returns></returns>
    public static List<RowSegmentDto> MergeDataRowSegmentOcr(
        List<ImageCropDto> imageCrops,
        List<SegmentResultRecoveryDto> segments,
        List<OcrResultRecoveryDto> ocrResults)
    {
        var rowDict = new Dictionary<int, RowSegmentDto>();

        var imageCropCoordinates = imageCrops.Select(x => x.Coordinate).ToList();
        var boundingRows = imageCropCoordinates.OfType<string>()
            .Select(JsonConvert.DeserializeObject<CoordinateBoundingDto>)
            .OfType<CoordinateBoundingDto>().ToList();
        boundingRows = boundingRows.OrderBy(r => r.Y).ToList();

        if (boundingRows.Count == 0)
        {
            throw new UserFriendlyException("No bounding rows found");
        }

        var sortedRows = boundingRows.OrderBy(r => r.Y).ToList();
        for (var i = 0; i < sortedRows.Count; i++)
        {
            rowDict[i] = new RowSegmentDto
            {
                X = sortedRows[i].X,
                Y = sortedRows[i].Y,
                Width = sortedRows[i].Width,
                Height = sortedRows[i].Height,
                Id = sortedRows[i].Id,
                Points = [],
                OcrResults = []
            };
        }

        foreach (var segment in segments.Where(segment => rowDict.ContainsKey(segment.RowIndex)))
        {
            rowDict[segment.RowIndex].Points.Add(segment.Points);
            rowDict[segment.RowIndex].BoundingSegment.Add(segment.BoundingSegment);
            rowDict[segment.RowIndex].OcrResults = ocrResults
                .Where(ocr => ocr.RowIndex == segment.RowIndex)
                .OrderBy(ocr => ocr.X)
                .Select(ocr => new OcrResultRecoveryDto
                {
                    Type = ocr.Type,
                    X = ocr.X,
                    OriginalX = ocr.X,
                    Y = ocr.Y,
                    Width = ocr.Width,
                    Height = ocr.Height,
                    Text = ocr.Text,
                    Probability = ocr.Probability,
                    RowIndex = ocr.RowIndex,
                    ImageCropId = imageCrops[segment.RowIndex].Id,
                    Id = ocr.Id
                })
                .ToList();
        }

        var rowSegmentsDto = rowDict.Values.OrderBy(r => r.Y).ToList();
        foreach (var item in rowSegmentsDto)
        {
            item.MiddleLine = new LineCoordinateDto
            {
                StartX = item.X,
                EndX = item.X + item.Width,
                Y = item.Y + (item.Height / 2)
            };
        }

        return rowSegmentsDto;
    }
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="rowsByImage"></param>
    public static List<RowSegmentDto> AlignSegmentsHorizontally(List<List<RowSegmentDto>> rowsByImage)
    {
        var segments = rowsByImage.SelectMany(x => x).ToList();

        var baseX = segments[0].X;
        var baseY = segments[0].Y;
        var baseMiddleLine = segments[0].MiddleLine;

        double cumulativeXOffset = 0;

        foreach (var segment in segments)
        {
            segment.X = baseX + cumulativeXOffset;

            var deltaY = baseY - segment.Y;
            segment.Y = baseY;

            segment.MiddleLine.Y = baseMiddleLine.Y;
            segment.MiddleLine.StartY = baseMiddleLine.StartY;
            segment.MiddleLine.EndY = baseMiddleLine.EndY;
            segment.MiddleLine.StartX += cumulativeXOffset;
            segment.MiddleLine.EndX += cumulativeXOffset;

            foreach (var ocr in segment.OcrResults)
            {
                ocr.X += cumulativeXOffset;
                ocr.Y = baseY;
            }

            foreach (var shape in segment.Points)
            {
                foreach (var point in shape)
                {
                    if (point.Count >= 2)
                    {
                        point[0] += cumulativeXOffset;
                        point[1] += deltaY;
                    }
                }
            }

            foreach (var line in segment.IntersectionPoints)
            {
                line.StartX += cumulativeXOffset;
                line.EndX += cumulativeXOffset;
                line.StartY = baseMiddleLine.StartY;
                line.EndY = baseMiddleLine.EndY;
                line.Y = baseMiddleLine.Y;
            }

            cumulativeXOffset += segment.Width;
        }

        return segments;
    }
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="imageRows"></param>
    /// <returns></returns>
    public static List<RowSegmentDto> AlignSegmentsVertical(List<List<RowSegmentDto>> imageRows)
    {
        if (imageRows.Count == 0 || imageRows[0].Count == 0)
            return [];

        var recalculatedRows = new List<RowSegmentDto>();
        
        recalculatedRows.AddRange(imageRows[0].Select(row => new RowSegmentDto
        {
            X = row.X,
            Y = row.Y,
            Width = row.Width,
            Height = row.Height,
            DepthFrom = row.DepthFrom,
            DepthTo = row.DepthTo,
            Id = row.Id,
            OcrResults = row.OcrResults,
            MiddleLine = row.MiddleLine,
            Points = row.Points,
            IntersectionPoints = row.IntersectionPoints,
        }));
        
        for (var imageIndex = 1; imageIndex < imageRows.Count; imageIndex++)
        {
            var previousImageLastRow = recalculatedRows[^1];
            var currentImageRows = imageRows[imageIndex];
            
            if (currentImageRows.Count == 0) continue;

            var yOffset = (previousImageLastRow.Y + previousImageLastRow.Height) - currentImageRows[0].Y;
            
            recalculatedRows.AddRange(currentImageRows.Select(row => new RowSegmentDto
            {
                X = row.X,
                Y = row.Y + yOffset,
                Width = row.Width,
                Height = row.Height,
                DepthFrom = row.DepthFrom,
                DepthTo = row.DepthTo,
                Id = row.Id,
                OcrResults = AdjustOcrResultsCoordinates(row.OcrResults, yOffset),
                MiddleLine = AdjustMiddleLineCoordinates(row.MiddleLine, yOffset),
                Points = AdjustPointsCoordinates(row.Points, yOffset),
                IntersectionPoints = AdjustMiddleLineCoordinates(row.IntersectionPoints, yOffset),
            }));
        }

        return recalculatedRows;
    }
    
    private static List<OcrResultRecoveryDto> AdjustOcrResultsCoordinates(List<OcrResultRecoveryDto> ocrResults, double yOffset)
    {
        if (ocrResults.Count == 0) return [];

        return ocrResults.Select(ocr => new OcrResultRecoveryDto
        {
            Type = ocr.Type,
            X = ocr.X,
            Y = ocr.Y + yOffset,
            Width = ocr.Width,
            Height = ocr.Height,
            Text = ocr.Text,
            Probability = ocr.Probability,
            RowIndex = ocr.RowIndex,
            Id = ocr.Id
        }).ToList();
    }

    private static LineCoordinateDto AdjustMiddleLineCoordinates(LineCoordinateDto middleLine, double yOffset)
    {

        return new LineCoordinateDto
        {
            // Assuming LineCoordinateDto has start and end points
            StartX = middleLine.StartX,
            StartY = middleLine.StartY + yOffset,
            EndX = middleLine.EndX,
            EndY = middleLine.EndY + yOffset,
            Y = middleLine.Y + yOffset
        };
    }
    
    private static List<LineCoordinateDto> AdjustMiddleLineCoordinates(List<LineCoordinateDto> middleLines, double yOffset)
    {
        return middleLines.Select(line => new LineCoordinateDto
        {
            StartX = line.StartX,
            StartY = line.StartY + yOffset,
            EndX = line.EndX,
            EndY = line.EndY + yOffset,
            Y = line.Y + yOffset
        }).ToList();
    }

    private static List<List<List<double>>> AdjustPointsCoordinates(List<List<List<double>>> points, double yOffset)
    {
        if (points.Count == 0) return [];

        return points.Select(group => 
            group.Select(point =>
                point.Select((coord, index) => 
                        index == 1 ? coord + yOffset : coord) // Adjust Y coordinate (index 1)
                    .ToList()
            ).ToList()
        ).ToList();
    }
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    public static List<OcrBlockRecoveryDto> FilterValidData(List<OcrBlockRecoveryDto> data)
    {
        var filteredList = new List<OcrBlockRecoveryDto>();
        var lastValue = double.MinValue;

        foreach (var item in data.Where(item => item.Value >= lastValue))
        {
            filteredList.Add(item);
            lastValue = item.Value;
        }

        return filteredList;
    }
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="rowSegments"></param>
    /// <param name="ocrBlocks"></param>
    /// <param name="coreTray"></param>
    /// <returns></returns>
    public static List<List<LengthRowDto>> SegmentLengthRows(
        List<RowSegmentDto> rowSegments,
        List<OcrBlockRecoveryDto> ocrBlocks,
        double coreTray)
    {
        var lengthRows = rowSegments
            .SelectMany(row => row.IntersectionPoints
                .Select(intersection => new LengthRowDto
                {
                    X = intersection.StartX,
                    Y = row.Y,
                    Value = row.Width != 0
                        ? Math.Round(((intersection.EndX - intersection.StartX) / row.Width) * coreTray, 2)
                        : 0,
                }))
            .ToList();

        var segmentedLists = new List<List<LengthRowDto>>();

        for (var i = 0; i < ocrBlocks.Count - 1; i++)
        {
            var startX = ocrBlocks[i].X;
            var endX = ocrBlocks[i + 1].X;

            var filteredItems = lengthRows
                .Where(item => item.X >= startX && item.X <= endX)
                .ToList();

            segmentedLists.Add(filteredItems);
        }

        return segmentedLists;
    }
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="ocrBlocks"></param>
    /// <returns></returns>
    public static List<double> CalculateDepths(List<OcrBlockRecoveryDto> ocrBlocks)
    {
        var valueDifferences = new List<double>();

        for (var i = 1; i < ocrBlocks.Count; i++)
        {
            var difference = ocrBlocks[i].Value - ocrBlocks[i - 1].Value;
            valueDifferences.Add(difference);
        }

        return valueDifferences;
    }
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="listOfLists"></param>
    /// <returns></returns>
    public static List<double> CalculateLengths(List<List<LengthRowDto>> listOfLists)
    {
        return listOfLists
            .Select(innerList => innerList.Sum(item => item.Value))
            .ToList();
    }
}