using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.GeologyFields;
using aibase.RockGroupProjects;
using aibase.RockGroups.Dto;
using aibase.RockStyles.Dto;
using aibase.RockTypes;
using aibase.RockTypes.Dto;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.RockGroups.Services.RockGroupService;

/// <inheritdoc />
public class RockGroupService : IRockGroupService
{
    private readonly IRepository<RockGroup, int> _repository;
    private readonly IRepository<RockType, int> _rockTypeRepository;
    private readonly IRepository<RockGroupProject, int> _rockGroupProjectRepository;
    private readonly IRepository<GeologyField, int> _geologyFieldRepository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="repository"></param>
    /// <param name="rockTypeRepository"></param>
    /// <param name="rockGroupProjectRepository"></param>
    /// <param name="geologyFieldRepository"></param>
    /// <param name="abpSession"></param>
    /// <param name="mapper"></param>
    /// <param name="unitOfWorkManager"></param>
    public RockGroupService(
        IRepository<RockGroup, int> repository, 
        IRepository<RockType, int> rockTypeRepository,
        IRepository<RockGroupProject, int> rockGroupProjectRepository, 
        IRepository<GeologyField, int> geologyFieldRepository,
        IAbpSession abpSession, 
        IMapper mapper, IUnitOfWorkManager unitOfWorkManager)
    {
        _repository = repository;
        _rockTypeRepository = rockTypeRepository;
        _rockGroupProjectRepository = rockGroupProjectRepository;
        _geologyFieldRepository = geologyFieldRepository;
        _abpSession = abpSession;
        _mapper = mapper;
        _unitOfWorkManager = unitOfWorkManager;
    }

    /// <inheritdoc />
    public async Task<RockGroup> CreateAsync(CreateRockGroupDto input, bool returnExist = false)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }
        var tenantId = _abpSession.GetTenantId();
        
        var existingRockGroup =
            await _repository.FirstOrDefaultAsync(d => d.TenantId == tenantId && d.Name == input.Name);
        if (existingRockGroup != null)
        {
            if (returnExist)
            {
                return existingRockGroup;
            }
            
            throw new UserFriendlyException($"The Rock Group with the name {existingRockGroup.Name} already exists.");
        }
        
        var rockGroup = new RockGroup()
        {
            Name = input.Name,
            IsActive = input.IsActive,
            TenantId = tenantId,
        };

        await _repository.InsertAsync(rockGroup);
        await _unitOfWorkManager.Current.SaveChangesAsync();
        return rockGroup;
    }

    /// <inheritdoc />
    public async Task<RockGroupDto> UpdateAsync(UpdateRockGroupDto input)
    {
        var rockGroup = await ValidateRockGroupEntity(input.Id);
        
        if (input.IsActive == false)
        {
            var geologyDateUsed =
                await _geologyFieldRepository.FirstOrDefaultAsync(x =>
                    x.Type == FieldType.RockGroup && x.RockGroupId == input.Id);
            if (geologyDateUsed != null)
            {
                throw new UserFriendlyException("Cannot deactivate this record because it is already in use.");
            }
        }
        
        rockGroup.Name = input.Name ?? rockGroup.Name;
        rockGroup.IsActive = input.IsActive ?? rockGroup.IsActive;

        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<RockGroupDto>> GetAllAsync(PagedRockGroupResultRequestDto input)
    {
        var query = _repository.GetAll()
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
            .OrderBy(r => r.Name);

        var totalCount = await query.CountAsync();

        var rockGroups = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        return new PagedResultDto<RockGroupDto>(totalCount, _mapper.Map<List<RockGroupDto>>(rockGroups));
    }

    /// <inheritdoc />
    public async Task<RockGroupDto> GetAsync(EntityDto<int> input)
    {
        var rockGroup = await ValidateRockGroupEntity(input.Id);
        var relateRockType = await _rockTypeRepository.GetAllIncluding(x => x.RockStyle)
            .AsNoTracking()
            .Where(x => x.RockGroupRockTypes.Any(y => y.RockGroupId == rockGroup.Id))
            .Select(b => new RockTypeDto()
            {
                Id = b.Id,
                Name = b.Name,
                Code = b.Code,
                Description = b.Description,
                IsActive = b.IsActive,
                RockStyleId = b.RockStyleId,
                RockStyle = _mapper.Map<RockStyleDto>(b.RockStyle)
            }).ToListAsync();

        var rockGroupDto = new RockGroupDto
        {
            Id = rockGroup.Id,
            Name = rockGroup.Name,
            IsActive = rockGroup.IsActive,
            RockTypes = relateRockType
        };

        return rockGroupDto;
    }

    /// <inheritdoc />
    public async Task DeleteAsync(EntityDto<int> input)
    {
        await ValidateRockGroupEntity(input.Id);
        var isUsedInProject =
            await _rockGroupProjectRepository.FirstOrDefaultAsync(x => x.RockGroupId == input.Id);

        if (isUsedInProject != null)
        {
            throw new UserFriendlyException(
                "Cannot delete Rock Group because it is associated with one or more Project types.");
        }
        
        var geologyFieldUsed = await _geologyFieldRepository.FirstOrDefaultAsync(x =>
            x.Type == FieldType.RockGroup && x.RockGroupId == input.Id);
        if (geologyFieldUsed != null)
        {
            throw new UserFriendlyException("Cannot delete this RockGroup because it is being used in GeologyField.");
        }

        await _repository.DeleteAsync(input.Id);
    }

    private async Task<RockGroup> ValidateRockGroupEntity(int id)
    {
        var rockGroup = await _repository.FirstOrDefaultAsync(x => x.Id == id);

        if (rockGroup == null)
        {
            throw new EntityNotFoundException(typeof(RockGroup), id);
        }

        return rockGroup;
    }
}