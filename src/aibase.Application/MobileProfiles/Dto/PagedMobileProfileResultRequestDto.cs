using Abp.Application.Services.Dto;

namespace aibase.MobileProfiles.Dto;

/// <summary>
/// Paged Mobile Profile Result Request DTO
/// </summary>
public class PagedMobileProfileResultRequestDto : PagedResultRequestDto
{
    /// <summary>
    /// Search keyword for name or description
    /// </summary>
    public string? Keyword { get; set; }

    /// <summary>
    /// Filter by active status
    /// </summary>
    public bool? IsActive { get; set; }
}