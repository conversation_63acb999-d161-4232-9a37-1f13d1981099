using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace aibase.MobileProfiles.Dto;

/// <summary>
/// DTO for assigning projects to a mobile profile
/// </summary>
public class AssignProjectsToMobileProfileDto
{
    /// <summary>
    /// Mobile profile ID
    /// </summary>
    [Required]
    public int MobileProfileId { get; set; }
        
    /// <summary>
    /// List of project IDs to assign
    /// </summary>
    [Required]
    public List<int> ProjectIds { get; set; }
}