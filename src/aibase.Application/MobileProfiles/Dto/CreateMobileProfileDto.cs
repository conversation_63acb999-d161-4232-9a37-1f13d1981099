using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace aibase.MobileProfiles.Dto;

/// <summary>
/// 
/// </summary>
public class CreateMobileProfileDto
{
    /// <summary>
    /// Name
    /// </summary>
    [Required]
    [StringLength(256)]
    public required string Name { get; set; }

    /// <summary>
    /// Description
    /// </summary>
    [StringLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Is Active
    /// </summary>
    [Required]
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Is Standard
    /// </summary>
    [Required]
    public bool IsStandard { get; set; }

    /// <summary>
    /// Is Wet
    /// </summary>
    [Required]
    public bool IsWet { get; set; }

    /// <summary>
    /// Is Dry
    /// </summary>
    [Required]
    public bool IsDry { get; set; }

    /// <summary>
    /// Is UV
    /// </summary>
    [Required]
    public bool IsUv { get; set; }

    /// <summary>
    /// Is Rig
    /// </summary>
    [Required]
    public bool IsRig { get; set; }

    /// <summary>
    /// Mobile Camera Type
    /// </summary>
    [Required]
    public CameraType MobileCameraType { get; set; }

    /// <summary>
    /// External Camera Type
    /// </summary>
    [Required]
    public CameraType ExternalCameraType { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public bool? IsDepthIncrement { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public double? DepthIncrement { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public bool? IsApplyDepthIncrement { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public int? RotateImg { get; set; }
        
    /// <summary>
    /// List of project IDs to link to this mobile profile
    /// </summary>
    public List<int>? ProjectIds { get; set; }
}