using System.Collections.Generic;
using Abp.Application.Services.Dto;
using Abp.AutoMapper;
using aibase.ImageTypes.Dto;
using aibase.Projects.Dto;

namespace aibase.MobileProfiles.Dto;

/// <summary>
/// Mobile Profile DTO
/// </summary>
[AutoMap(typeof(MobileProfile))]
public class MobileProfileDto : EntityDto
{
    /// <summary>
    /// Name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Is Active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Is Standard
    /// </summary>
    public bool IsStandard { get; set; }

    /// <summary>
    /// Is Wet
    /// </summary>
    public bool IsWet { get; set; }

    /// <summary>
    /// Is Dry
    /// </summary>
    public bool IsDry { get; set; }

    /// <summary>
    /// Is UV
    /// </summary>
    public bool IsUv { get; set; }

    /// <summary>
    /// Is Rig
    /// </summary>
    public bool IsRig { get; set; }

    /// <summary>
    /// Mobile Camera Type
    /// </summary>
    public CameraType MobileCameraType { get; set; }

    /// <summary>
    /// External Camera Type
    /// </summary>
    public CameraType ExternalCameraType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool? IsDepthIncrement { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double? DepthIncrement { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool? IsApplyDepthIncrement { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? RotateImg { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<ImageTypeDto> ImageTypes { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    public List<ProjectDto> Projects { get; set; } = [];
}