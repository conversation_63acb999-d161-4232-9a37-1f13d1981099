using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Abp.Application.Services.Dto;
using Abp.AutoMapper;

namespace aibase.MobileProfiles.Dto;

/// <summary>
/// Update Mobile Profile DTO
/// </summary>
[AutoMap(typeof(MobileProfile))]
public class UpdateMobileProfileDto : EntityDto<int>
{
    /// <summary>
    /// Name
    /// </summary>
    [StringLength(256)]
    public string? Name { get; set; }

    /// <summary>
    /// Description
    /// </summary>
    [StringLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Is Active
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// Is Standard
    /// </summary>
    public bool? IsStandard { get; set; }

    /// <summary>
    /// Is Wet
    /// </summary>
    public bool? IsWet { get; set; }

    /// <summary>
    /// Is Dry
    /// </summary>
    public bool? IsDry { get; set; }

    /// <summary>
    /// Is UV
    /// </summary>
    public bool? IsUv { get; set; }

    /// <summary>
    /// Is Rig
    /// </summary>
    public bool? IsRig { get; set; }

    /// <summary>
    /// Mobile Camera Type
    /// </summary>
    public CameraType? MobileCameraType { get; set; }

    /// <summary>
    /// External Camera Type
    /// </summary>
    public CameraType? ExternalCameraType { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public bool? IsDepthIncrement { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public double? DepthIncrement { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public bool? IsApplyDepthIncrement { get; set; }
        
    /// <summary>
    /// Rotate Image Mobile
    /// </summary>
    public int? RotateImgMobile { get; set; }

    /// <summary>
    /// Rotate Image External
    /// </summary>
    public int? RotateImgExternal { get; set; }
        
    /// <summary>
    /// List of project IDs to link to this mobile profile
    /// </summary>
    public List<int>? ProjectIds { get; set; }
}
