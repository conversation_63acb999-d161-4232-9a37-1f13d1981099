using System.Threading.Tasks;
using Abp.Application.Services;
using aibase.MobileProfiles.Dto;

namespace aibase.MobileProfiles
{
    /// <summary>
    /// Mobile Profile application service interface
    /// </summary>
    public interface IMobileProfileAppService : IAsyncCrudAppService<MobileProfileDto, int, PagedMobileProfileResultRequestDto, CreateMobileProfileDto, UpdateMobileProfileDto>
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        Task<MobileProfileDto> GetMobileProfileByProjectAsync(int projectId);
    }
}