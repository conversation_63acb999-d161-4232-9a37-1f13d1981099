using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.GeotechSuites.Dto;
using aibase.GeotechSuiteStructures;
using aibase.ProjectEntity;
using aibase.ProjectGeotechSuites;
using aibase.Projects.Dto;
using aibase.Structures;
using aibase.Structures.Dto;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using AssignProjectGeotechSuiteDto = aibase.GeotechSuites.Dto.AssignProjectGeotechSuiteDto;

namespace aibase.GeotechSuites.Services;

/// <inheritdoc />
public class GeotechSuiteService : IGeotechSuiteService
{
    private readonly IRepository<GeotechSuite, int> _repository;
    private readonly IRepository<GeotechSuiteStructure, int> _geotechSuiteStructureRepository;
    private readonly IRepository<Structure, int> _structureRepository;
    private readonly IRepository<ProjectGeotechSuite, int> _projectGeotechSuiteRepository;
    private readonly IRepository<Project, int> _projectRepository;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;
    private readonly IUnitOfWorkManager _unitOfWorkManager;

    /// <summary>
    /// Constructor for GeotechSuiteService
    /// </summary>
    public GeotechSuiteService(
        IRepository<GeotechSuite, int> repository,
        IRepository<GeotechSuiteStructure, int> geotechSuiteStructureRepository,
        IAbpSession abpSession,
        IMapper mapper,
        IUnitOfWorkManager unitOfWorkManager, IRepository<Structure, int> structureRepository,
        IRepository<ProjectGeotechSuite, int> projectGeotechSuiteRepository,
        IRepository<Project, int> projectRepository)
    {
        _repository = repository;
        _geotechSuiteStructureRepository = geotechSuiteStructureRepository;
        _abpSession = abpSession;
        _mapper = mapper;
        _unitOfWorkManager = unitOfWorkManager;
        _structureRepository = structureRepository;
        _projectGeotechSuiteRepository = projectGeotechSuiteRepository;
        _projectRepository = projectRepository;
    }

    /// <inheritdoc />
    public async Task<GeotechSuite> CreateAsync(CreateGeotechSuiteDto input, bool returnExist = false)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var tenantId = _abpSession.GetTenantId();

        var existingGeotechSuite = await _repository
            .FirstOrDefaultAsync(x => x.TenantId == tenantId && x.Name == input.Name);
        if (existingGeotechSuite != null)
        {
            if (returnExist)
            {
                return existingGeotechSuite;
            }

            throw new UserFriendlyException(
                $"A GeotechSuite with the name {existingGeotechSuite.Name} already exists.");
        }

        var geotechSuite = new GeotechSuite()
        {
            Name = input.Name,
            IsActive = input.IsActive,
            TenantId = tenantId
        };

        await _repository.InsertAsync(geotechSuite);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        if (input.ProjectIds.Count > 0)
        {
            var geologyProjectSuite = new AssignProjectGeotechSuiteDto()
            {
                GeotechSuiteId = geotechSuite.Id,
                ProjectIds = input.ProjectIds,
            };
            await AssignProjectGeotechSuiteAsync(geologyProjectSuite);
        }

        if (input.StructureIds is not { Count: > 0 }) return geotechSuite;

        var assignProjectLoggingViewDto = new AssignGeotechSuiteStructureDto
        {
            GeotechSuiteId = geotechSuite.Id,
            StructureIds = input.StructureIds
        };
        await AssignGeotechSuiteStructureAsync(assignProjectLoggingViewDto);

        return geotechSuite;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<GeotechSuiteDto>> GetAllAsync(PagedGeotechSuiteResultRequestDto input)
    {
        var query = _repository.GetAll()
            .AsNoTracking()
            .Include(x => x.GeotechSuiteStructures)
            .ThenInclude(x => x.Structure)
            .Include(x => x.ProjectGeotechSuites)
            .ThenInclude(x => x.Project)
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
            .WhereIf(input.ProjectId.HasValue, x => x.ProjectGeotechSuites.Any(p => p.ProjectId == input.ProjectId))
            .OrderBy(r => r.Name);

        var totalCount = await query.CountAsync();

        var geotechSuites = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .Select(x => new GeotechSuiteDto
            {
                Id = x.Id,
                Name = x.Name,
                IsActive = x.IsActive,
                Structures = _mapper.Map<List<StructureDto>>(x.GeotechSuiteStructures.Select(y => y.Structure)),
                Projects = x.ProjectGeotechSuites.Select(p => new ProjectDto
                {
                    Id = p.Project.Id,
                    Name = p.Project.Name,
                    TextColor = p.Project.TextColor,
                    BackgroundColor = p.Project.BackgroundColor,
                    IsActive = p.Project.IsActive
                }).ToList()
            })
            .ToListAsync();

        return new PagedResultDto<GeotechSuiteDto>(totalCount, geotechSuites);
    }

    /// <inheritdoc />
    public async Task<GeotechSuiteDto> UpdateAsync(UpdateGeotechSuiteDto input)
    {
        var geotechSuite = await ValidateGeotechSuiteEntity(input.Id);

        geotechSuite.Name = input.Name ?? geotechSuite.Name;
        geotechSuite.IsActive = input.IsActive ?? geotechSuite.IsActive;

        await _repository.UpdateAsync(geotechSuite);

        if (input.StructureIds != null)
        {
            var assignProjectLoggingViewDto = new AssignGeotechSuiteStructureDto
            {
                GeotechSuiteId = geotechSuite.Id,
                StructureIds = input.StructureIds
            };
            await AssignGeotechSuiteStructureAsync(assignProjectLoggingViewDto);
        }

        if (input.ProjectIds.Count > 0)
        {
            var geologyProjectSuite = new AssignProjectGeotechSuiteDto()
            {
                GeotechSuiteId = geotechSuite.Id,
                ProjectIds = input.ProjectIds,
            };
            await AssignProjectGeotechSuiteAsync(geologyProjectSuite);
        }

        return _mapper.Map<GeotechSuiteDto>(geotechSuite);
    }

    /// <inheritdoc />
    public async Task<GeotechSuiteDto> GetAsync(EntityDto<int> input)
    {
        var geotechSuite = await ValidateGeotechSuiteEntity(input.Id);
        var relate = await _structureRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.GeotechSuiteStructures.Any(y => y.GeotechSuiteId == geotechSuite.Id))
            .ToListAsync();

        var relateProject = await _projectRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.ProjectGeotechSuites.Any(y => y.GeotechSuiteId == geotechSuite.Id))
            .ToListAsync();

        var geotechSuiteDto = _mapper.Map<GeotechSuiteDto>(geotechSuite);
        geotechSuiteDto.Structures = _mapper.Map<List<StructureDto>>(relate);
        geotechSuiteDto.Projects = _mapper.Map<List<ProjectDto>>(relateProject);

        return geotechSuiteDto;
    }

    /// <inheritdoc />
    public async Task DeleteAsync(EntityDto<int> input)
    {
        var hasGeotechSuiteStructures = await _geotechSuiteStructureRepository
            .CountAsync(x => x.GeotechSuiteId == input.Id) > 0;

        if (hasGeotechSuiteStructures)
        {
            throw new UserFriendlyException(
                "Cannot delete GeotechSuite because it has associated GeotechSuiteStructures.");
        }

        await _repository.DeleteAsync(input.Id);
    }

    private async Task<GeotechSuite> ValidateGeotechSuiteEntity(int id)
    {
        var geotechSuite = await _repository
            .GetAllIncluding(x => x.GeotechSuiteStructures)
            .FirstOrDefaultAsync(x => x.Id == id);

        if (geotechSuite == null)
        {
            throw new EntityNotFoundException(typeof(GeotechSuite), id);
        }

        return geotechSuite;
    }

    /// <inheritdoc />
    public async Task AssignGeotechSuiteStructureAsync(AssignGeotechSuiteStructureDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var existingStructures =
            await _geotechSuiteStructureRepository.GetAllListAsync(x => x.GeotechSuiteId == input.GeotechSuiteId);

        var existingStructureIds = existingStructures.Select(x => x.StructureId).ToList();

        var structuresToAdd = input.StructureIds.Except(existingStructureIds).ToList();

        var structuresToDelete = existingStructureIds.Except(input.StructureIds).ToList();

        foreach (var geotechSuiteStructure in structuresToAdd.Select(structureId => new GeotechSuiteStructure
                 {
                     GeotechSuiteId = input.GeotechSuiteId,
                     StructureId = structureId
                 }))
        {
            await _geotechSuiteStructureRepository.InsertAsync(geotechSuiteStructure);
        }

        foreach (var structureId in structuresToDelete)
        {
            var geotechSuiteStructure = existingStructures.FirstOrDefault(x => x.StructureId == structureId);
            if (geotechSuiteStructure != null)
            {
                await _geotechSuiteStructureRepository.DeleteAsync(geotechSuiteStructure);
            }
        }
    }

    private async Task AssignProjectGeotechSuiteAsync(AssignProjectGeotechSuiteDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var existingProjects = await _projectGeotechSuiteRepository.GetAllListAsync(x =>
            x.GeotechSuiteId == input.GeotechSuiteId);

        var existingIds = existingProjects.Select(x => x.ProjectId).ToList();

        var projectsToAdd = input.ProjectIds.Except(existingIds).ToList();
        var projectsToDelete = existingIds.Except(input.ProjectIds).ToList();

        foreach (var projectGeotechSuite in projectsToAdd.Select(projectId =>
                     new ProjectGeotechSuite
                     {
                         ProjectId = projectId,
                         GeotechSuiteId = input.GeotechSuiteId,
                     }))
        {
            await _projectGeotechSuiteRepository.InsertAsync(projectGeotechSuite);
        }

        foreach (var projectId in projectsToDelete)
        {
            await _projectGeotechSuiteRepository.DeleteAsync(x => x.ProjectId == projectId && x.GeotechSuiteId == input.GeotechSuiteId);
        }
    }
}