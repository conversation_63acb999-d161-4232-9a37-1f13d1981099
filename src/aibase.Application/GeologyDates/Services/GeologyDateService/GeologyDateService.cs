using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.GeologyDates.Dto;
using aibase.GeologyFields;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.GeologyDates.Services.GeologyDateService;

/// <inheritdoc />
public class GeologyDateService : IGeologyDateService
{
    private readonly IRepository<GeologyDate, int> _repository;
    private readonly IRepository<GeologyField, int> _geologyFieldRepository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="repository"></param>
    /// <param name="geologyFieldRepository"></param>
    /// <param name="abpSession"></param>
    /// <param name="mapper"></param>
    /// <param name="unitOfWorkManager"></param>
    public GeologyDateService(
        IRepository<GeologyDate, int> repository, 
        IRepository<GeologyField, int> geologyFieldRepository,
        IAbpSession abpSession, 
        IMapper mapper, IUnitOfWorkManager unitOfWorkManager)
    {
        _repository = repository;
        _geologyFieldRepository = geologyFieldRepository;
        _abpSession = abpSession;
        _mapper = mapper;
        _unitOfWorkManager = unitOfWorkManager;
    }

    /// <inheritdoc />
    public async Task<GeologyDate> CreateAsync(CreateGeologyDateDto input, bool returnExist = false)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }
        var tenantId = _abpSession.GetTenantId();

        var existingGeologyDate =
            await _repository.FirstOrDefaultAsync(d => d.TenantId == tenantId && d.Name == input.Name);
        if (existingGeologyDate != null)
        {
            if (returnExist)
            {
                return existingGeologyDate;
            }
            
            throw new UserFriendlyException($"The Geology Date with the name {existingGeologyDate.Name} already exists.");
        }
        
        var geologyDate = new GeologyDate()
        {
            Name = input.Name,
            IsActive = input.IsActive,
            TenantId = tenantId,
        };

        await _repository.InsertAsync(geologyDate);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        return geologyDate;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<GeologyDateDto>> GetAllAsync(PagedGeologyDateResultRequestDto input)
    {
        var query = _repository.GetAll()
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .OrderBy(r => r.Name);

        var totalCount = await query.CountAsync();

        var dates = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        var dateDto = _mapper.Map<List<GeologyDateDto>>(dates);

        return new PagedResultDto<GeologyDateDto>(totalCount, dateDto);
    }

    /// <inheritdoc />
    public async Task<GeologyDateDto> GetAsync(EntityDto<int> input)
    {
        var geologyDate = await ValidateGeologyDateEntity(input.Id);
        return _mapper.Map<GeologyDateDto>(geologyDate);
    }

    /// <inheritdoc />
    public async Task<GeologyDateDto> UpdateAsync(UpdateGeologyDateDto input)
    {
        var geologyDate = await ValidateGeologyDateEntity(input.Id);
        
        if (input.IsActive == false)
        {
            var geologyDateUsed = await _geologyFieldRepository.FirstOrDefaultAsync(x => x.Type == FieldType.Date && x.GeologyDateId == input.Id);
            if (geologyDateUsed != null)
            {
                throw new UserFriendlyException("Cannot deactivate this record because it is already in use.");
            }
        }

        geologyDate.Name = input.Name ?? geologyDate.Name;
        geologyDate.IsActive = input.IsActive ?? geologyDate.IsActive;

        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task DeleteAsync(EntityDto<int> input)
    {
        var geologyDate = await ValidateGeologyDateEntity(input.Id);

        var geologyFieldUsed = await _geologyFieldRepository.FirstOrDefaultAsync(x =>
            x.Type == FieldType.PickList && x.PickListId == input.Id);
        if (geologyFieldUsed != null)
        {
            throw new UserFriendlyException("Cannot delete this GeologyDate because it is being used in GeologyField.");
        }
        
        await _repository.DeleteAsync(geologyDate);
    }

    private async Task<GeologyDate> ValidateGeologyDateEntity(int id)
    {
        var colour = await _repository.FirstOrDefaultAsync(x => x.Id == id);

        if (colour == null)
        {
            throw new EntityNotFoundException(typeof(GeologyDate), id);
        }

        return colour;
    }
}