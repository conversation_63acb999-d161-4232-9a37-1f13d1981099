using Abp.Application.Services.Dto;
using aibase.AssayAttributes.Dto;
using aibase.AssaySuites.Dto;
using aibase.Attributes.Dto;
using aibase.GeologySuiteFields.Dto;
using aibase.GeologySuites.Dto;
using aibase.ImageEntity;
using aibase.ImageSubtypes.Dto;
using aibase.ImageTypes.Dto;
using aibase.NumberRanges.Dto;
using aibase.Suites.Dto;
using AutoMapper;

namespace aibase.LoggingViewColumns.Dto;

/// <inheritdoc />
[AutoMap(typeof(LoggingViewColumn))]
public class LoggingViewColumnDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// 
    /// </summary>
    public ColumnClass ColumnClass { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int Width { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public GeologySuiteDto? GeologySuite { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public GeologySuiteFieldDto? GeologySuiteField { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public AssaySuiteDto? AssaySuite { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public AssayAttributeDto? AssayAttribute { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public SuiteDto? Suite { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public AttributeDto? Attribute { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int LoggingViewId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public NumberRangeDto NumberRange { get; set; }

    /// <summary>
    /// Optional sequence number for ordering, defaults to 0
    /// </summary>
    public int? Sequence { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public double? WidthFactor  { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public ImageCategory? Type { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public StandardType? StandardType { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public ImageTypeDto? ImageType { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public ImageSubtypeDto? ImageSubtype { get; set; }
}