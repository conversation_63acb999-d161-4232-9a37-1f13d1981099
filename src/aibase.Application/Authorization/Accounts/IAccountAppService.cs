﻿using System.Threading.Tasks;
using Abp.Application.Services;
using aibase.Authorization.Accounts.Dto;

namespace aibase.Authorization.Accounts
{
    /// <summary>
    /// Application service that is used by 'Account' module.
    /// </summary>
    public interface IAccountAppService : IApplicationService
    {
        /// <summary>
        /// Tenant
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<IsTenantAvailableOutput> IsTenantAvailable(IsTenantAvailableInput input);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userName"></param>
        /// <returns></returns>
        Task<IsTenantAvailableOutput> GetTenantIdByUserName(string userName);

        /// <summary>
        /// Register
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<RegisterOutput> Register(RegisterInput input);
    }
}
