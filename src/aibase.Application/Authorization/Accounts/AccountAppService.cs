using System;
using System.Linq;
using System.Threading.Tasks;
using Abp.MultiTenancy;
using Abp.UI;
using aibase.Authorization.Accounts.Dto;
using aibase.Authorization.Roles;
using aibase.Authorization.Users;
using aibase.Editions;
using aibase.MultiTenancy;
using Abp.Domain.Uow;
using Abp.Domain.Repositories;
using aibase.Polygons;
using AiBase.Services;
using aibase.Settings;

namespace aibase.Authorization.Accounts;

/// <summary>
/// This class defines some methods for account registration.
/// </summary>
public class AccountAppService : aibaseAppServiceBase, IAccountAppService
{
    private readonly IAbpZeroDbMigrator _abpZeroDbMigrator;
    private readonly IRepository<Setting, int> _settingRepository;
    private readonly IRepository<Polygon, int> _polygonRepository;
    private readonly EditionManager _editionManager;
    private readonly TenantManager _tenantManager;
    private readonly RoleManager _roleManager;
    private readonly UserManager _userManager;
    private readonly Mailer _mailer;

    /// <summary>
    /// Constructor
    /// </summary>
    public AccountAppService(
        IRepository<Setting, int> settingRepository,
        IRepository<Polygon, int> polygonRepository,
        IAbpZeroDbMigrator abpZeroDbMigrator, 
        EditionManager editionManager, 
        TenantManager tenantManager, 
        RoleManager roleManager, 
        UserManager userManager,
        Mailer mailer)
    {
        _settingRepository = settingRepository;
        _polygonRepository = polygonRepository;
        _abpZeroDbMigrator = abpZeroDbMigrator;
        _mailer = mailer;
        _editionManager = editionManager;
        _tenantManager = tenantManager;
        _roleManager = roleManager;
        _userManager = userManager;
    }

    /// <inheritdoc />
    public async Task<IsTenantAvailableOutput> IsTenantAvailable(IsTenantAvailableInput input)
    {
        var tenant = await TenantManager.FindByTenancyNameAsync(input.TenancyName);
        if (tenant == null)
        {
            return new IsTenantAvailableOutput(TenantAvailabilityState.NotFound);
        }

        return !tenant.IsActive ? new IsTenantAvailableOutput(TenantAvailabilityState.InActive) : new IsTenantAvailableOutput(TenantAvailabilityState.Available, tenant.Id);
    }

    /// <inheritdoc />
    public async Task<RegisterOutput> Register(RegisterInput input)
    {
        // Create tenant
        var tenant = new Tenant(input.CompanyName.Replace(" ", "").ToLower(), input.CompanyName)
        {
            Country = input.Country,
            FirstAddress = input.FirstAddress,
            SecondAddress = input.SecondAddress,
            Suburb = input.Suburb,
            PostCode = input.PostCode,
            State = input.State,
            IsActive = true,
            ConnectionString = null
        };

        var defaultEdition = await _editionManager.FindByNameAsync(EditionManager.DefaultEditionName);
        if (defaultEdition != null)
        {
            tenant.EditionId = defaultEdition.Id;
        }

        await _tenantManager.CreateAsync(tenant);
        await CurrentUnitOfWork.SaveChangesAsync(); // To get new tenant's id.

        // Create tenant database
        _abpZeroDbMigrator.CreateOrMigrateForTenant(tenant);

        // We are working entities of new tenant, so changing tenant filter
        using (CurrentUnitOfWork.DisableFilter(AbpDataFilters.MayHaveTenant))
        {
            // Grant all permissions to admin role
            var adminRole = _roleManager.Roles.Single(r => r.Name == StaticRoleNames.Tenants.Admin);
            await _roleManager.GrantAllPermissionsAsync(adminRole);

            // Create admin user for the tenant
            var adminUser = User.CreateTenantAdminUser(tenant.Id, input.EmailAddress);
            adminUser.Name = input.EmailAddress.Split("@")[0];
            adminUser.Surname = input.EmailAddress.Split("@")[0];
            adminUser.FirstName = input.FirstName;
            adminUser.LastName = input.LastName;
            adminUser.IsActive = true;
            adminUser.IsEmailConfirmed = false;

            await UserManager.InitializeOptionsAsync(tenant.Id);
            CheckErrors(await UserManager.CreateAsync(adminUser, input.Password));
            await CurrentUnitOfWork.SaveChangesAsync(); // To get admin user's id

            // Assign admin user to role!
            CheckErrors(await UserManager.AddToRoleAsync(adminUser, adminRole.Name));
            await CurrentUnitOfWork.SaveChangesAsync();

            // Create setting account
            var setting = new Setting()
            {
                TenantId = tenant.Id,
                ProductName = $"{input.CompanyName}",
                CollectionName = "Drill holes",
            };

            await _settingRepository.InsertAsync(setting);
                
            // Create default polygon
            var boxUuid = Guid.NewGuid();
            var defaultBoxPolygon = new Polygon
            {
                Name = "Box 1",
                Type = PolygonType.BoundingBox,
                Description = "Default bounding box",
                Coordinates = $"[{{\"x\":100,\"y\":100,\"width\":1000,\"height\":1000,\"id\":\"{boxUuid}\",\"type\":\"Box\"}}]",
                TenantId = tenant.Id,
            };
            await _polygonRepository.InsertAsync(defaultBoxPolygon);
            await CurrentUnitOfWork.SaveChangesAsync();
                
            var rowUuid = Guid.NewGuid();
            var defaultRowPolygon = new Polygon
            {
                Name = "Rows 1",
                Type = PolygonType.BoundingRows,
                Description = "Default bounding row",
                Coordinates = $"[{{\"x\":200,\"y\":200,\"width\":800,\"height\":800,\"id\":\"{rowUuid}\",\"type\":\"Row\"}}]",
                TenantId = tenant.Id,
            };
            await _polygonRepository.InsertAsync(defaultRowPolygon);
            await CurrentUnitOfWork.SaveChangesAsync();

            // Send verification email
            var token = await UserManager.GenerateEmailConfirmationTokenAsync(adminUser);
            await _mailer.SendVerificationEmailAsync(input.EmailAddress, input.FirstName, input.LastName, adminUser.Id, token);
        }

        return new RegisterOutput
        {
            CanLogin = false,
            Message = "Please check your email to verify your account."
        };

    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="userName"></param>
    /// <returns></returns>
    /// <exception cref="UserFriendlyException"></exception>
    public Task<IsTenantAvailableOutput> GetTenantIdByUserName(string userName)
    {
        using (CurrentUnitOfWork.DisableFilter(AbpDataFilters.MayHaveTenant))
        {
            var user = _userManager.Users.FirstOrDefault(u => u.UserName == userName || u.EmailAddress == userName);
            if (user == null)
            {
                throw new UserFriendlyException(L("LoginFailed"), L("InvalidUserNameOrPassword"));
            }

            var tenant = _tenantManager.Tenants.FirstOrDefault(t => t.Id == user.TenantId);
            if (tenant == null)
            {
                return Task.FromResult(new IsTenantAvailableOutput(TenantAvailabilityState.NotFound));
            }

            return Task.FromResult(!tenant.IsActive ? new IsTenantAvailableOutput(TenantAvailabilityState.InActive) : new IsTenantAvailableOutput(TenantAvailabilityState.Available, tenant.Id));
        }
    }
}