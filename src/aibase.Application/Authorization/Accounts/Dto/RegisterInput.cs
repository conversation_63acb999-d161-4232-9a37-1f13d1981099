﻿using System.ComponentModel.DataAnnotations;
using Abp.Auditing;
using Abp.Authorization.Users;

namespace aibase.Authorization.Accounts.Dto;

/// <summary>
/// Used to register a new user.
/// </summary>
public class RegisterInput
{
    /// <summary>
    /// The last name user.
    /// </summary>
    ///
    [Required]
    [StringLength(AbpUserBase.MaxNameLength)]
    public required string LastName { get; set; }

    /// <summary>
    /// The first name user.
    /// </summary>
    [Required]
    [StringLength(AbpUserBase.MaxSurnameLength)]
    public required string FirstName { get; set; }

    /// <summary>
    /// The email address
    /// </summary>
    [Required]
    [EmailAddress]
    [StringLength(AbpUserBase.MaxEmailAddressLength)]
    public required string EmailAddress { get; set; }

    /// <summary>
    /// Password
    /// </summary>
    [Required]
    [StringLength(AbpUserBase.MaxPlainPasswordLength)]
    [DisableAuditing]
    public required string Password { get; set; }

    /// <summary>
    /// Tenancy name
    /// </summary>
    [Required]
    [StringLength(256)]
    public required string CompanyName { get; set; }

    /// <summary>
    /// Country
    /// </summary>
    [Required]
    [StringLength(64)]
    public required string Country { get; set; }

    /// <summary>
    /// State
    /// </summary>
    [StringLength(255)]
    public string? State { get; set; }

    /// <summary>
    /// Post code 
    /// </summary>
    [StringLength(64)]
    public string? PostCode { get; set; }

    /// <summary>
    /// Suburb
    /// </summary>
    [StringLength(255)]
    public string? Suburb { get; set; }

    /// <summary>
    /// First Address
    /// </summary>
    [StringLength(255)]
    public string? FirstAddress { get; set; }

    /// <summary>
    /// Second Address
    /// </summary>
    [StringLength(255)]
    public string? SecondAddress { get; set; }

    /// <summary>
    /// Captcha response
    /// </summary>
    [DisableAuditing]
    public string? CaptchaResponse { get; set; }
}