﻿using System;
using Abp;
using Abp.Authorization;
using Abp.Dependency;
using Abp.UI;

namespace aibase.Authorization;

/// <summary>
/// 
/// </summary>
public class AbpLoginResultTypeHelper : AbpServiceBase, ITransientDependency
{
    /// <inheritdoc />
    public AbpLoginResultTypeHelper()
    {
        LocalizationSourceName = aibaseConsts.LocalizationSourceName;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="result"></param>
    /// <param name="usernameOrEmailAddress"></param>
    /// <param name="tenancyName"></param>
    /// <returns></returns>
    public Exception CreateExceptionForFailedLoginAttempt(AbpLoginResultType result, string usernameOrEmailAddress, string tenancyName)
    {
        switch (result)
        {
            case AbpLoginResultType.Success:
                return new Exception("Don't call this method with a success result!");
            case AbpLoginResultType.InvalidUserNameOrEmailAddress:
            case AbpLoginResultType.InvalidPassword:
                return new UserFriendlyException(L("LoginFailed"), L("InvalidUserNameOrPassword"));
            case AbpLoginResultType.InvalidTenancyName:
                return new UserFriendlyException(L("LoginFailed"), L("ThereIsNoTenantDefinedWithName{0}", tenancyName));
            case AbpLoginResultType.TenantIsNotActive:
                return new UserFriendlyException(L("LoginFailed"), L("TenantIsNotActive", tenancyName));
            case AbpLoginResultType.UserIsNotActive:
                return new UserFriendlyException(L("LoginFailed"), L("UserIsNotActiveAndCanNotLogin", usernameOrEmailAddress));
            case AbpLoginResultType.UserEmailIsNotConfirmed:
                return new UserFriendlyException(L("LoginFailed"), L("UserEmailIsNotConfirmedAndCanNotLogin"));
            case AbpLoginResultType.LockedOut:
                return new UserFriendlyException(L("LoginFailed"), L("UserLockedOutMessage"));
            default: // Can not fall to default actually. But other result types can be added in the future and we may forget to handle it
                Logger.Warn("Unhandled login fail reason: " + result);
                return new UserFriendlyException(L("LoginFailed"));
        }
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="result"></param>
    /// <param name="usernameOrEmailAddress"></param>
    /// <param name="tenancyName"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public string CreateLocalizedMessageForFailedLoginAttempt(AbpLoginResultType result, string usernameOrEmailAddress, string tenancyName)
    {
        switch (result)
        {
            case AbpLoginResultType.Success:
                throw new Exception("Don't call this method with a success result!");
            case AbpLoginResultType.InvalidUserNameOrEmailAddress:
            case AbpLoginResultType.InvalidPassword:
                return L("InvalidUserNameOrPassword");
            case AbpLoginResultType.InvalidTenancyName:
                return L("ThereIsNoTenantDefinedWithName{0}", tenancyName);
            case AbpLoginResultType.TenantIsNotActive:
                return L("TenantIsNotActive", tenancyName);
            case AbpLoginResultType.UserIsNotActive:
                return L("UserIsNotActiveAndCanNotLogin", usernameOrEmailAddress);
            case AbpLoginResultType.UserEmailIsNotConfirmed:
                return L("UserEmailIsNotConfirmedAndCanNotLogin");
            default: // Can not fall to default actually. But other result types can be added in the future and we may forget to handle it
                Logger.Warn("Unhandled login fail reason: " + result);
                return L("LoginFailed");
        }
    }
}