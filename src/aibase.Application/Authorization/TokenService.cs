using System;
using System.Security.Cryptography;
using System.Threading.Tasks;
using Abp.Dependency;
using Abp.Domain.Repositories;
using Abp.UI;
using aibase.Authorization.Users;
using Microsoft.EntityFrameworkCore;

namespace aibase.Authorization;

/// <inheritdoc />
public class TokenService : ITransientDependency
{
    private readonly IRepository<RefreshToken, int> _refreshTokenRepository;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="refreshTokenRepository"></param>
    public TokenService(IRepository<RefreshToken, int> refreshTokenRepository)
    {
        _refreshTokenRepository = refreshTokenRepository;
    }
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    public async Task<RefreshToken> GenerateRefreshToken(long userId)
    {
        var refreshToken = new RefreshToken
        {
            Token = GenerateRefreshTokenString(),
            ExpiresAt = DateTime.UtcNow.AddDays(7),
            CreationTime = DateTime.UtcNow,
            UserId = userId
        };

        await _refreshTokenRepository.InsertAsync(refreshToken);
        return refreshToken;
    }
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="token"></param>
    /// <returns></returns>
    /// <exception cref="UserFriendlyException"></exception>
    public async Task<RefreshToken> ValidateRefreshToken(string token)
    {
        var refreshToken = await _refreshTokenRepository.GetAllIncluding(x => x.User)
            .FirstOrDefaultAsync(r => r.Token == token);

        if (refreshToken == null)
            throw new UserFriendlyException("Invalid refresh token");

        if (refreshToken.ExpiresAt < DateTime.UtcNow)
            throw new UserFriendlyException("Refresh token expired");

        // if (refreshToken.RevokedAt != null)
        //     throw new UserFriendlyException("Refresh token revoked");

        return refreshToken;
    }

    private static string GenerateRefreshTokenString()
    {
        var randomBytes = new byte[32];
        using var rng = new RNGCryptoServiceProvider();
        rng.GetBytes(randomBytes);
        return Convert.ToBase64String(randomBytes);
    }
}