using System.Collections.Generic;
using Abp.Application.Services.Dto;
using AutoMapper;

namespace aibase.NumberRanges.Dto;

/// <inheritdoc />
[AutoMap(typeof(NumberRange))]
public class NumberRangeDto : EntityDto
{
    /// <summary>
    /// Name of the number range
    /// </summary>
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// Minimum display value
    /// </summary>
    public double MinDisplayValue { get; set; }
    
    /// <summary>
    /// Maximum display value
    /// </summary>
    public double MaxDisplayValue { get; set; }
    
    /// <summary>
    /// Whether the range is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Intervals in this number range
    /// </summary>
    public List<NumberRangeIntervalDto> Intervals { get; set; } = [];
}