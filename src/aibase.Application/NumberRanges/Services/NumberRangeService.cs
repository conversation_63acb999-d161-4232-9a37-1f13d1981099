using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.LoggingViewColumns;
using aibase.NumberRanges.Dto;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.NumberRanges.Services;

/// <inheritdoc />
public class NumberRangeService : INumberRangeService
{
    private readonly IRepository<NumberRange, int> _repository;
    private readonly IRepository<LoggingViewColumn, int> _loggingRepository;
    private readonly IRepository<NumberRangeInterval, int> _numberRangeIntervalRepository;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;
    private readonly IUnitOfWorkManager _unitOfWorkManager;

    /// <summary>
    /// Constructor for NumberRangeService
    /// </summary>
    public NumberRangeService(
        IRepository<NumberRange, int> repository,
        IAbpSession abpSession,
        IMapper mapper,
        IUnitOfWorkManager unitOfWorkManager, IRepository<LoggingViewColumn, int> loggingRepository, IRepository<NumberRangeInterval, int> numberRangeIntervalRepository)
    {
        _repository = repository;
        _abpSession = abpSession;
        _mapper = mapper;
        _unitOfWorkManager = unitOfWorkManager;
        _loggingRepository = loggingRepository;
        _numberRangeIntervalRepository = numberRangeIntervalRepository;
    }

    /// <inheritdoc />
    public async Task<NumberRange> CreateAsync(CreateNumberRangeDto input, bool returnExist = false)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }
        var tenantId = _abpSession.GetTenantId();

        if (input.MaxDisplayValue <= input.MinDisplayValue)
        {
            throw new UserFriendlyException("Max display value must be greater than min display value.");
        }

        var existingNumberRange = await _repository
            .FirstOrDefaultAsync(x => x.TenantId == tenantId && x.Name == input.Name);
        if (existingNumberRange != null)
        {
            if (returnExist)
            {
                return existingNumberRange;
            }
            
            throw new UserFriendlyException($"A NumberRange with the name {existingNumberRange.Name} already exists.");
        }

        var numberRange = new NumberRange()
        {
            Name = input.Name,
            MinDisplayValue = input.MinDisplayValue,
            MaxDisplayValue = input.MaxDisplayValue,
            IsActive = input.IsActive,
            TenantId = tenantId
        };

        await _repository.InsertAsync(numberRange);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        return numberRange;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<NumberRangeDto>> GetAllAsync(PagedNumberRangeResultRequestDto input)
    {
        var query = _repository.GetAll()
            .Include(x => x.Intervals)
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
            .WhereIf(input.Value.HasValue, x => x.MinDisplayValue <= input.Value && x.MaxDisplayValue >= input.Value)
            .OrderBy(r => r.Name);

        var totalCount = await query.CountAsync();

        var numberRanges = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        return new PagedResultDto<NumberRangeDto>(totalCount, _mapper.Map<List<NumberRangeDto>>(numberRanges));
    }

    /// <inheritdoc />
    public async Task<NumberRangeDto> UpdateAsync(UpdateNumberRangeDto input)
    {
        var numberRange = await ValidateNumberRangeEntity(input.Id);
        
        if (input.MaxDisplayValue.HasValue || input.MinDisplayValue.HasValue)
        {
            var newMin = input.MinDisplayValue ?? numberRange.MinDisplayValue;
            var newMax = input.MaxDisplayValue ?? numberRange.MaxDisplayValue;
            
            if (newMax <= newMin)
            {
                throw new UserFriendlyException("Max display value must be greater than min display value.");
            }
            
            //// Check if any intervals would be outside the new range
            //var invalidIntervals = await _repository.GetAll()
            //    .Include(x => x.Intervals)
            //    .Where(x => x.Id == input.Id)
            //    .SelectMany(x => x.Intervals)
            //    .Where(i => i.IntervalMin < newMin || i.IntervalMax > newMax)
            //    .AnyAsync();

            //if (invalidIntervals)
            //{
            //    throw new UserFriendlyException("Cannot update display values: Some intervals would be outside the new range.");
            //}
            
            numberRange.MinDisplayValue = newMin;
            numberRange.MaxDisplayValue = newMax;
        }

        numberRange.Name = input.Name ?? numberRange.Name;
        numberRange.IsActive = input.IsActive ?? numberRange.IsActive;

        await _repository.UpdateAsync(numberRange);
        return _mapper.Map<NumberRangeDto>(numberRange);
    }

    /// <inheritdoc />
    public async Task<NumberRangeDto> GetAsync(EntityDto<int> input)
    {
        var numberRange = await _repository.GetAll()
            .Include(x => x.Intervals)
            .FirstOrDefaultAsync(x => x.Id == input.Id);

        if (numberRange == null)
        {
            throw new EntityNotFoundException(typeof(NumberRange), input.Id);
        }

        return _mapper.Map<NumberRangeDto>(numberRange);
    }

    /// <inheritdoc />
    public async Task DeleteAsync(EntityDto<int> input)
    {
        var numberRange = await ValidateNumberRangeEntity(input.Id);
        
        var loggingViewColumnUsed = await _loggingRepository.FirstOrDefaultAsync(x =>  x.NumberRangeId == numberRange.Id);
        if (loggingViewColumnUsed != null)
        {
            throw new UserFriendlyException("Cannot delete this NumberRange because it is being used in LoggingViewColumn.");
        }
        
        var numberRangeIntervalUsed = await _numberRangeIntervalRepository.FirstOrDefaultAsync(x =>  x.NumberRangeId == numberRange.Id);
        if (numberRangeIntervalUsed != null)
        {
            throw new UserFriendlyException("Cannot delete this NumberRange because it is being used in NumberRangeInterval.");
        }
        
        await _repository.DeleteAsync(input.Id);
    }

    private async Task<NumberRange> ValidateNumberRangeEntity(int id)
    {
        var numberRange = await _repository.GetAll()
            .Include(x => x.Intervals)
            .FirstOrDefaultAsync(x => x.Id == id);

        if (numberRange == null)
        {
            throw new EntityNotFoundException(typeof(NumberRange), id);
        }

        return numberRange;
    }
}