using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.RockTree.Dto;

namespace aibase.RockTree
{
    /// <summary>
    /// Service for managing Rock Tree Roots.
    /// </summary>
    public class RockTreeService : AsyncCrudAppService<RockTreeRoot, RockTreeRootDto, int, PagedRockTreeRootResultRequestDto, CreateRockTreeRootInput, UpdateRockTreeRootInput>, IRockTreeService
    {
        private readonly IRepository<RockTreeRoot, int> _rockTreeRootRepository;
        private readonly IAbpSession _abpSession;

        /// <summary>
        /// Initializes a new instance of the <see cref="RockTreeService"/> class.
        /// </summary>
        /// <param name="repository">The repository for <see cref="RockTreeRoot"/> entities.</param>
        /// <param name="abpSession">The ABP session to get current tenant information.</param>
        public RockTreeService(IRepository<RockTreeRoot, int> repository, IAbpSession abpSession)
            : base(repository)
        {
            _rockTreeRootRepository = repository;
            _abpSession = abpSession;
        }

        /// <inheritdoc />
        protected override RockTreeRoot MapToEntity(CreateRockTreeRootInput createInput)
        {
            if (_abpSession.TenantId == null)
            {
                throw new UserFriendlyException("The account does not have permission to perform this feature.");
            }
            var entity = base.MapToEntity(createInput);
            entity.TenantId = _abpSession.GetTenantId();
            return entity;
        }

        /// <inheritdoc />
        protected override async Task<RockTreeRoot> GetEntityByIdAsync(int id)
        {
            var entity = await base.GetEntityByIdAsync(id);
            if (entity.TenantId != _abpSession.GetTenantId())
            {
                throw new EntityNotFoundException(typeof(RockTreeRoot), id);
            }
            return entity;
        }

        /// <inheritdoc />
        protected override void MapToEntity(UpdateRockTreeRootInput updateInput, RockTreeRoot entity)
        {
            if (entity.TenantId != _abpSession.GetTenantId())
            {
                throw new UserFriendlyException("You do not have permission to update this entity.");
            }
            base.MapToEntity(updateInput, entity);
        }

        /// <inheritdoc />
        public override async Task<RockTreeRootDto> UpdateAsync(UpdateRockTreeRootInput input)
        {
            CheckUpdatePermission(); // Standard ABP permission check

            var entity = await GetEntityByIdAsync(input.Id); // This now includes tenant check
            // entity.TenantId will be checked by GetEntityByIdAsync

            MapToEntity(input, entity); // Maps input to the existing entity
            await CurrentUnitOfWork.SaveChangesAsync();

            return MapToEntityDto(entity);
        }

        /// <inheritdoc />
        public override async Task DeleteAsync(EntityDto<int> input)
        {
            CheckDeletePermission(); // Standard ABP permission check
            var entity = await GetEntityByIdAsync(input.Id); // This now includes tenant check
            // entity.TenantId will be checked by GetEntityByIdAsync

            await Repository.DeleteAsync(entity);
        }


        /// <summary>
        /// Creates a filtered query for RockTreeRoots based on the input.
        /// </summary>
        /// <param name="input">The input DTO containing filter parameters.</param>
        /// <returns>An <see cref="IQueryable{RockTreeRoot}"/> for filtered entities.</returns>
        protected override IQueryable<RockTreeRoot> CreateFilteredQuery(PagedRockTreeRootResultRequestDto input)
        {
            return base.CreateFilteredQuery(input)
                .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId()) // Tenant Filter
                .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x =>
                    input.Keyword != null && ((x.Name.Contains(input.Keyword)) ||
                                              (x.Description != null && x.Description.Contains(input.Keyword))))
                .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
                .WhereIf(input.GeologySuiteId.HasValue, x => input.GeologySuiteId != null && x.GeologySuiteId == input.GeologySuiteId.Value);
        }
        
        // CRUD operations are inherited from AsyncCrudAppService

        /// <summary>
        /// Associates a Geology Suite with a Rock Tree Root.
        /// </summary>
        /// <param name="input">Input DTO containing RockTreeRootId and GeologySuiteId.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        public async Task AssociateGeologySuiteAsync(AssociateGeologySuiteInput input)
        {
            var rockTreeRoot = await GetEntityByIdAsync(input.RockTreeRootId); // Validates tenant
            // TODO: Validate if GeologySuiteId exists if necessary, or rely on FK constraints
            rockTreeRoot.GeologySuiteId = input.GeologySuiteId;
            await _rockTreeRootRepository.UpdateAsync(rockTreeRoot);
        }

        /// <summary>
        /// Disassociates a Geology Suite from a Rock Tree Root.
        /// </summary>
        /// <param name="input">Input DTO containing RockTreeRootId.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        public async Task DisassociateGeologySuiteAsync(DisassociateGeologySuiteInput input)
        {
            var rockTreeRoot = await GetEntityByIdAsync(input.RockTreeRootId); // Validates tenant
            rockTreeRoot.GeologySuiteId = null;
            await _rockTreeRootRepository.UpdateAsync(rockTreeRoot);
        }
    }
}