using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Abp.Application.Services;
using Abp.Domain.Repositories;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.RockTree.Dto;
using Microsoft.EntityFrameworkCore;

namespace aibase.RockTree;

/// <summary>
/// Provides services for traversing and querying rock tree structures.
/// </summary>
public class RockTreeTraversalService : ApplicationService, IRockTreeTraversalService
{
    private readonly IRepository<RockNode, int> _rockNodeRepository;
    private readonly IRepository<RockNodeRelation, int> _rockNodeRelationRepository;
    private readonly IRepository<RockTreeRootNode, int> _rockTreeRootNodeRepository;
    private readonly IRepository<RockTreeRoot, int> _rockTreeRootRepository;
    private readonly IAbpSession _abpSession;

    /// <summary>
    /// Initializes a new instance of the <see cref="RockTreeTraversalService"/> class.
    /// </summary>
    public RockTreeTraversalService(
        IRepository<RockNode, int> rockNodeRepository,
        IRepository<RockNodeRelation, int> rockNodeRelationRepository,
        IRepository<RockTreeRootNode, int> rockTreeRootNodeRepository,
        IRepository<RockTreeRoot, int> rockTreeRootRepository,
        IAbpSession abpSession)
    {
        _rockNodeRepository = rockNodeRepository;
        _rockNodeRelationRepository = rockNodeRelationRepository;
        _rockTreeRootNodeRepository = rockTreeRootNodeRepository;
        _rockTreeRootRepository = rockTreeRootRepository;
        _abpSession = abpSession;
    }

    private int GetCurrentTenantId()
    {
        if (!_abpSession.TenantId.HasValue)
        {
            throw new UserFriendlyException("TenantId is not available in the current session.");
        }

        return _abpSession.TenantId.Value;
    }

    /// <summary>
    /// Gets the direct children of a given rock node or rock tree root.
    /// </summary>
    public async Task<List<RockNodeDto>> GetChildrenAsync(int nodeId, bool includeInactive = false)
    {
        var tenantId = GetCurrentTenantId();

        // Validate if nodeId (as RockNode or RockTreeRoot) exists for the current tenant
        var nodeAsRockNodeExists =
            await _rockNodeRepository.GetAll().AnyAsync(n => n.Id == nodeId && n.TenantId == tenantId);
        var nodeAsRockTreeRootExists = await _rockTreeRootRepository.GetAll()
            .AnyAsync(rtr => rtr.Id == nodeId && rtr.TenantId == tenantId);

        if (!nodeAsRockNodeExists && !nodeAsRockTreeRootExists)
        {
            throw new UserFriendlyException(
                $"Node or RockTreeRoot with Id {nodeId} not found for the current tenant.");
        }

        IQueryable<RockNode> query;

        // Check if nodeId refers to a RockTreeRoot for the current tenant
        var isTenantRockTreeRoot = nodeAsRockTreeRootExists; // True if nodeId is a RockTreeRootId of current tenant

        if (isTenantRockTreeRoot)
        {
            // nodeId is a RockTreeRootId
            query = from rnm in _rockTreeRootNodeRepository.GetAllIncluding(r => r.RockTreeRoot,
                    r => r.RockNode.RockType.RockStyle)
                where rnm.RockTreeRootId == nodeId &&
                      rnm.RockTreeRoot.TenantId == tenantId &&
                      rnm.RockNode.TenantId == tenantId
                orderby rnm.DisplayOrder
                select rnm.RockNode;
        }
        else
        {
            // nodeId is a RockNodeId, already validated to be in tenant
            query = from relation in _rockNodeRelationRepository.GetAll()
                join node in _rockNodeRepository.GetAllIncluding(n => n.RockType.RockStyle)
                    on relation.ChildNodeId equals node.Id
                where relation.ParentNodeId == nodeId &&
                      relation.IsPrimary &&
                      node.TenantId == tenantId // Ensure child node is also in tenant
                orderby relation.DisplayOrder
                select node;
        }

        if (!includeInactive)
        {
            query = query.Where(n => n.IsActive);
        }

        var children = await query.ToListAsync();
        return ObjectMapper.Map<List<RockNodeDto>>(children);
    }

    /// <summary>
    /// Gets all descendants of a given rock node.
    /// </summary>
    public async Task<List<RockNodeDto>> GetDescendantsAsync(int nodeId, bool includeInactive = false)
    {
        var tenantId = GetCurrentTenantId();
        // Validate starting nodeId
        var initialNode =
            await _rockNodeRepository.FirstOrDefaultAsync(n => n.Id == nodeId && n.TenantId == tenantId);
        if (initialNode == null)
        {
            // Check if it's a RockTreeRootId for the tenant
            var initialRoot =
                await _rockTreeRootRepository.FirstOrDefaultAsync(rtr =>
                    rtr.Id == nodeId && rtr.TenantId == tenantId);
            if (initialRoot == null)
            {
                throw new UserFriendlyException(
                    $"Node or RockTreeRoot with Id {nodeId} not found for the current tenant to get descendants.");
            }
        }

        var descendants = new List<RockNode>();
        await CollectDescendantsAsync(nodeId, descendants, includeInactive, new HashSet<int>(), tenantId);
        return ObjectMapper.Map<List<RockNodeDto>>(descendants);
    }

    private async Task CollectDescendantsAsync(int parentId, List<RockNode> allDescendants, bool includeInactive,
        HashSet<int> visited, int tenantId)
    {
        if (!visited.Add(parentId)) return; // Cycle detection

        // GetChildrenAsync is now tenant-aware
        var childrenDto = await GetChildrenAsync(parentId, true);

        foreach (var childDto in childrenDto)
        {
            // childDto is from GetChildrenAsync, so it's already confirmed to be in tenant if GetChildrenAsync is correct.
            // Re-fetch to get the entity, ensuring it's still valid for the tenant (though GetChildrenAsync should guarantee this)
            var childNode =
                await _rockNodeRepository.FirstOrDefaultAsync(n => n.Id == childDto.Id && n.TenantId == tenantId);
            if (childNode == null) continue; // Should not happen if GetChildrenAsync is correct

            if (includeInactive || childNode.IsActive)
            {
                if (allDescendants.All(d => d.Id != childNode.Id))
                {
                    allDescendants.Add(childNode);
                }
            }

            await CollectDescendantsAsync(childNode.Id, allDescendants, includeInactive, visited, tenantId);
        }
    }

    /// <summary>
    /// Gets all ancestors of a given rock node, up to the root of its tree.
    /// </summary>
    public async Task<List<RockNodeDto>> GetAncestorsAsync(int nodeId, bool includeInactive = false)
    {
        var tenantId = GetCurrentTenantId();
        var initialNode =
            await _rockNodeRepository.FirstOrDefaultAsync(n => n.Id == nodeId && n.TenantId == tenantId);
        if (initialNode == null)
        {
            throw new UserFriendlyException($"Node with Id {nodeId} not found for the current tenant.");
        }

        var ancestorEntities = await GetAncestorEntitiesAsync(nodeId, includeInactive, tenantId);
        return ObjectMapper.Map<List<RockNodeDto>>(ancestorEntities);
    }

    private async Task<List<RockNode>> GetAncestorEntitiesAsync(int nodeId, bool includeInactive, int tenantId,
        int? stopAtRootId = null)
    {
        var ancestors = new List<RockNode>();
        var currentNodeId = nodeId; // Already validated to be in tenant by caller
        var visited = new HashSet<int>();

        while (true)
        {
            if (!visited.Add(currentNodeId)) break;

            var parentRelation =
                await _rockNodeRelationRepository.FirstOrDefaultAsync(r =>
                    r.ChildNodeId == currentNodeId && r.IsPrimary);
            if (parentRelation == null) break;

            var parentNode = await _rockNodeRepository.GetAllIncluding(n => n.RockType.RockStyle)
                .FirstOrDefaultAsync(n => n.Id == parentRelation.ParentNodeId && n.TenantId == tenantId);
            if (parentNode == null) break; // Parent not found or not in tenant

            if (includeInactive || parentNode.IsActive)
            {
                ancestors.Insert(0, parentNode);
            }

            if (stopAtRootId.HasValue)
            {
                var rootMap = await _rockTreeRootNodeRepository.GetAllIncluding(rtr => rtr.RockTreeRoot)
                    .FirstOrDefaultAsync(rnm => rnm.RockNodeId == parentNode.Id &&
                                                rnm.RockTreeRootId == stopAtRootId.Value &&
                                                rnm.RockTreeRoot.TenantId == tenantId);
                if (rootMap != null) break;
            }
            else
            {
                if (await _rockTreeRootNodeRepository.GetAllIncluding(rtr => rtr.RockTreeRoot)
                        .AnyAsync(rnm => rnm.RockNodeId == parentNode.Id && rnm.RockTreeRoot.TenantId == tenantId))
                {
                    break;
                }
            }

            currentNodeId = parentNode.Id;
        }

        return ancestors;
    }

    /// <summary>
    /// Gets the path from a rock tree root (or the node's actual root) to a specified target rock node.
    /// </summary>
    public async Task<RockNodePathDto> GetPathAsync(int nodeId, int? rockTreeRootId = null)
    {
        var tenantId = GetCurrentTenantId();
        var targetNode =
            await _rockNodeRepository.FirstOrDefaultAsync(n => n.Id == nodeId && n.TenantId == tenantId);
        if (targetNode == null)
            throw new UserFriendlyException($"Target node with Id {nodeId} not found for the current tenant.");

        if (rockTreeRootId.HasValue)
        {
            var rootForTenant = await _rockTreeRootRepository.FirstOrDefaultAsync(rtr =>
                rtr.Id == rockTreeRootId.Value && rtr.TenantId == tenantId);
            if (rootForTenant == null)
                throw new UserFriendlyException(
                    $"RockTreeRoot with Id {rockTreeRootId.Value} not found for the current tenant.");
        }

        var pathDto = new RockNodePathDto
        {
            TargetNodeId = targetNode.Id,
            TargetNodeName = targetNode.Name,
            RootId = rockTreeRootId
        };

        var ancestorEntities = await GetAncestorEntitiesAsync(nodeId, true, tenantId, rockTreeRootId);

        pathDto.Path.AddRange(ancestorEntities.Select(a => new RockNodePathItemDto
            { Id = a.Id, Name = a.Name, NodeType = a.NodeType, IsActive = a.IsActive }));
        pathDto.Path.Add(new RockNodePathItemDto
        {
            Id = targetNode.Id, Name = targetNode.Name, NodeType = targetNode.NodeType,
            IsActive = targetNode.IsActive
        });

        if (ancestorEntities.Any())
        {
            var firstAncestor = ancestorEntities.First();
            var rootMap = await _rockTreeRootNodeRepository.GetAllIncluding(rtr => rtr.RockTreeRoot)
                .FirstOrDefaultAsync(rnm =>
                    rnm.RockNodeId == firstAncestor.Id && rnm.RockTreeRoot.TenantId == tenantId);
            if (rootMap != null)
            {
                pathDto.RootId = rootMap.RockTreeRootId;
            }
        }
        else
        {
            var rootMap = await _rockTreeRootNodeRepository.GetAllIncluding(rtr => rtr.RockTreeRoot)
                .FirstOrDefaultAsync(
                    rnm => rnm.RockNodeId == targetNode.Id && rnm.RockTreeRoot.TenantId == tenantId);
            if (rootMap != null)
            {
                pathDto.RootId = rootMap.RockTreeRootId;
            }
        }

        if (rockTreeRootId.HasValue && (!pathDto.RootId.HasValue || pathDto.RootId.Value != rockTreeRootId.Value))
        {
            // If a specific rockTreeRootId was provided, but the resolved path's root doesn't match,
            // it means the targetNode is not part of the specified rockTreeRootId for the tenant.
            // We can clear the path or throw an error. For now, let's indicate the path is not in context.
            // This might occur if GetAncestorEntitiesAsync stops early due to stopAtRootId,
            // but the actual root of the node is different.
            // Or if targetNode is a root of a different tree.
            // If rockTreeRootId is specified, the path should strictly be within that root.

            // Re-evaluate if the targetNode itself is a root of the specified rockTreeRootId
            var isTargetNodeTheRoot = await _rockTreeRootNodeRepository.GetAllIncluding(rtr => rtr.RockTreeRoot)
                .AnyAsync(rnm =>
                    rnm.RockNodeId == targetNode.Id && rnm.RockTreeRootId == rockTreeRootId.Value &&
                    rnm.RockTreeRoot.TenantId == tenantId);

            if (!isTargetNodeTheRoot && !ancestorEntities.Any(a =>
                    _rockTreeRootNodeRepository.GetAllIncluding(rtr => rtr.RockTreeRoot)
                        .Any(rnm => rnm.RockNodeId == a.Id && rnm.RockTreeRootId == rockTreeRootId.Value &&
                                    rnm.RockTreeRoot.TenantId == tenantId)))
            {
                // If neither target nor any ancestor is the specified root, then the path is invalid for this context.
                // Consider throwing an exception or returning an empty/error path DTO.
                // For now, we'll let the potentially "incorrect" RootId (if any was found) be, client needs to be aware.
                // A stricter approach:
                // throw new UserFriendlyException($"Node {nodeId} is not part of RockTreeRoot {rockTreeRootId.Value} for the current tenant.");
            }

            // Ensure pathDto.RootId is set to the requested rockTreeRootId if the path is valid for it.
            if (pathDto.RootId.HasValue && pathDto.RootId.Value == rockTreeRootId.Value)
            {
                // Path is consistent with requested root.
            }
            else if (isTargetNodeTheRoot)
            {
                pathDto.RootId = rockTreeRootId;
            }
            // else: path is not within the specified root, pathDto.RootId might be its actual root or null.
        }

        return pathDto;
    }

    // Method GetNodesByCategoryIdAsync removed as RockTypeCategoryMapping is removed.

    /// <summary>
    /// Gets all rock nodes associated with a specific rock type ID.
    /// </summary>
    public async Task<List<RockNodeDto>> GetNodesByRockTypeIdAsync(int rockTypeId, int? rockTreeRootId = null,
        bool includeInactive = false)
    {
        var tenantId = GetCurrentTenantId();
        var query = _rockNodeRepository.GetAllIncluding(n => n.RockType.RockStyle)
            .Where(n => n.RockTypeId == rockTypeId && n.TenantId == tenantId);

        if (!includeInactive)
        {
            query = query.Where(n => n.IsActive);
        }

        if (rockTreeRootId.HasValue)
        {
            // Ensure the specified RockTreeRootId belongs to the current tenant
            var tenantRockTreeRoot = await _rockTreeRootRepository.FirstOrDefaultAsync(rtr =>
                rtr.Id == rockTreeRootId.Value && rtr.TenantId == tenantId);
            if (tenantRockTreeRoot == null)
            {
                // If the specified root doesn't belong to the tenant, no nodes can be part of it for this tenant.
                return new List<RockNodeDto>();
            }

            // Get all node IDs that are part of this specific tenant's RockTreeRoot
            // This includes direct root nodes and all their tenant-aware descendants.
            var allNodeIdsInTenantTree = new HashSet<int>();
            var directRootNodes = await _rockTreeRootNodeRepository
                .GetAllIncluding(rnm => rnm.RockNode) // Ensure RockNode is included for TenantId check
                .Where(rnm => rnm.RockTreeRootId == rockTreeRootId.Value && rnm.RockNode.TenantId == tenantId)
                .Select(rnm => rnm.RockNodeId)
                .ToListAsync();

            foreach (var directRootNodeId in directRootNodes)
            {
                if (allNodeIdsInTenantTree.Add(directRootNodeId)) // Add if not already present
                {
                    // GetDescendantsAsync is now tenant-aware.
                    // It expects the ID of a RockNode or a RockTreeRoot. Here, directRootNodeId is a RockNodeId.
                    var descendants =
                        await GetDescendantsAsync(directRootNodeId,
                            true); // includeInactive = true to get all potential nodes
                    foreach (var desc in descendants)
                    {
                        allNodeIdsInTenantTree.Add(desc.Id);
                    }
                }
            }

            query = query.Where(n => allNodeIdsInTenantTree.Contains(n.Id));
        }

        var nodes = await query.ToListAsync();
        return ObjectMapper.Map<List<RockNodeDto>>(nodes);
    }

    /// <summary>
    /// Retrieves the complete tree structure for the current tenant.
    /// </summary>
    /// <param name="parentNodeId"></param>
    /// <param name="cancellationToken">Cancellation token for async operation.</param>
    /// <returns>A hierarchical tree structure containing all nodes for the tenant.</returns>
    public async Task<TreeStructureDto> GetTenantTreeStructureAsync(string parentNodeId = null,
        CancellationToken cancellationToken = default)
    {
        var tenantId = 0; // Initialize to a default value
        try
        {
            tenantId = GetCurrentTenantId();

            Logger.Info($"Starting tree structure retrieval for tenant {tenantId}, parentNodeId: '{parentNodeId}'");

            var result = new TreeStructureDto
            {
                TenantId = tenantId
            };

            if (string.IsNullOrWhiteSpace(parentNodeId))
            {
                Logger.Debug($"Fetching full tree for tenant {tenantId}");

                // First, get nodes that are direct roots of RockTreeRoots
                var directRootNodesQuery =
                    from rootNodeMap in _rockTreeRootNodeRepository.GetAllIncluding(rnm => rnm.RockNode,
                        rnm => rnm.RockTreeRoot)
                    where rootNodeMap.RockNode.TenantId == tenantId &&
                          rootNodeMap.RockTreeRoot.TenantId == tenantId
                    orderby rootNodeMap.DisplayOrder
                    select rootNodeMap.RockNode;

                var directRootNodes = await directRootNodesQuery.ToListAsync(cancellationToken);

                // Second, get orphan nodes that have no parent relations
                var allNodeIdsInTenant = await _rockNodeRepository.GetAll()
                    .Where(n => n.TenantId == tenantId)
                    .Select(n => n.Id)
                    .ToListAsync(cancellationToken);

                var nodesWithParentsInTenant = await _rockNodeRelationRepository.GetAll()
                    .Where(r => allNodeIdsInTenant.Contains(r.ChildNodeId))
                    .Select(r => r.ChildNodeId)
                    .Distinct()
                    .ToListAsync(cancellationToken);

                var directRootIdsSet = directRootNodes.Select(n => n.Id).ToHashSet();
                var orphanNodeIds = allNodeIdsInTenant
                    .Where(id => !nodesWithParentsInTenant.Contains(id) && !directRootIdsSet.Contains(id))
                    .ToList();

                var orphanNodes = await _rockNodeRepository.GetAll()
                    .Where(n => orphanNodeIds.Contains(n.Id) && n.TenantId == tenantId)
                    .ToListAsync(cancellationToken);

                // Combine both types of root nodes, ensuring uniqueness
                var allRootNodes = directRootNodes.Concat(orphanNodes).DistinctBy(n => n.Id).ToList();

                Logger.Debug($"Found {allRootNodes.Count} root nodes for tenant {tenantId} (full tree)");

                // Step 2: Build the tree structure by recursively adding children to each root node
                foreach (var rootNode in allRootNodes)
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    var treeNode = new TreeNodeDto
                    {
                        Id = rootNode.Id,
                        Name = rootNode.Name,
                        Code = rootNode.Code,
                        Description = rootNode.Description,
                        NodeType = rootNode.NodeType,
                        RockTypeId = rootNode.RockTypeId,
                        IsActive = rootNode.IsActive,
                        DisplayColor = rootNode.DisplayColor,
                        IconUrl = rootNode.IconUrl,
                        ParentId = null // Root nodes have no parent
                    };

                    await BuildTreeRecursivelyAsync(treeNode, tenantId, new HashSet<int>(), cancellationToken);

                    result.RootNodes.Add(treeNode);
                }
            }
            else
            {
                // New logic for subtree
                Logger.Debug($"Fetching subtree for parentNodeId '{parentNodeId}' for tenant {tenantId}");
                if (!int.TryParse(parentNodeId, out var actualParentNodeId))
                {
                    throw new UserFriendlyException(
                        $"Invalid parentNodeId format: '{parentNodeId}'. Expected an integer ID.");
                }

                var startingNode = await _rockNodeRepository.GetAll()
                    .Where(n => n.Id == actualParentNodeId && n.TenantId == tenantId)
                    .FirstOrDefaultAsync(cancellationToken);

                if (startingNode == null)
                {
                    throw new UserFriendlyException(
                        $"Parent node with ID '{actualParentNodeId}' not found for the current tenant.");
                }

                Logger.Debug(
                    $"Found starting node ID {startingNode.Id} ('{startingNode.Name}') for subtree for tenant {tenantId}");

                var treeNode = new TreeNodeDto
                {
                    Id = startingNode.Id,
                    Name = startingNode.Name,
                    Code = startingNode.Code,
                    Description = startingNode.Description,
                    NodeType = startingNode.NodeType,
                    RockTypeId = startingNode.RockTypeId,
                    IsActive = startingNode.IsActive,
                    DisplayColor = startingNode.DisplayColor,
                    IconUrl = startingNode.IconUrl,
                    ParentId = null // This is the root of the subtree
                };

                await BuildTreeRecursivelyAsync(treeNode, tenantId, new HashSet<int>(), cancellationToken);
                result.RootNodes.Add(treeNode);
            }

            Logger.Info(
                $"Completed tree structure retrieval for tenant {tenantId}, found {result.RootNodes.Count} root node(s) in the result.");

            return result;
        }
        catch (Exception ex)
        {
            var currentTenantInfo = _abpSession.TenantId?.ToString() ?? "N/A (before session init or error)";
            if (tenantId != 0) // If GetCurrentTenantId() succeeded
            {
                currentTenantInfo = tenantId.ToString();
            }

            Logger.Error(
                $"Error retrieving tree structure (Tenant: {currentTenantInfo}, ParentNodeId: '{parentNodeId}'): {ex.Message}",
                ex);
            throw;
        }
    }

    /// <summary>
    /// Recursively builds the tree structure by adding children to the specified node.
    /// </summary>
    /// <param name="parentNode">The parent node to add children to</param>
    /// <param name="tenantId">The tenant ID</param>
    /// <param name="visitedNodes">Set of already visited node IDs to prevent cycles</param>
    /// <param name="cancellationToken">Cancellation token</param>
    private async Task BuildTreeRecursivelyAsync(TreeNodeDto parentNode, int tenantId, HashSet<int> visitedNodes,
        CancellationToken cancellationToken)
    {
        // Add current node to visited set to prevent cycles
        if (!visitedNodes.Add(parentNode.Id))
        {
            Logger.Warn($"Detected cycle in tree structure for node ID {parentNode.Id}, skipping");
            return;
        }

        // Get child nodes for this parent from the relations
        var childRelations = await _rockNodeRelationRepository.GetAllIncluding(r => r.ChildNode)
            .Where(r => r.ParentNodeId == parentNode.Id &&
                        r.ChildNode.TenantId == tenantId)
            .OrderBy(r => r.DisplayOrder)
            .ToListAsync(cancellationToken);

        foreach (var relation in childRelations)
        {
            cancellationToken.ThrowIfCancellationRequested();

            // Skip if we've already visited this node (prevents cycles)
            if (visitedNodes.Contains(relation.ChildNodeId)) continue;

            // Get the full child node entity
            var childNode = await _rockNodeRepository.GetAsync(relation.ChildNodeId);
            if (childNode.TenantId != tenantId) continue; // Additional tenant safety check

            var childTreeNode = new TreeNodeDto
            {
                Id = childNode.Id,
                Name = childNode.Name,
                Code = childNode.Code,
                Description = childNode.Description,
                NodeType = childNode.NodeType,
                RockTypeId = childNode.RockTypeId,
                IsActive = childNode.IsActive,
                DisplayColor = childNode.DisplayColor,
                IconUrl = childNode.IconUrl,
                ParentId = parentNode.Id
            };

            // Recursively add children to this child node
            await BuildTreeRecursivelyAsync(childTreeNode, tenantId, [..visitedNodes],
                cancellationToken);

            // Add child to parent's children list
            parentNode.Children.Add(childTreeNode);
        }
    }
}