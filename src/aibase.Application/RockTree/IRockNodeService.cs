using System.Threading.Tasks;
using Abp.Application.Services;
using aibase.RockTree.Dto;

// Ensure RockNode is accessible

namespace aibase.RockTree
{
    /// <summary>
    /// Application service for managing RockTree nodes.
    /// </summary>
    public interface IRockNodeService : IAsyncCrudAppService<RockNodeDto, int, PagedRockNodeResultRequestDto, CreateRockNodeInput, UpdateRockNodeInput>
    {
        /// <summary>
        /// Creates a new folder node.
        /// </summary>
        /// <param name="input">Input parameters for creating a folder node.</param>
        /// <returns>The created folder node.</returns>
        Task<RockNodeDto> CreateFolderNodeAsync(CreateFolderNodeInput input);

        /// <summary>
        /// Creates a new rock type node.
        /// </summary>
        /// <param name="input">Input parameters for creating a rock type node.</param>
        /// <returns>The created rock type node.</returns>
        Task<RockNodeDto> CreateRockTypeNodeAsync(CreateRockTypeNodeInput input);

        /// <summary>
        /// Clones an existing node and its children.
        /// </summary>
        /// <param name="input">Input parameters for cloning a node.</param>
        /// <returns>The new cloned node (or the top-level one if multiple).</returns>
        Task<RockNodeDto> CloneNodeAsync(CloneNodeInput input);

        /// <summary>
        /// Moves a node to a new parent or position.
        /// </summary>
        /// <param name="input">Input parameters for moving a node.</param>
        /// <returns>The moved node.</returns>
        Task<RockNodeDto> MoveNodeAsync(MoveNodeInput input);

        /// <summary>
        /// Reorders sibling nodes.
        /// </summary>
        /// <param name="input">Input parameters for reordering sibling nodes.</param>
        Task ReorderSiblingNodesAsync(ReorderSiblingsInput input);

        /// <summary>
        /// Activates a node.
        /// </summary>
        /// <param name="nodeId">The ID of the node to activate.</param>
        Task ActivateNodeAsync(int nodeId);

        /// <summary>
        /// Deactivates a node.
        /// </summary>
        /// <param name="nodeId">The ID of the node to deactivate.</param>
        Task DeactivateNodeAsync(int nodeId);
        // Or a single method: Task SetNodeActiveStatusAsync(ActivateDeactivateNodeInput input);

        /// <summary>
        /// Gets a RockNode by its ID, ensuring it belongs to the current tenant.
        /// Throws an exception if not found or tenant mismatch.
        /// </summary>
        /// <param name="rockNodeId">The ID of the RockNode.</param>
        /// <returns>The validated RockNode.</returns>
        Task<RockNode> GetAndValidateRockNodeForTenantAsync(int rockNodeId);
    }
}