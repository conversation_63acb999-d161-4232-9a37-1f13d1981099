using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Abp.Application.Services;
using aibase.RockTree.Dto;

namespace aibase.RockTree;

/// <inheritdoc />
public interface IRockTreeTraversalService : IApplicationService
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="nodeId"></param>
    /// <param name="includeInactive"></param>
    /// <returns></returns>
    Task<List<RockNodeDto>> GetDescendantsAsync(int nodeId, bool includeInactive = false);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="nodeId"></param>
    /// <param name="includeInactive"></param>
    /// <returns></returns>
    Task<List<RockNodeDto>> GetAncestorsAsync(int nodeId, bool includeInactive = false);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="nodeId"></param>
    /// <param name="rockTreeRootId"></param>
    /// <returns></returns>
    Task<RockNodePathDto> GetPathAsync(int nodeId, int? rockTreeRootId = null);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="nodeId"></param>
    /// <param name="includeInactive"></param>
    /// <returns></returns>
    Task<List<RockNodeDto>> GetChildrenAsync(int nodeId, bool includeInactive = false);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="rockTypeId"></param>
    /// <param name="rockTreeRootId"></param>
    /// <param name="includeInactive"></param>
    /// <returns></returns>
    Task<List<RockNodeDto>> GetNodesByRockTypeIdAsync(int rockTypeId, int? rockTreeRootId = null,
        bool includeInactive = false);

    /// <summary>
    /// Retrieves the complete tree structure for the current tenant.
    /// </summary>
    /// <param name="parentNodeId"></param>
    /// <param name="cancellationToken">Cancellation token for async operation.</param>
    /// <returns>A hierarchical tree structure containing all nodes for the tenant.</returns>
    Task<TreeStructureDto> GetTenantTreeStructureAsync(string parentNodeId = null,
        CancellationToken cancellationToken = default);
}