using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.RockTree.Dto;
using aibase.RockTypes;
using Microsoft.EntityFrameworkCore;

// For RockType entity
// For RockStyle entity
// For EntityNotFoundException if needed

// Added for IAbpSession

namespace aibase.RockTree;

/// <summary>
/// Service for managing Rock Nodes within a Rock Tree structure.
/// Handles CRUD operations, cloning, moving, and reordering of nodes.
/// </summary>
public class RockNodeService :
    AsyncCrudAppService<RockNode, RockNodeDto, int, PagedRockNodeResultRequestDto, CreateRockNodeInput,
        UpdateRockNodeInput>, IRockNodeService
{
    private readonly IRepository<RockNode, int> _rockNodeRepository;
    private readonly IRepository<RockNodeRelation, int> _rockNodeRelationRepository;
    private readonly IRepository<RockTreeRootNode, int> _rockTreeRootNodeRepository;
    private readonly IRepository<RockType, int> _rockTypeRepository;
    private readonly IRepository<RockTreeRoot, int> _rockTreeRootRepo;
    private readonly IAbpSession _abpSession; // Added

    /// <summary>
    /// Initializes a new instance of the <see cref="RockNodeService"/> class.
    /// </summary>
    public RockNodeService(
        IRepository<RockNode, int> repository,
        IRepository<RockNodeRelation, int> rockNodeRelationRepository,
        IRepository<RockTreeRootNode, int> rockTreeRootNodeRepository,
        IRepository<RockType, int> rockTypeRepository,
        IRepository<RockTreeRoot, int> rockTreeRootRepo,
        IAbpSession abpSession) // Added
        : base(repository)
    {
        _rockNodeRepository = repository;
        _rockNodeRelationRepository = rockNodeRelationRepository;
        _rockTreeRootNodeRepository = rockTreeRootNodeRepository;
        _rockTypeRepository = rockTypeRepository;
        _rockTreeRootRepo = rockTreeRootRepo;
        _abpSession = abpSession; // Added
    }

    private int GetCurrentTenantId()
    {
        if (!_abpSession.TenantId.HasValue)
        {
            throw new UserFriendlyException("TenantId is not available in the current session.");
        }

        return _abpSession.TenantId.Value;
    }

    /// <inheritdoc />
    protected override RockNode MapToEntity(CreateRockNodeInput createInput)
    {
        var tenantId = GetCurrentTenantId(); // Ensures TenantId is available
        var entity = base.MapToEntity(createInput);
        entity.TenantId = tenantId;
        return entity;
    }

    /// <inheritdoc />
    protected override async Task<RockNode> GetEntityByIdAsync(int id)
    {
        var entity = await base.GetEntityByIdAsync(id);
        if (entity.TenantId != _abpSession.GetTenantId()) // Use _abpSession directly
        {
            throw new EntityNotFoundException(typeof(RockNode), id);
        }

        return entity;
    }

    // UpdateAsync and DeleteAsync will use the overridden GetEntityByIdAsync for tenant check.

    /// <summary>
    /// Creates a filtered query for RockNode entities based on the input DTO.
    /// </summary>
    protected override IQueryable<RockNode> CreateFilteredQuery(PagedRockNodeResultRequestDto input)
    {
        var tenantId = GetCurrentTenantId();
        var query = base.CreateFilteredQuery(input)
            .Include(x => x.RockType)
            .Where(x => x.TenantId == tenantId) // Primary tenant filter
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(),
                x => input.Keyword != null && (x.Name.Contains(input.Keyword) ||
                                               (x.Code != null && x.Code.Contains(input.Keyword)) ||
                                               (x.Description != null && x.Description.Contains(input.Keyword))))
            .WhereIf(input.IsActive.HasValue, x => input.IsActive != null && x.IsActive == input.IsActive.Value)
            .WhereIf(input.NodeType.HasValue, x => input.NodeType != null && x.NodeType == input.NodeType.Value)
            .WhereIf(input.RockTypeId.HasValue,
                x => input.RockTypeId != null && x.RockTypeId == input.RockTypeId.Value);

        if (input.RockTreeRootId.HasValue)
        {
            // Ensure the RockTreeRoot itself belongs to the tenant
            query = from node in query
                join rootNodeMap in _rockTreeRootNodeRepository.GetAll() on node.Id equals rootNodeMap.RockNodeId
                join rockTreeRoot in _rockTreeRootRepo.GetAll() on rootNodeMap.RockTreeRootId equals rockTreeRoot.Id
                where rootNodeMap.RockTreeRootId == input.RockTreeRootId.Value && rockTreeRoot.TenantId == tenantId
                select node;
        }

        if (input.ParentNodeId.HasValue)
        {
            // Ensure the ParentNode belongs to the tenant
            query = from node in query
                join relation in _rockNodeRelationRepository.GetAll() on node.Id equals relation.ChildNodeId
                join parentNode in _rockNodeRepository.GetAll() on relation.ParentNodeId equals parentNode.Id
                where relation.ParentNodeId == input.ParentNodeId.Value && parentNode.TenantId == tenantId
                select node;
        }
        else if (!input.RockTreeRootId.HasValue)
        {
            // When neither ParentNodeId nor RockTreeRootId is specified,
            // return orphaned nodes (nodes without parent relations or tree root connections)
            query = query.Where(n => !_rockNodeRelationRepository.GetAll().Any(r => r.ChildNodeId == n.Id) &&
                                     !_rockTreeRootNodeRepository.GetAll().Any(rtn => rtn.RockNodeId == n.Id));
        }

        return query;
    }

    /// <summary>
    /// Creates a new RockNode.
    /// </summary>
    public override async Task<RockNodeDto> CreateAsync(CreateRockNodeInput input)
    {
        CheckCreatePermission();
        var tenantId = GetCurrentTenantId();

        if (input.NodeType == RockNodeType.ConcreteRockType || input.NodeType == RockNodeType.VirtualRockType)
        {
            if (!input.RockTypeId.HasValue)
                throw new UserFriendlyException(
                    "RockTypeId must be provided for ConcreteRockType or VirtualRockType nodes.");
            // Assuming RockType is global, no tenant check here. If it were tenant-specific, add tenantId to query.
            if (!await _rockTypeRepository.GetAll().AnyAsync(rt => rt.Id == input.RockTypeId.Value))
                throw new UserFriendlyException($"RockType with Id {input.RockTypeId.Value} not found.");
        }

        var rockNode = MapToEntity(input); // TenantId is set here

        await _rockNodeRepository.InsertAsync(rockNode);
        await CurrentUnitOfWork.SaveChangesAsync();

        if (input.ParentNodeId.HasValue)
        {
            var parentNode = await _rockNodeRepository.FirstOrDefaultAsync(n =>
                n.Id == input.ParentNodeId.Value && n.TenantId == tenantId);
            if (parentNode == null)
                throw new UserFriendlyException(
                    $"ParentNode with Id {input.ParentNodeId.Value} not found for the current tenant.");
            var relation = new RockNodeRelation(input.ParentNodeId.Value, rockNode.Id, input.Order ?? 0, true);
            await _rockNodeRelationRepository.InsertAsync(relation);
        }
        else if (input.RockTreeRootId is > 0)
        {
            var rockTreeRoot = await _rockTreeRootRepo.FirstOrDefaultAsync(rtr =>
                rtr.Id == input.RockTreeRootId && rtr.TenantId == tenantId);
            if (rockTreeRoot == null)
                throw new UserFriendlyException(
                    $"RockTreeRoot with Id {input.RockTreeRootId} not found for the current tenant.");
            var rootNodeMap = new RockTreeRootNode(input.RockTreeRootId.Value, rockNode.Id, input.Order ?? 0);
            await _rockTreeRootNodeRepository.InsertAsync(rootNodeMap);
        }
        // Removed requirement to have either ParentNodeId or RockTreeRootId
        // Nodes can now be created without a parent or tree root connection

        await CurrentUnitOfWork.SaveChangesAsync();
        return ObjectMapper.Map<RockNodeDto>(rockNode);
    }

    // UpdateAsync is inherited and will use tenant-aware GetEntityByIdAsync.

    /// <summary>
    /// Creates a new folder-type RockNode.
    /// </summary>
    public async Task<RockNodeDto> CreateFolderNodeAsync(CreateFolderNodeInput input)
    {
        // CreateAsync will handle tenant assignment and validation of ParentNodeId/RockTreeRootId
        var createInput = new CreateRockNodeInput
        {
            Name = input.Name,
            Description = input.Description,
            NodeType = RockNodeType.Folder,
            ParentNodeId = input.ParentNodeId,
            RockTreeRootId = input.RockTreeRootId, // Now can accept null values
            IsActive = input.IsActive,
            DisplayColor = input.DisplayColor,
            IconUrl = input.IconUrl,
            Order = input.Order
            // TenantId will be set by the overridden MapToEntity in base CreateAsync
        };
        return await CreateAsync(createInput);
    }

    /// <summary>
    /// Creates a new RockNode based on a RockType.
    /// </summary>
    public async Task<RockNodeDto> CreateRockTypeNodeAsync(CreateRockTypeNodeInput input)
    {
        var rockType = await _rockTypeRepository.GetAllIncluding(rt => rt.RockStyle)
            .FirstOrDefaultAsync(rt => rt.Id == input.RockTypeId);
        if (rockType == null)
            throw new UserFriendlyException($"RockType with Id {input.RockTypeId} not found.");

        var createInput = new CreateRockNodeInput
        {
            Name = input.Name,
            Code = input.Code,
            Description = input.Description,
            NodeType = RockNodeType.ConcreteRockType,
            RockTypeId = input.RockTypeId,
            ParentNodeId = input.ParentNodeId,
            RockTreeRootId = input.RockTreeRootId, // Now can accept null values
            IsActive = input.IsActive,
            DisplayColor = input.DisplayColor ?? rockType.RockStyle?.FillColor,
            IconUrl = input.IconUrl,
            Order = input.Order
            // TenantId will be set by the overridden MapToEntity in base CreateAsync
        };
        return await CreateAsync(createInput);
    }

    /// <summary>
    /// Clones an existing RockNode, optionally including its descendants.
    /// </summary>
    public async Task<RockNodeDto> CloneNodeAsync(CloneNodeInput input)
    {
        var tenantId = GetCurrentTenantId();
        var nodeToClone = await _rockNodeRepository.GetAllIncluding(n => n.RockType.RockStyle)
            .FirstOrDefaultAsync(n => n.Id == input.NodeIdToClone && n.TenantId == tenantId);
        if (nodeToClone == null)
            throw new UserFriendlyException(
                $"Node with Id {input.NodeIdToClone} not found for the current tenant.");

        if (input.TargetRockTreeRootId.HasValue)
        {
            var targetRoot = await _rockTreeRootRepo.FirstOrDefaultAsync(rtr =>
                rtr.Id == input.TargetRockTreeRootId.Value && rtr.TenantId == tenantId);
            if (targetRoot == null)
                throw new UserFriendlyException(
                    $"TargetRockTreeRoot with Id {input.TargetRockTreeRootId.Value} not found for the current tenant.");
        }

        if (input.NewParentNodeId.HasValue)
        {
            if (input.NewParentNodeId.Value == input.NodeIdToClone)
                throw new UserFriendlyException("Cannot clone a node under itself.");
            var newParent = await _rockNodeRepository.FirstOrDefaultAsync(n =>
                n.Id == input.NewParentNodeId.Value && n.TenantId == tenantId);
            if (newParent == null)
                throw new UserFriendlyException(
                    $"New parent node with Id {input.NewParentNodeId.Value} not found for the current tenant.");
        }

        var newClonedNode = new RockNode
        {
            Name = input.NewName ?? nodeToClone.Name,
            Code = nodeToClone.Code,
            Description = nodeToClone.Description,
            NodeType = nodeToClone.NodeType,
            RockTypeId = nodeToClone.RockTypeId,
            IsActive = nodeToClone.IsActive,
            DisplayColor = nodeToClone.DisplayColor,
            IconUrl = nodeToClone.IconUrl,
            TenantId = tenantId // Set TenantId for the new clone
        };

        await _rockNodeRepository.InsertAsync(newClonedNode);
        await CurrentUnitOfWork.SaveChangesAsync();

        int? actualTargetRockTreeRootId = input.TargetRockTreeRootId;

        if (input.NewParentNodeId.HasValue)
        {
            if (!actualTargetRockTreeRootId.HasValue)
            {
                actualTargetRockTreeRootId =
                    await GetRootIdForNodeAsync(input.NewParentNodeId.Value, tenantId); // Pass tenantId
                if (!actualTargetRockTreeRootId.HasValue)
                    throw new UserFriendlyException(
                        $"Could not determine RockTreeRoot for the new parent node {input.NewParentNodeId.Value} in the current tenant.");
            }

            var newRelation = new RockNodeRelation(input.NewParentNodeId.Value, newClonedNode.Id, 0, true);
            await _rockNodeRelationRepository.InsertAsync(newRelation);
        }
        else if
            (actualTargetRockTreeRootId.HasValue) // This implies input.TargetRockTreeRootId was set and validated
        {
            var newRootNodeMap = new RockTreeRootNode(actualTargetRockTreeRootId.Value, newClonedNode.Id, 0);
            await _rockTreeRootNodeRepository.InsertAsync(newRootNodeMap);
        }
        else // No new parent, no target root specified, try to use original node's root if it's in the same tenant
        {
            var originalRootInfo = await _rockTreeRootNodeRepository.GetAllIncluding(rnm => rnm.RockTreeRoot)
                .FirstOrDefaultAsync(rnm =>
                    rnm.RockNodeId == nodeToClone.Id && rnm.RockTreeRoot.TenantId == tenantId);
            if (originalRootInfo != null)
            {
                var newRootNodeMap = new RockTreeRootNode(originalRootInfo.RockTreeRootId, newClonedNode.Id, 0);
                await _rockTreeRootNodeRepository.InsertAsync(newRootNodeMap);
                actualTargetRockTreeRootId = originalRootInfo.RockTreeRootId;
            }
        }

        if (input.IncludeDescendants &&
            actualTargetRockTreeRootId.HasValue) // actualTargetRockTreeRootId must be valid for the tenant
        {
            await CloneDescendantsAsync(nodeToClone.Id, newClonedNode.Id,
                new Dictionary<int, int>(), tenantId);
        }

        await CurrentUnitOfWork.SaveChangesAsync();
        return ObjectMapper.Map<RockNodeDto>(newClonedNode);
    }

    /// <summary>
    /// Recursively clones descendant nodes.
    /// </summary>
    private async Task CloneDescendantsAsync(int originalParentId, int newClonedParentId,
        Dictionary<int, int> oldToNewIdMap, int tenantId)
    {
        // originalParentId is already confirmed to be in tenant by CloneNodeAsync
        var childRelations =
            await _rockNodeRelationRepository.GetAllListAsync(r => r.ParentNodeId == originalParentId);
        // We need to ensure originalChildNode is also from the same tenant.
        // Since originalParentId is from the tenant, its direct children (via relation) should also be.
        var originalChildNodeIds = childRelations.Select(cr => cr.ChildNodeId).ToList();
        var children =
            await _rockNodeRepository.GetAllListAsync(n =>
                originalChildNodeIds.Contains(n.Id) && n.TenantId == tenantId);


        foreach (var originalChildNode in children)
        {
            var clonedChildNode = new RockNode
            {
                Name = originalChildNode.Name,
                Code = originalChildNode.Code,
                Description = originalChildNode.Description,
                NodeType = originalChildNode.NodeType,
                RockTypeId = originalChildNode.RockTypeId,
                IsActive = originalChildNode.IsActive,
                DisplayColor = originalChildNode.DisplayColor,
                IconUrl = originalChildNode.IconUrl,
                TenantId = tenantId // Assign tenantId
            };

            await _rockNodeRepository.InsertAsync(clonedChildNode);
            // SaveChanges might be needed here if ID is used before loop ends, but relations are created after this.

            var originalRelation = childRelations.First(r => r.ChildNodeId == originalChildNode.Id);
            var newChildRelation = new RockNodeRelation(newClonedParentId, clonedChildNode.Id,
                originalRelation.DisplayOrder, originalRelation.IsPrimary);
            await _rockNodeRelationRepository.InsertAsync(newChildRelation);

            oldToNewIdMap[originalChildNode.Id] = clonedChildNode.Id;

            // No need to check NodeType for Folder explicitly, as GetChildrenAsync (if used) or direct relation query will handle it.
            // The recursive call will fetch children of originalChildNode.
            await CloneDescendantsAsync(originalChildNode.Id, clonedChildNode.Id,
                oldToNewIdMap, tenantId);
        }
    }

    /// <summary>
    /// Gets the RockTreeRoot ID for a given node by traversing up its parentage within the current tenant.
    /// </summary>
    private async Task<int?> GetRootIdForNodeAsync(int nodeId, int tenantId)
    {
        // Ensure the starting node belongs to the tenant
        var startNode =
            await _rockNodeRepository.FirstOrDefaultAsync(n => n.Id == nodeId && n.TenantId == tenantId);
        if (startNode == null) return null; // Node not found in tenant

        var rootMap = await _rockTreeRootNodeRepository.GetAllIncluding(rnm => rnm.RockTreeRoot)
            .FirstOrDefaultAsync(rnm => rnm.RockNodeId == nodeId && rnm.RockTreeRoot.TenantId == tenantId);
        if (rootMap != null)
        {
            return rootMap.RockTreeRootId;
        }

        var currentNodeId = nodeId;
        var visited = new HashSet<int>();

        while (true)
        {
            if (!visited.Add(currentNodeId)) return null;

            var parentRelation =
                await _rockNodeRelationRepository.FirstOrDefaultAsync(r =>
                    r.ChildNodeId == currentNodeId && r.IsPrimary);
            if (parentRelation == null) return null;

            // Ensure parent node is in the same tenant
            var parentNode = await _rockNodeRepository.FirstOrDefaultAsync(n =>
                n.Id == parentRelation.ParentNodeId && n.TenantId == tenantId);
            if (parentNode == null) return null; // Parent not in tenant or not found

            var parentIsRoot = await _rockTreeRootNodeRepository.GetAllIncluding(rnm => rnm.RockTreeRoot)
                .FirstOrDefaultAsync(rnm =>
                    rnm.RockNodeId == parentRelation.ParentNodeId && rnm.RockTreeRoot.TenantId == tenantId);
            if (parentIsRoot != null)
            {
                return parentIsRoot.RockTreeRootId;
            }

            currentNodeId = parentRelation.ParentNodeId;
        }
    }

    /// <summary>
    /// Moves a RockNode to a new parent within the same tenant.
    /// </summary>
    public async Task<RockNodeDto> MoveNodeAsync(MoveNodeInput input)
    {
        var tenantId = GetCurrentTenantId();
        var nodeToMove =
            await _rockNodeRepository.FirstOrDefaultAsync(n =>
                n.Id == input.NodeIdToMove && n.TenantId == tenantId);
        if (nodeToMove == null)
            throw new EntityNotFoundException(typeof(RockNode), input.NodeIdToMove);

        // Remove old parent relation
        var oldRelation =
            await _rockNodeRelationRepository.FirstOrDefaultAsync(r =>
                r.ChildNodeId == input.NodeIdToMove && r.IsPrimary);
        if (oldRelation != null)
        {
            await _rockNodeRelationRepository.DeleteAsync(oldRelation);
        }

        // Remove old root mapping
        var oldRootMap =
            await _rockTreeRootNodeRepository.FirstOrDefaultAsync(rnm => rnm.RockNodeId == input.NodeIdToMove);
        if (oldRootMap != null)
        {
            var rootOfOldMap = await _rockTreeRootRepo.FirstOrDefaultAsync(rtr =>
                rtr.Id == oldRootMap.RockTreeRootId && rtr.TenantId == tenantId);
            if (rootOfOldMap != null) // Only delete if the root map points to a root of the current tenant
            {
                await _rockTreeRootNodeRepository.DeleteAsync(oldRootMap);
            }
        }

        if (input.NewParentNodeId.HasValue)
        {
            var newParentNodeIdValue = input.NewParentNodeId.Value;
            // Ensure the new parent node exists and belongs to the current tenant
            var newParentNode =
                await _rockNodeRepository.FirstOrDefaultAsync(n =>
                    n.Id == newParentNodeIdValue && n.TenantId == tenantId);
            if (newParentNode == null)
                throw new EntityNotFoundException(typeof(RockNode), newParentNodeIdValue); // Corrected constructor

            // Prevent moving a node under itself
            if (input.NodeIdToMove == newParentNodeIdValue)
                throw new UserFriendlyException("Cannot move a node under itself.");

            // Prevent creating a circular dependency by moving a node under one of its own descendants
            var isDescendant = await IsDescendantAsync(newParentNodeIdValue, input.NodeIdToMove, new HashSet<int>(),
                tenantId);
            if (isDescendant)
                throw new UserFriendlyException(
                    "Cannot move a node under one of its own descendants (creates a cycle).");

            // Add new relation to the new parent
            // If NewOrder is null, calculate order to append. Otherwise, use the specified NewOrder.
            int newOrder = input.NewOrder ??
                           (await _rockNodeRelationRepository.CountAsync(
                               r => r.ParentNodeId == newParentNodeIdValue));
            var newRelation =
                new RockNodeRelation(newParentNodeIdValue, input.NodeIdToMove, newOrder,
                    true); // Assuming new relation is primary
            await _rockNodeRelationRepository.InsertAsync(newRelation);
        }
        // else: input.NewParentNodeId is null.
        // The node is being moved to be a root node (i.e., its ParentId becomes null).
        // This is achieved by not creating a new RockNodeRelation for it as a child.
        // The old relations and root mappings have already been removed.
        // The 'NewOrder' from the input is not directly applied in this branch, as it would typically
        // relate to ordering within a parent's children or as a root node within a specific RockTreeRoot.
        // Handling association with a specific RockTreeRoot (if NewRockTreeRootId were provided) is beyond
        // the scope of this specific modification which focuses on NewParentNodeId being null.

        await CurrentUnitOfWork.SaveChangesAsync(); // Consolidate SaveChanges to a single call
        return ObjectMapper.Map<RockNodeDto>(nodeToMove);
    }

    /// <summary>
    /// Checks if a node is a descendant of another node within the same tenant.
    /// </summary>
    private async Task<bool> IsDescendantAsync(int potentialDescendantId, int ancestorId, HashSet<int> visited,
        int tenantId)
    {
        if (potentialDescendantId == ancestorId) return false;
        if (!visited.Add(ancestorId)) return false;

        // Ensure ancestorId node is part of the tenant before querying its children
        var ancestorNode =
            await _rockNodeRepository.FirstOrDefaultAsync(n => n.Id == ancestorId && n.TenantId == tenantId);
        if (ancestorNode == null) return false; // Ancestor not in tenant, so descendant cannot be through it.

        var childrenRelations =
            await _rockNodeRelationRepository.GetAllListAsync(r => r.ParentNodeId == ancestorId && r.IsPrimary);
        foreach (var relation in childrenRelations)
        {
            // Ensure child node is part of the tenant
            var childNode =
                await _rockNodeRepository.FirstOrDefaultAsync(n =>
                    n.Id == relation.ChildNodeId && n.TenantId == tenantId);
            if (childNode == null) continue; // Child not in tenant

            if (relation.ChildNodeId == potentialDescendantId) return true;
            if (await IsDescendantAsync(potentialDescendantId, relation.ChildNodeId, visited, tenantId))
                return true;
        }

        return false;
    }

    /// <summary>
    /// Reorders sibling nodes under a common parent or root, ensuring tenant consistency.
    /// </summary>
    public async Task ReorderSiblingNodesAsync(ReorderSiblingsInput input)
    {
        var tenantId = GetCurrentTenantId();
        if (input.OrderedNodeIds.Count == 0) return;

        var isRootNodeReorder = await _rockTreeRootRepo.GetAll()
            .AnyAsync(rtr => rtr.Id == input.ParentNodeId && rtr.TenantId == tenantId);

        if (isRootNodeReorder) // ParentNodeId is actually RockTreeRootId here
        {
            var rootNodesMaps = await _rockTreeRootNodeRepository.GetAllIncluding(rnm => rnm.RockNode)
                .Where(rnm => rnm.RockTreeRootId == input.ParentNodeId && rnm.RockNode.TenantId == tenantId)
                .ToListAsync();

            if (rootNodesMaps.Count != input.OrderedNodeIds.Count ||
                !input.OrderedNodeIds.All(id => rootNodesMaps.Any(rn => rn.RockNodeId == id)) ||
                !rootNodesMaps.All(rn => input.OrderedNodeIds.Contains(rn.RockNodeId)))
            {
                throw new UserFriendlyException(
                    "OrderedNodeIds list must exactly match all current root nodes for the given RockTreeRootId in the current tenant.");
            }

            for (int i = 0; i < input.OrderedNodeIds.Count; i++)
            {
                var nodeId = input.OrderedNodeIds[i];
                var rootNodeMap = rootNodesMaps.First(rnm => rnm.RockNodeId == nodeId);
                rootNodeMap.DisplayOrder = i;
                await _rockTreeRootNodeRepository.UpdateAsync(rootNodeMap);
            }
        }
        else
        {
            var parentNode =
                await _rockNodeRepository.FirstOrDefaultAsync(n =>
                    n.Id == input.ParentNodeId && n.TenantId == tenantId);
            if (parentNode == null)
                throw new UserFriendlyException(
                    $"Parent node with Id {input.ParentNodeId} not found in current tenant.");

            var childRelations = await _rockNodeRelationRepository.GetAllIncluding(r => r.ChildNode)
                .Where(r => r.ParentNodeId == input.ParentNodeId && r.IsPrimary && r.ChildNode.TenantId == tenantId)
                .ToListAsync();

            if (childRelations.Count != input.OrderedNodeIds.Count ||
                !input.OrderedNodeIds.All(id => childRelations.Any(cr => cr.ChildNodeId == id)) ||
                !childRelations.All(cr => input.OrderedNodeIds.Contains(cr.ChildNodeId)))
            {
                throw new UserFriendlyException(
                    "OrderedNodeIds list must exactly match all current child nodes for the given ParentNodeId in the current tenant.");
            }

            for (int i = 0; i < input.OrderedNodeIds.Count; i++)
            {
                var nodeId = input.OrderedNodeIds[i];
                var relation = childRelations.First(r => r.ChildNodeId == nodeId);
                relation.DisplayOrder = i;
                await _rockNodeRelationRepository.UpdateAsync(relation);
            }
        }

        await CurrentUnitOfWork.SaveChangesAsync();
    }

    /// <summary>
    /// Activates a RockNode. Ensures node belongs to current tenant.
    /// </summary>
    public async Task ActivateNodeAsync(int nodeId)
    {
        var node = await GetEntityByIdAsync(nodeId); // Tenant check is here
        node.IsActive = true;
        await _rockNodeRepository.UpdateAsync(node);
    }

    /// <summary>
    /// Deactivates a RockNode. Ensures node belongs to current tenant.
    /// </summary>
    public async Task DeactivateNodeAsync(int nodeId)
    {
        var node = await GetEntityByIdAsync(nodeId); // Tenant check is here
        node.IsActive = false;
        await _rockNodeRepository.UpdateAsync(node);
    }

    /// <inheritdoc />
    public async Task<RockNode> GetAndValidateRockNodeForTenantAsync(int rockNodeId)
    {
        if (!AbpSession.TenantId.HasValue)
        {
            // Or handle as per your application's policy for host users accessing tenant data
            throw new UserFriendlyException("Tenant context is required.");
        }

        var rockNode = await _rockNodeRepository.FirstOrDefaultAsync(rockNodeId);

        if (rockNode == null)
        {
            throw new UserFriendlyException($"RockNode with ID {rockNodeId} not found.");
        }

        if (rockNode.TenantId != AbpSession.TenantId.Value)
        {
            // Log this attempt for security audit if necessary
            throw new UserFriendlyException(
                $"Access denied. RockNode with ID {rockNodeId} does not belong to your tenant.");
        }

        return rockNode;
    }
}