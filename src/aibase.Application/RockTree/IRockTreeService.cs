using System.Threading.Tasks;
using Abp.Application.Services;
using aibase.RockTree.Dto;

namespace aibase.RockTree;

/// <inheritdoc />
public interface IRockTreeService : IAsyncCrudAppService<RockTreeRootDto, int, PagedRockTreeRootResultRequestDto,
    CreateRockTreeRootInput, UpdateRockTreeRootInput>
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task AssociateGeologySuiteAsync(AssociateGeologySuiteInput input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task DisassociateGeologySuiteAsync(DisassociateGeologySuiteInput input);
}