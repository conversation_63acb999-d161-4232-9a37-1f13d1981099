﻿using Abp.AutoMapper;
using Abp.Modules;
using Abp.Reflection.Extensions;
using aibase.Authorization;

namespace aibase
{
    /// <inheritdoc />
    [DependsOn(
        typeof(aibaseCoreModule), 
        typeof(AbpAutoMapperModule))]
    public class aibaseApplicationModule : AbpModule
    {
        /// <inheritdoc />
        public override void PreInitialize()
        {
            Configuration.Authorization.Providers.Add<aibaseAuthorizationProvider>();
        }

        /// <inheritdoc />
        public override void Initialize()
        {
            var thisAssembly = typeof(aibaseApplicationModule).GetAssembly();

            IocManager.RegisterAssemblyByConvention(thisAssembly);

            // Register IMailer service
            IocManager.Register<AiBase.Services.IMailer, AiBase.Services.Mailer>();

            // Register RockNodeValueRepository
            //IocManager.Register<IRockNodeValueRepository, RockNodeValueRepository>(DependencyLifeStyle.Transient);

            Configuration.Modules.AbpAutoMapper().Configurators.Add(
                // Scan the assembly for classes which inherit from AutoMapper.Profile
                cfg => cfg.AddMaps(thisAssembly)
            );
        }
    }
}
