using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.ImageSubtypes.Dto;
using aibase.ImageTypes;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.ImageSubtypes.Services;

/// <inheritdoc />
public class ImageSubtypeService : IImageSubtypeService
{
    private readonly IRepository<ImageSubtype, int> _repository;
    private readonly IRepository<ImageType, int> _imageTypeRepository;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;
    private readonly IUnitOfWorkManager _unitOfWorkManager;

    /// <summary>
    /// Constructor
    /// </summary>
    public ImageSubtypeService(
        IRepository<ImageSubtype, int> repository,
        IRepository<ImageType, int> imageTypeRepository,
        IAbpSession abpSession,
        IMapper mapper,
        IUnitOfWorkManager unitOfWorkManager)
    {
        _repository = repository;
        _imageTypeRepository = imageTypeRepository;
        _abpSession = abpSession;
        _mapper = mapper;
        _unitOfWorkManager = unitOfWorkManager;
    }

    /// <inheritdoc />
    public async Task<ImageSubtype> CreateAsync(CreateImageSubtypeDto input, bool returnExist = false)
    {
        var tenantId = _abpSession.GetTenantId();

        var imageType = await _imageTypeRepository.FirstOrDefaultAsync(x =>
            x.Id == input.ImageTypeId && x.TenantId == tenantId);
        if (imageType == null)
        {
            throw new UserFriendlyException($"ImageType with Id {input.ImageTypeId} not found.");
        }

        var existingImageSubtype = await _repository
            .FirstOrDefaultAsync(x => x.Name == input.Name &&
                                      x.ImageTypeId == input.ImageTypeId &&
                                      x.TenantId == tenantId);

        if (existingImageSubtype != null)
        {
            if (returnExist)
            {
                return existingImageSubtype;
            }

            throw new UserFriendlyException(
                $"An Image Subtype with the name {existingImageSubtype.Name} already exists for this Image Type.");
        }

        var imageSubtype = new ImageSubtype
        {
            Name = input.Name,
            ImageTypeId = input.ImageTypeId,
            IsActive = input.IsActive,
            IsWet = input.IsWet,
            IsDry = input.IsDry,
            IsUv = input.IsUv,
            Sequence = input.Sequence,
            TenantId = tenantId,
        };
        
        await ValidateSubtypeDryWetAsync(imageSubtype.IsWet, imageSubtype.IsDry, imageSubtype.IsUv, imageSubtype.ImageTypeId, tenantId);

        await _repository.InsertAsync(imageSubtype);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        return imageSubtype;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<ImageSubtypeDto>> GetAllAsync(PagedImageSubtypeResultRequestDto input)
    {
        var query = _repository.GetAllIncluding(x => x.ImageType)
            .AsNoTracking()
            .Where(x => x.TenantId == _abpSession.GetTenantId());

        if (!string.IsNullOrWhiteSpace(input.Keyword))
        {
            query = query.Where(x => x.Name.Contains(input.Keyword));
        }

        if (input.ImageTypeId.HasValue)
        {
            query = query.Where(x => x.ImageTypeId == input.ImageTypeId.Value);
        }
        
        query = query.OrderBy(x => x.Sequence);

        var totalCount = await query.CountAsync();
        var imageSubtypes = await query
            .OrderBy(x => x.ImageType.Name)
            .ThenBy(x => x.Name)
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        return new PagedResultDto<ImageSubtypeDto>(totalCount, _mapper.Map<List<ImageSubtypeDto>>(imageSubtypes));
    }

    /// <inheritdoc />
    public async Task<ImageSubtypeDto> UpdateAsync(UpdateImageSubtypeDto input)
    {
        var imageSubtype = await ValidateImageSubtypeEntity(input.Id);

        // Check for duplicate name within same ImageType (if name is being changed)
        if (!string.IsNullOrEmpty(input.Name) && input.Name != imageSubtype.Name)
        {
            var existingSubtype = await _repository.FirstOrDefaultAsync(x => 
                x.ImageTypeId == imageSubtype.ImageTypeId && 
                x.Name.ToLower() == input.Name.ToLower() && 
                x.TenantId == imageSubtype.TenantId &&
                x.Id != input.Id);
            
            if (existingSubtype != null)
            {
                throw new UserFriendlyException($"ImageSubtype with name '{input.Name}' already exists for this ImageType.");
            }
        }
        
        imageSubtype.Name = input.Name ?? imageSubtype.Name;
        imageSubtype.IsActive = input.IsActive ?? imageSubtype.IsActive;
        imageSubtype.IsWet = input.IsWet ?? imageSubtype.IsWet;
        imageSubtype.IsDry = input.IsDry ?? imageSubtype.IsDry;
        imageSubtype.IsUv = input.IsUv ?? imageSubtype.IsUv;
        imageSubtype.Sequence = input.Sequence ?? imageSubtype.Sequence;
        
        await ValidateSubtypeDryWetAsync(imageSubtype.IsWet, imageSubtype.IsDry, imageSubtype.IsUv, imageSubtype.Id, _abpSession.GetTenantId(), imageSubtype.Id);

        await _repository.UpdateAsync(imageSubtype);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        return _mapper.Map<ImageSubtypeDto>(imageSubtype);
    }

    /// <inheritdoc />
    public async Task<ImageSubtypeDto> GetAsync(EntityDto<int> input)
    {
        var imageSubtype = await ValidateImageSubtypeEntity(input.Id);
        return _mapper.Map<ImageSubtypeDto>(imageSubtype);
    }

    private async Task<ImageSubtype> ValidateImageSubtypeEntity(int id)
    {
        var imageSubtype = await _repository.GetAllIncluding(x => x.ImageType)
            .FirstOrDefaultAsync(x => x.Id == id && x.TenantId == _abpSession.GetTenantId());

        if (imageSubtype == null)
        {
            throw new EntityNotFoundException(typeof(ImageSubtype), id);
        }

        return imageSubtype;
    }
    
    private async Task SetAllOtherSubtypesAsNonWetAsync(int imageTypeId, int tenantId, int? excludeId = null)
    {
        var query = _repository.GetAll()
            .Where(x => x.ImageTypeId == imageTypeId && x.TenantId == tenantId && x.IsWet);
        
        if (excludeId.HasValue)
        {
            query = query.Where(x => x.Id != excludeId.Value);
        }

        var wetRecords = await query.ToListAsync();

        foreach (var record in wetRecords)
        {
            record.IsWet = false;
        }
    }
    
    private async Task SetAllOtherSubtypesAsNonDryAsync(int imageTypeId, int tenantId, int? excludeId = null)
    {
        var query = _repository.GetAll()
            .Where(x => x.ImageTypeId == imageTypeId && x.TenantId == tenantId && x.IsDry);
        
        if (excludeId.HasValue)
        {
            query = query.Where(x => x.Id != excludeId.Value);
        }

        var dryRecords = await query.ToListAsync();

        foreach (var record in dryRecords)
        {
            record.IsDry = false;
        }
    }
    
    private async Task SetAllOtherSubtypesAsNonUvAsync(int imageTypeId, int tenantId, int? excludeId = null)
    {
        var query = _repository.GetAll()
            .Where(x => x.ImageTypeId == imageTypeId && x.TenantId == tenantId && x.IsUv);
        
        if (excludeId.HasValue)
        {
            query = query.Where(x => x.Id != excludeId.Value);
        }

        var dryRecords = await query.ToListAsync();

        foreach (var record in dryRecords)
        {
            record.IsUv = false;
        }
    }

    private async Task ValidateSubtypeDryWetAsync(bool isWet, bool isDry, bool isUv, int imageTypeId, int tenantId, int? excludeId = null)
    {
        if (isWet && isDry)
        {
            throw new UserFriendlyException("ImageSubtype cannot be both Wet and Dry.");
        }
        
        if (isWet && isUv)
        {
            throw new UserFriendlyException("ImageSubtype cannot be both Wet and UV.");
        }
        
        if (isDry && isUv)
        {
            throw new UserFriendlyException("ImageSubtype cannot be both Dry and UV.");
        }
        
        if (isDry && isUv && isWet)
        {
            throw new UserFriendlyException("ImageSubtype cannot be both Wet, Dry and UV.");
        }
        
        if (isWet)
        {
            await SetAllOtherSubtypesAsNonWetAsync(imageTypeId, tenantId, excludeId);
        }

        if (isDry)
        {
            await SetAllOtherSubtypesAsNonDryAsync(imageTypeId, tenantId, excludeId);
        }
        
        if (isUv)
        {
            await SetAllOtherSubtypesAsNonUvAsync(imageTypeId, tenantId, excludeId);
        }
    }
}