using System.ComponentModel.DataAnnotations;

namespace aibase.ImageSubtypes.Dto;

/// <summary>
/// Used to create a new ImageSubtype
/// </summary>
public class CreateImageSubtypeDto
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int ImageTypeId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// 
    /// </summary>
    public bool IsWet { get; set; } = false;

    /// <summary>
    /// 
    /// </summary>
    public bool IsDry { get; set; } = false;
    
    /// <summary>
    /// 
    /// </summary>
    public bool IsUv { get; set; } = false;
    
    /// <summary>
    /// 
    /// </summary>
    public int Sequence { get; set; }
}