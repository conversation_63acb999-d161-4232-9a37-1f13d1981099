using Abp.Application.Services.Dto;
using Abp.AutoMapper;

namespace aibase.ImageSubtypes.Dto;

/// <inheritdoc />
[AutoMap(typeof(ImageSubtype))]
public class UpdateImageSubtypeDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? ImageTypeId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool? IsWet { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool? IsDry { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public bool? IsUv { get; set; } = false;
    
    /// <summary>
    /// 
    /// </summary>
    public int? Sequence { get; set; }
}