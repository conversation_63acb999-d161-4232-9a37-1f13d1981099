using Abp.Application.Services.Dto;
using AutoMapper;

namespace aibase.ImageSubtypes.Dto;

/// <inheritdoc />
[AutoMap(typeof(ImageSubtype))]
public class ImageSubtypeDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public int ImageTypeId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool IsWet { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool IsDry { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public bool IsUv { get; set; } = false;
    
    /// <summary>
    /// 
    /// </summary>
    public int Sequence { get; set; }
}