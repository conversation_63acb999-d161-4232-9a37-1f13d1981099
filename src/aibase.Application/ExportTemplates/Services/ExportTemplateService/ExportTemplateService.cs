using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore.Repositories;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.DrillHoles.Dto;
using aibase.ExportEvents;
using aibase.ExportTemplateDrillHoles;
using aibase.ExportTemplateImageSubtypes;
using aibase.ExportTemplateImageTypes;
using aibase.ExportTemplates.Dto;
using aibase.ImageEntity;
using aibase.ImageSubtypes;
using aibase.ImageSubtypes.Dto;
using aibase.ImageTypes;
using aibase.ImageTypes.Dto;
using aibase.Projects.Dto;
using aibase.Prospects.Dto;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.ExportTemplates.Services.ExportTemplateService;

/// <inheritdoc />
public class ExportTemplateService : IExportTemplateService
{
    private readonly IRepository<ExportTemplate, int> _repository;
    private readonly IRepository<ExportEvent, int> _exportEventRepository;
    private readonly IRepository<ExportTemplateImageType, int> _exportTemplateImageTypeRepository;
    private readonly IRepository<ExportTemplateImageSubtype, int> _exportTemplateImageSubtypeRepository;
    private readonly IRepository<ExportTemplateDrillHole, int> _exportTemplateDrillHoleRepository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;


    /// <summary>
    /// 
    /// </summary>
    public ExportTemplateService(
        IRepository<ExportTemplate, int> repository,
        IRepository<ExportEvent, int> exportEventRepository,
        IRepository<ExportTemplateImageType, int> exportTemplateImageTypeRepository,
        IRepository<ExportTemplateImageSubtype, int> exportTemplateImageSubtypeRepository,
        IRepository<ExportTemplateDrillHole, int> exportTemplateDrillHoleRepository,
        IUnitOfWorkManager unitOfWorkManager,
        IAbpSession abpSession,
        IMapper mapper
        )
    {
        _repository = repository;
        _exportEventRepository = exportEventRepository;
        _exportTemplateImageTypeRepository = exportTemplateImageTypeRepository;
        _exportTemplateImageSubtypeRepository = exportTemplateImageSubtypeRepository;
        _exportTemplateDrillHoleRepository = exportTemplateDrillHoleRepository;
        _unitOfWorkManager = unitOfWorkManager;
        _abpSession = abpSession;
        _mapper = mapper;
    }

    /// <inheritdoc />
    public async Task<ExportTemplate> CreateAsync(CreateExportTemplateDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var exportTemplate = new ExportTemplate()
        {
            Name = input.Name,
            Description = input.Description,
            ProjectId = input.ProjectId,
            ProspectId = input.ProspectId,
            DrillholeIds = input.DrillholeIds,
            DrillHoleStatus = input.DrillHoleStatus,
            UpdateStatus = input.UpdateStatus,
            DataSource = input.DataSource,
            ImageSizes = input.ImageSize,
            ImageCategories = input.ImageCategory,
            StandardType = input.StandardType,
            ImageTypes = input.ImageTypes,
            DataOutput = input.DataOutput,
            TenantId = _abpSession.GetTenantId(),
        };
        await _repository.InsertAsync(exportTemplate);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        if (input.ImageTypeIds is { Count: > 0 })
        {
            var assignExportTemplateImageTypeDto = new AssignExportTemplateImageTypeDto
            {
                ExportTemplateId = exportTemplate.Id,
                ImageTypeIds = input.ImageTypeIds
            };
            await AssignExportTemplateImageTypeAsync(assignExportTemplateImageTypeDto);
        }

        if (input.ImageSubtypeIds is { Count: > 0 })
        {
            var assignExportTemplateImageSubtypeDto = new AssignExportTemplateImageSubtypeDto
            {
                ExportTemplateId = exportTemplate.Id,
                ImageSubtypeIds = input.ImageSubtypeIds
            };
            await AssignExportTemplateImageSubtypeAsync(assignExportTemplateImageSubtypeDto);
        }

        if (input.DrillholeIds is { Count: > 0 })
        {
            var assignExportTemplateDrillHoleDto = new AssignExportTemplateDrillHoleDto
            {
                ExportTemplateId = exportTemplate.Id,
                DrillHoleIds = input.DrillholeIds
            };
            await AssignExportTemplateDrillHoleAsync(assignExportTemplateDrillHoleDto);
        }

        return exportTemplate;
    }

    /// <inheritdoc />
    public async Task<ExportTemplateDto> UpdateAsync(UpdateExportTemplateDto input)
    {
        var exportTemplate = await ValidateExportTemplateEntity(input.Id);

        exportTemplate.Name = input.Name ?? exportTemplate.Name;
        exportTemplate.Description = input.Description ?? exportTemplate.Description;
        exportTemplate.ProjectId = input.ProjectId ?? exportTemplate.ProjectId;
        exportTemplate.ProspectId = input.ProspectId ?? exportTemplate.ProspectId;
        exportTemplate.DrillholeIds = input.DrillholeIds ?? exportTemplate.DrillholeIds;
        exportTemplate.DrillHoleStatus = input.DrillHoleStatus ?? exportTemplate.DrillHoleStatus;
        exportTemplate.UpdateStatus = input.UpdateStatus ?? exportTemplate.UpdateStatus;
        exportTemplate.DataSource = input.DataSource ?? exportTemplate.DataSource;
        exportTemplate.ImageSizes = input.ImageSize ?? exportTemplate.ImageSizes;
        exportTemplate.ImageCategories = input.ImageCategory ?? exportTemplate.ImageCategories;
        exportTemplate.StandardType = input.StandardType ?? exportTemplate.StandardType;
        exportTemplate.ImageTypes = input.ImageTypes ?? exportTemplate.ImageTypes;
        exportTemplate.DataOutput = input.DataOutput ?? exportTemplate.DataOutput;

        if (input.ImageTypeIds != null)
        {
            var assignExportTemplateImageTypeDto = new AssignExportTemplateImageTypeDto
            {
                ExportTemplateId = exportTemplate.Id,
                ImageTypeIds = input.ImageTypeIds
            };
            await AssignExportTemplateImageTypeAsync(assignExportTemplateImageTypeDto);
        }


        if (input.ImageSubtypeIds != null)
        {
            var assignExportTemplateImageSubtypeDto = new AssignExportTemplateImageSubtypeDto
            {
                ExportTemplateId = exportTemplate.Id,
                ImageSubtypeIds = input.ImageSubtypeIds
            };
            await AssignExportTemplateImageSubtypeAsync(assignExportTemplateImageSubtypeDto);
        }

        if (input.DrillholeIds != null)
        {
            var assignExportTemplateDrillHoleDto = new AssignExportTemplateDrillHoleDto
            {
                ExportTemplateId = exportTemplate.Id,
                DrillHoleIds = input.DrillholeIds
            };
            await AssignExportTemplateDrillHoleAsync(assignExportTemplateDrillHoleDto);
        }

        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<ExportTemplateDto>> GetAllAsync(PagedExportTemplateResultRequestDto input)
    {
        var query = _repository.GetAllIncluding(x => x.Project, x => x.Prospect)
            .Include(x => x.ExportTemplateDrillHoles)
            .ThenInclude(x => x.DrillHole)
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .OrderByDescending(r => r.CreationTime);

        var totalCount = await query.CountAsync();

        var exportTemplates = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        var exportTemplateDto = exportTemplates.Select(x => new ExportTemplateDto
        {
            Id = x.Id,
            Name = x.Name,
            Description = x.Description,
            ProjectId = x.ProjectId,
            Project = _mapper.Map<ProjectDto>(x.Project),
            ProspectId = x.ProspectId,
            Prospect = _mapper.Map<ProspectDto>(x.Prospect),
            DataSource = x.DataSource,
            ImageSize = x.ImageSizes,
            ImageCategory = x.ImageCategories,
            StandardType = x.StandardType,
            ImageTypes = x.ImageTypes,
            DataOutput = x.DataOutput,
            DrillholeIds = x.DrillholeIds,
            DrillHoles = _mapper.Map<List<DrillHoleDto>>(x.ExportTemplateDrillHoles.Select(d => d.DrillHole).ToList()),
            Url = x.Url,
            FileSize = x.FileSize
        }).ToList();

        return new PagedResultDto<ExportTemplateDto>(totalCount, exportTemplateDto);
    }

    /// <inheritdoc />
    public async Task<ExportTemplateDto> GetAsync(EntityDto<int> input)
    {
        var exportTemplate = await _repository.GetAllIncluding(x => x.Project, x => x.Prospect)
            .Include(x => x.ExportTemplateDrillHoles)
            .ThenInclude(x => x.DrillHole)
            .Include(x => x.ExportTemplateImageTypes)
            .ThenInclude(x => x.ImageType)
            .Include(x => x.ExportTemplateImageSubtypes)
            .ThenInclude(x => x.ImageSubtype)
            .FirstOrDefaultAsync(x => x.Id == input.Id);

        if (exportTemplate == null)
        {
            throw new EntityNotFoundException(typeof(ExportTemplate), input.Id);
        }

        var exportTemplateDto = _mapper.Map<ExportTemplateDto>(exportTemplate);
        exportTemplateDto.ImageSize = exportTemplate.ImageSizes;
        exportTemplateDto.ImageCategory = exportTemplate.ImageCategories;
        exportTemplateDto.ListImageType =
            _mapper.Map<List<ImageTypeDto>>(exportTemplate.ExportTemplateImageTypes.Select(x => x.ImageType).ToList());
        exportTemplateDto.ImageSubtypes =
            _mapper.Map<List<ImageSubtypeDto>>(exportTemplate.ExportTemplateImageSubtypes.Select(x => x.ImageSubtype)
                .ToList());
        exportTemplateDto.DrillHoles =
            _mapper.Map<List<DrillHoleDto>>(exportTemplate.ExportTemplateDrillHoles.Select(x => x.DrillHole).ToList());
        return exportTemplateDto;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<ExportEventDto>> GetListEventByExportTemplateAsync(
        PagedExportEventResultRequestDto input)
    {
        var query = _exportEventRepository.GetAllIncluding(x => x.ExportTemplate)
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(input.ExportTemplateId.HasValue, x => x.ExportTemplateId == input.ExportTemplateId)
            .OrderByDescending(r => r.Id);

        var totalCount = await query.CountAsync();

        var exportEvents = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        var exportEventDto = _mapper.Map<List<ExportEventDto>>(exportEvents);

        return new PagedResultDto<ExportEventDto>(totalCount, exportEventDto);
    }

    /// <inheritdoc />
    public async Task CancelProcessExportAsync(EntityDto<int> input)
    {
        var exportEvent = await _exportEventRepository.FirstOrDefaultAsync(x => x.Id == input.Id);
        if (exportEvent == null)
        {
            throw new EntityNotFoundException(typeof(ExportEvent), input.Id);
        }

        if (exportEvent.Status == EventStatus.Running)
        {
            exportEvent.Status = EventStatus.Canceled;
        }

        await _exportEventRepository.UpdateAsync(exportEvent);
    }

    /// <inheritdoc />
    public async Task MockDataAsync()
    {
        using (_unitOfWorkManager.Current.DisableFilter(AbpDataFilters.MayHaveTenant))
        {
            var templateExports = await _repository.GetAll()
                .AsNoTracking()
                .ToListAsync();

            var drillHoleIds = templateExports.Where(x => x.DrillholeIds?.Count != null).SelectMany(x => x.DrillholeIds)
                .Distinct().ToList();

            var exportTemplateDrillHoles = templateExports
                .SelectMany(template =>
                    drillHoleIds
                        .Where(drillHole => template.DrillholeIds != null && template.DrillholeIds.Contains(drillHole))
                        .Select(drillHole => new ExportTemplateDrillHole
                        {
                            ExportTemplateId = template.Id,
                            DrillHoleId = drillHole,
                            TenantId = template.TenantId
                        }))
                .ToList();

            await _exportTemplateDrillHoleRepository.InsertRangeAsync(exportTemplateDrillHoles);
        }
    }

    private async Task<ExportTemplate> ValidateExportTemplateEntity(int id)
    {
        var exportTemplate = await _repository.GetAllIncluding(x => x.Project, x => x.Prospect)
            .FirstOrDefaultAsync(x => x.Id == id);

        if (exportTemplate == null)
        {
            throw new EntityNotFoundException(typeof(ExportTemplate), id);
        }

        return exportTemplate;
    }

    private async Task AssignExportTemplateImageTypeAsync(AssignExportTemplateImageTypeDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var tenantId = _abpSession.GetTenantId();

        var existingImageTypes = await _exportTemplateImageTypeRepository.GetAllListAsync(x =>
            x.ExportTemplateId == input.ExportTemplateId && x.TenantId == tenantId);

        var existingImageTypesIds = existingImageTypes.Select(x => x.ImageTypeId).ToList();

        var imageTypesToAdd = input.ImageTypeIds.Except(existingImageTypesIds).ToList();
        var imageTypesToDelete = existingImageTypesIds.Except(input.ImageTypeIds).ToList();

        foreach (var exportTemplateImageType in imageTypesToAdd.Select(imageTypeId =>
                     new ExportTemplateImageType
                     {
                         ExportTemplateId = input.ExportTemplateId,
                         ImageTypeId = imageTypeId,
                         TenantId = tenantId,
                     }))
        {
            await _exportTemplateImageTypeRepository.InsertAsync(exportTemplateImageType);
        }

        foreach (var imageTypeId in imageTypesToDelete)
        {
            await _exportTemplateImageTypeRepository.DeleteAsync(x =>
                x.ExportTemplateId == input.ExportTemplateId && x.ImageTypeId == imageTypeId);
        }
    }

    private async Task AssignExportTemplateImageSubtypeAsync(AssignExportTemplateImageSubtypeDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var tenantId = _abpSession.GetTenantId();

        var existingImageSubtypes = await _exportTemplateImageSubtypeRepository.GetAllListAsync(x =>
            x.ExportTemplateId == input.ExportTemplateId && x.TenantId == tenantId);

        var existingImageSubtypesIds = existingImageSubtypes.Select(x => x.ImageSubtypeId).ToList();

        var imageSubtypesToAdd = input.ImageSubtypeIds.Except(existingImageSubtypesIds).ToList();
        var imageSubtypesToDelete = existingImageSubtypesIds.Except(input.ImageSubtypeIds).ToList();

        foreach (var exportTemplateImageType in imageSubtypesToAdd.Select(imageSubtypeId =>
                     new ExportTemplateImageSubtype
                     {
                         ExportTemplateId = input.ExportTemplateId,
                         ImageSubtypeId = imageSubtypeId,
                         TenantId = tenantId,
                     }))
        {
            await _exportTemplateImageSubtypeRepository.InsertAsync(exportTemplateImageType);
        }

        foreach (var imageSubtypeId in imageSubtypesToDelete)
        {
            await _exportTemplateImageSubtypeRepository.DeleteAsync(x =>
                x.ExportTemplateId == input.ExportTemplateId && x.ImageSubtypeId == imageSubtypeId);
        }
    }

    private async Task AssignExportTemplateDrillHoleAsync(AssignExportTemplateDrillHoleDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var tenantId = _abpSession.GetTenantId();

        var existingDrillHoles = await _exportTemplateDrillHoleRepository.GetAllListAsync(x =>
            x.ExportTemplateId == input.ExportTemplateId && x.TenantId == tenantId);

        var existingDrillHolesIds = existingDrillHoles.Select(x => x.DrillHoleId).ToList();

        var drillHolesToAdd = input.DrillHoleIds.Except(existingDrillHolesIds).ToList();
        var drillHolesToDelete = existingDrillHolesIds.Except(input.DrillHoleIds).ToList();

        foreach (var exportTemplateImageType in drillHolesToAdd.Select(drillHoleId =>
                     new ExportTemplateDrillHole
                     {
                         ExportTemplateId = input.ExportTemplateId,
                         DrillHoleId = drillHoleId,
                         TenantId = tenantId,
                     }))
        {
            await _exportTemplateDrillHoleRepository.InsertAsync(exportTemplateImageType);
        }

        foreach (var drillHoleId in drillHolesToDelete)
        {
            await _exportTemplateDrillHoleRepository.DeleteAsync(x =>
                x.ExportTemplateId == input.ExportTemplateId && x.DrillHoleId == drillHoleId);
        }
    }
}