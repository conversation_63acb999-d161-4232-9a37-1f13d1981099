using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Abp.BackgroundJobs;
using Abp.Dependency;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using aibase.DownholeDatas;
using aibase.DrillHoleEntity;
using aibase.ExportEvents;
using aibase.ExportTemplates.Dto;
using aibase.ImageEntity;
using aibase.Images.Services.AzureService;
using aibase.Images.Services.UploadService.Handler;
using aibase.Jobs.Services.Socket;
using aibase.ProjectEntity;
using aibase.Prospects;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using OfficeOpenXml;

namespace aibase.ExportTemplates.Services.ExecuteService;

/// <summary>
/// 
/// </summary>
public class ExportService : AsyncBackgroundJob<ExecuteExportDto>, ITransientDependency
{
    private readonly IRepository<Project, int> _projectRepository;
    private readonly IRepository<Prospect, int> _prospectRepository;
    private readonly IRepository<DrillHole, int> _drillHoleRepository;
    private readonly IRepository<Image, int> _imageRepository;
    private readonly IRepository<ExportTemplate, int> _exportTemplateRepository;
    private readonly IRepository<ExportEvent, int> _exportEventRepository;
    private readonly IRepository<DownholeData, int> _downHoleRepository;
    private readonly IHubContext<SocketWorkflowJob> _hubContext;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAzureService _azureService;
    private readonly IHttpClientFactory _httpClientFactory;

    /// <inheritdoc />
    public ExportService(
        IRepository<Project, int> projectRepository,
        IRepository<Prospect, int> prospectRepository,
        IRepository<DrillHole, int> drillHoleRepository,
        IRepository<Image, int> imageRepository,
        IRepository<ExportTemplate, int> exportTemplateRepository,
        IRepository<ExportEvent, int> exportEventRepository,
        IUnitOfWorkManager unitOfWorkManager,
        IHubContext<SocketWorkflowJob> hubContext,
        IAzureService azureService,
        IRepository<DownholeData, int> downHoleRepository,
        IHttpClientFactory httpClientFactory)
    {
        _projectRepository = projectRepository;
        _prospectRepository = prospectRepository;
        _drillHoleRepository = drillHoleRepository;
        _imageRepository = imageRepository;
        _exportTemplateRepository = exportTemplateRepository;
        _hubContext = hubContext;
        _unitOfWorkManager = unitOfWorkManager;
        _exportEventRepository = exportEventRepository;
        _azureService = azureService;
        _downHoleRepository = downHoleRepository;
        _httpClientFactory = httpClientFactory;
    }

    /// <inheritdoc />
    public override async Task ExecuteAsync(ExecuteExportDto input)
    {
        try
        {
            using var transaction = _unitOfWorkManager.Begin();

            var exportTemplate = await _exportTemplateRepository.GetAll()
                .Include(x => x.ExportTemplateImageTypes)
                .ThenInclude(x => x.ImageType)
                .Include(x => x.ExportTemplateImageSubtypes)
                .ThenInclude(x => x.ImageSubtype)
                .FirstOrDefaultAsync(x => x.Id == input.ExportTemplatetId);
            var exportEvent = await _exportEventRepository.FirstOrDefaultAsync(x => x.Id == input.ExportEventId);

            try
            {
                (string, double) zipFilePath;
                if (input.DataSource == DataSourceExport.GeophysicData)
                {
                    zipFilePath = await ExportFileAsync(exportTemplate);
                }
                else
                {
                    zipFilePath = await ExportImageAsync(exportTemplate);
                }

                var exportEventCheck = await _exportEventRepository.GetAll()
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == input.ExportEventId);
                if (exportEventCheck is { Status: EventStatus.Running })
                {
                    exportTemplate.Url = zipFilePath.Item1;
                    exportTemplate.FileSize = zipFilePath.Item2;

                    exportEvent.Status = EventStatus.Completed;
                }
            }
            catch (Exception e)
            {
                exportEvent.Status = EventStatus.Failed;

                Logger.Error(e.Message);
                throw;
            }

            await _exportTemplateRepository.UpdateAsync(exportTemplate);
            await _exportEventRepository.UpdateAsync(exportEvent);

            var jsonSerializerSettings = new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver(),
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore
            };
            await _hubContext.Clients.Group(exportEvent.Id.ToString())
                .SendAsync("ExportJobSocket",
                    JsonConvert.SerializeObject(exportEvent, jsonSerializerSettings));

            await transaction.CompleteAsync();
        }
        catch (Exception ex)
        {
            Logger.Error($"Execute export template {input.ExportTemplatetId} failed: {ex.Message}");
        }
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="exportTemplate"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task<(string, double)> ExportImageAsync(ExportTemplate exportTemplate,
        CancellationToken cancellationToken = default)
    {
        var imageTypes = exportTemplate.ExportTemplateImageTypes.Select(x => x.ImageType).ToList();
        var imageSubtypes = exportTemplate.ExportTemplateImageSubtypes.Select(x => x.ImageSubtype).ToList();

        // Validate project
        var project = await _projectRepository.GetAll()
                          .AsNoTracking()
                          .FirstOrDefaultAsync(x => x.Id == exportTemplate.ProjectId, cancellationToken)
                      ?? throw new EntityNotFoundException(typeof(Project), exportTemplate.ProjectId);

        // Filtered prospects
        var prospects = await _prospectRepository.GetAll()
            .AsNoTracking()
            .Where(p => exportTemplate.ProspectId == null
                ? p.ProjectId == project.Id
                : p.Id == exportTemplate.ProspectId)
            .ToListAsync(cancellationToken);
        var prospectIds = prospects.Select(p => p.Id).ToList();

        // Filtered drill holes
        var drillHolesQuery = _drillHoleRepository
            .GetAllIncluding(dh => dh.Prospect)
            .Where(dh => exportTemplate.DrillholeIds == null
                ? dh.ProjectId == project.Id && prospectIds.Contains(dh.ProspectId)
                : exportTemplate.DrillholeIds.Contains(dh.Id));
        if (exportTemplate.DrillHoleStatus.HasValue && exportTemplate.DrillHoleStatus != DrillHoleStatusExport.All)
        {
            drillHolesQuery = drillHolesQuery.Where(dh =>
                dh.DrillHoleStatus == (DrillHoleStatus?)exportTemplate.DrillHoleStatus);
        }

        var drillHoles = await drillHolesQuery.ToListAsync(cancellationToken);
        var drillHoleIds = drillHoles.Select(dh => dh.Id).ToList();

        // Get images with optimized query
        var imagesQuery = _imageRepository
            .GetAllIncluding(x => x.Files, x => x.CroppedImages, x => x.ImageType, x => x.ImageSubtype)
            .AsNoTracking()
            .Where(i => drillHoleIds.Contains(i.DrillHoleId));

        if (imageTypes.Count > 0)
        {
            imagesQuery = imagesQuery.Where(x => x.ImageType != null && imageTypes.Contains(x.ImageType));
        }

        if (imageSubtypes.Count > 0)
        {
            imagesQuery = imagesQuery.Where(x => x.ImageSubtype != null && imageSubtypes.Contains(x.ImageSubtype));
        }

        var images = await imagesQuery.ToListAsync(cancellationToken);
        var allCrops = (exportTemplate.ImageTypes.Contains(ImageTypeExport.CroppedBox) ||
                        exportTemplate.ImageTypes.Contains(ImageTypeExport.CroppedRow))
            ? images.SelectMany(x => x.CroppedImages).ToList()
            : [];

        var urls = new List<(string Url, string Path)>();

        foreach (var drillHole in drillHoles)
        {
            var drillHoleImages = images.Where(i => i.DrillHoleId == drillHole.Id).ToList();
            foreach (var image in drillHoleImages)
            {
                var imageFiles = image.Files.ToList();

                var imageType = image.ImageType?.Name ?? "";
                var imageSubtype = image.ImageSubtype?.Name ?? "";

                if (exportTemplate.ImageTypes.Contains(ImageTypeExport.CroppedBox) ||
                    exportTemplate.ImageTypes.Contains(ImageTypeExport.CroppedRow))
                {
                    var imageCrops = allCrops.Where(x => x.ImageId == image.Id).ToList();

                    if (exportTemplate.ImageTypes.Contains(ImageTypeExport.CroppedBox))
                    {
                        urls.AddRange(imageCrops.Where(x => x.Type == ImageConstSettings.ImageCropBox).Select(crop =>
                            (crop.UrlCroppedImage,
                                BuildImagePath(project.Name, drillHole.Prospect.Name, drillHole.Name, image,
                                    imageType,
                                    imageSubtype, "croppedBox",
                                    exportTemplate.ImageSizes?.Contains(ImageSize.FullSize) ?? false
                                        ? crop.UrlCroppedImage
                                        : crop.MediumSize))));
                    }

                    if (exportTemplate.ImageTypes.Contains(ImageTypeExport.CroppedRow))
                    {
                        urls.AddRange(imageCrops.Where(x => x.Type == ImageConstSettings.ImageCropRow).Select(crop =>
                            (crop.UrlCroppedImage,
                                BuildImagePath(project.Name, drillHole.Prospect.Name, drillHole.Name, image,
                                    imageType,
                                    imageSubtype, "croppedRow",
                                    exportTemplate.ImageSizes?.Contains(ImageSize.FullSize) ?? false
                                        ? crop.UrlCroppedImage
                                        : crop.MediumSize))));
                    }
                }

                if (exportTemplate.ImageTypes.Contains(ImageTypeExport.Original) ||
                    exportTemplate.ImageTypes.Count == 0)
                {
                    if (exportTemplate.ImageSizes?.Contains(ImageSize.Medium) ?? false)
                    {
                        var mediumFile = imageFiles.FirstOrDefault(x => x.Size == ImageConstSettings.ImageMediumSize);
                        if (mediumFile != null)
                        {
                            urls.Add((mediumFile.Url,
                                BuildImagePath(project.Name, drillHole.Prospect.Name, drillHole.Name,
                                    image, imageType, imageSubtype, ImageConstSettings.ImageMediumSize,
                                    mediumFile.Url)));
                        }
                    }

                    if (exportTemplate.ImageSizes?.Contains(ImageSize.FullSize) ?? false)
                    {
                        var fullSizeFile = imageFiles.FirstOrDefault(x => x.Size == ImageConstSettings.ImageFullSize);
                        if (fullSizeFile != null)
                        {
                            urls.Add((fullSizeFile.Url,
                                BuildImagePath(project.Name, drillHole.Prospect.Name, drillHole.Name,
                                    image, imageType, imageType, "full", fullSizeFile.Url)));
                        }
                    }
                }
            }
        }

        var exportPath = Path.Combine(Path.GetTempPath(), $"ExportImage_{Guid.NewGuid()}.zip");

        using var httpClient = _httpClientFactory.CreateClient();
        // httpClient.Timeout = TimeSpan.FromMinutes(30);
        var createdFolders = new HashSet<string>();

        using (var zip = ZipFile.Open(exportPath, ZipArchiveMode.Create))
        {
            foreach (var (url, path) in urls)
            {
                try
                {
                    var folderPath = Path.GetDirectoryName(path) ?? "";
                    // Create folder entry if it doesn't exist
                    if (!createdFolders.Contains(folderPath))
                    {
                        zip.CreateEntry(folderPath + "/");
                        createdFolders.Add(folderPath);
                    }


                    var client = new HttpClient
                    {
                        Timeout = TimeSpan.FromMinutes(2)
                    };
                    var imageBytes = await client.GetByteArrayAsync(url, cancellationToken);
                    Console.WriteLine($"Get image from: {url}");
                    var zipEntry = zip.CreateEntry(path);
                    await using var entryStream = zipEntry.Open();
                    await entryStream.WriteAsync(imageBytes, cancellationToken);
                }
                catch (Exception e)
                {
                    Logger.Error(e.Message);
                    Console.WriteLine($"Error get image from: {url}");
                }
            }
        }

        // Upload to Azure
        await using var fileStream = File.OpenRead(exportPath);
        var fileSize = fileStream.Length;
        var uploadedUrl = await _azureService.UploadFileAsync(
            fileStream,
            $"TempZip/{Path.GetFileName(exportPath)}",
            true);

        // Update drill hole statuses
        foreach (var drillHole in drillHoles)
        {
            drillHole.DrillHoleStatus = exportTemplate?.UpdateStatus ?? DrillHoleStatus.Exported;
            drillHole.IsExport = true;
            await _drillHoleRepository.UpdateAsync(drillHole);
        }

        return (uploadedUrl, fileSize);
    }

    private static string BuildImagePath(string projectName, string prospectName, string drillHoleName,
        Image image, string imageType, string standardType, string size, string url)
    {
        var depthPart = $"{image.DepthFrom:F2}_{image.DepthTo:F2}";
        var standardTypePath = string.IsNullOrEmpty(standardType) ? "" : $"_{standardType}";
        var standardTypeFolderName = $"{imageType}{standardTypePath}";
        var extension = Path.GetExtension(url);
        var fileName = $"{drillHoleName}_{depthPart}{standardTypePath}_{size}{extension}";

        return Path.Combine(
            projectName,
            prospectName,
            drillHoleName,
            standardTypeFolderName,
            fileName
        );
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="exportTemplate"></param>
    /// <returns></returns>
    public async Task<(string, double)> ExportFileAsync(ExportTemplate exportTemplate)
    {
        var downholeRawQuery = _downHoleRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.ProjectId == exportTemplate.ProjectId)
            .OrderBy(r => r.CreationTime)
            .Select(x => new
            {
                x.AttributeName,
                x.AttributeValue,
                x.GroupId,
                x.DrillHole
            });
        var downholeRaw = await downholeRawQuery.ToListAsync();

        var groupedData = downholeRaw
            .GroupBy(d => d.GroupId)
            .Select(g =>
            {
                var dict = g.ToDictionary(
                    d => d.AttributeName,
                    d => d.AttributeValue);

                var newDict = new Dictionary<string, string>
                {
                    { "DrillHole", g.First().DrillHole ?? string.Empty }
                };
                foreach (var kvp in dict)
                {
                    newDict[kvp.Key] = kvp.Value;
                }

                return newDict;
            })
            .ToList();

        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

        using var package = new ExcelPackage();
        var worksheet = package.Workbook.Worksheets.Add("Downhole Data");

        var headers = groupedData.First().Keys.ToList();
        for (var i = 0; i < headers.Count; i++)
        {
            worksheet.Cells[1, i + 1].Value = headers[i];
        }

        for (var row = 0; row < groupedData.Count; row++)
        {
            var dataRow = groupedData[row];
            for (var col = 0; col < headers.Count; col++)
            {
                worksheet.Cells[row + 2, col + 1].Value = dataRow[headers[col]];
            }
        }

        var excelData = await package.GetAsByteArrayAsync();

        string uploadedUrl;
        double fileSize;
        await using (var fileStream = new MemoryStream(excelData))
        {
            fileSize = fileStream.Length;
            uploadedUrl =
                await _azureService.UploadFileAsync(fileStream, $"DownholeExport/{DateTime.Now.Date:dd-MM-yyyy}.xlsx",
                    true);
        }

        return (uploadedUrl, fileSize);
    }
}