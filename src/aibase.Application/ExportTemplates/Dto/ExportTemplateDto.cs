using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Abp.Application.Services.Dto;
using aibase.DrillHoleEntity;
using aibase.DrillHoles.Dto;
using aibase.ImageEntity;
using aibase.ImageSubtypes.Dto;
using aibase.ImageTypes.Dto;
using aibase.Projects.Dto;
using aibase.Prospects.Dto;
using AutoMapper;

namespace aibase.ExportTemplates.Dto;

/// <inheritdoc />
[AutoMap(typeof(ExportTemplate))]
public class ExportTemplateDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int ProjectId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public ProjectDto Project { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? ProspectId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public ProspectDto? Prospect { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<int>? DrillholeIds { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<DrillHoleDto> DrillHoles { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    public DrillHoleStatusExport? DrillHoleStatus { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public DrillHoleStatus? UpdateStatus { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public DataSourceExport DataSource { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<ImageSize> ImageSize { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    public List<ImageCategoryNew> ImageCategory { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    public StandardTypeExport? StandardType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<ImageTypeExport> ImageTypes { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public DataOutput DataOutput { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string CreatedBy { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public string? Url { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double? FileSize { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<ImageTypeDto> ListImageType { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    public List<ImageSubtypeDto> ImageSubtypes { get; set; } = [];
}