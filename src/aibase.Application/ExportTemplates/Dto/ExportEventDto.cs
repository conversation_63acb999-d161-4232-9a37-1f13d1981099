using System;
using System.ComponentModel.DataAnnotations;
using Abp.Application.Services.Dto;
using aibase.ExportEvents;
using AutoMapper;

namespace aibase.ExportTemplates.Dto;

/// <inheritdoc />
[AutoMap(typeof(ExportEvent))]
public class ExportEventDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public EventStatus Status { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int ExportTemplateId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public ExportTemplate ExportTemplate { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string UserName { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public DateTime CreationTime { get; set; }
}