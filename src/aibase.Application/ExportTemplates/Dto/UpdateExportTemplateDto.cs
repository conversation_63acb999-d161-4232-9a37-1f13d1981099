using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Abp.Application.Services.Dto;
using aibase.DrillHoleEntity;
using aibase.ImageEntity;
using aibase.ProjectEntity;
using AutoMapper;

namespace aibase.ExportTemplates.Dto;

/// <inheritdoc />
[AutoMap(typeof(Project))]
public class UpdateExportTemplateDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? ProjectId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? ProspectId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<int>? DrillholeIds { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public DrillHoleStatusExport? DrillHoleStatus { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public DrillHoleStatus? UpdateStatus { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public DataSourceExport? DataSource { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<ImageSize>? ImageSize { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<ImageCategoryNew>? ImageCategory { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public StandardTypeExport? StandardType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<ImageTypeExport>? ImageTypes { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public DataOutput? DataOutput { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<int>? ImageTypeIds { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    public List<int>? ImageSubtypeIds { get; set; } = [];
}