﻿using Abp.Application.Services.Dto;
using aibase.ProjectEntity;
using AutoMapper;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace aibase.Projects.Dto
{
    /// <inheritdoc />
    [AutoMap(typeof(Project))]
    public class UpdateProjectDto : EntityDto
    {
        /// <summary>
        /// 
        /// </summary>
        [MaxLength(3)]
        public string? Code { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public string? Name { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public double? CoreTrayLength { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public string? Description { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public string? BackgroundColor { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public string? TextColor { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public string? LoggingTextColor { get; set; } = "#FFFFFF";
        
        /// <summary>
        /// 
        /// </summary>
        public List<int>? BoundingBoxId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public List<int>? BoundingRowsId { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public bool? IsActive { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public int? MobileWorkflowId { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public List<int>? LoggingViewIds { get; set; } = [];
        
        /// <summary>
        /// 
        /// </summary>
        public List<int>? SuiteIds { get; set; } = [];
        
        /// <summary>
        /// 
        /// </summary>
        public List<int>? GeologySuiteIds { get; set; } = [];
        
        /// <summary>
        /// 
        /// </summary>
        public List<int>? AssaySuiteIds { get; set; } = [];
        
        /// <summary>
        /// 
        /// </summary>
        public List<int>? GeotechSuiteIds { get; set; } = [];
        
        /// <summary>
        /// 
        /// </summary>
        public List<int>? RqdCalculationIds { get; set; } = [];
        
        /// <summary>
        /// 
        /// </summary>
        public List<int>? ImageTypeIds { get; set; } = [];
        
        /// <summary>
        /// 
        /// </summary>
        public int? RockGroupId { get; set; }

        /// <summary>
        /// Mobile profile ID
        /// </summary>
        public int? MobileProfileId { get; set; }
    }
}
