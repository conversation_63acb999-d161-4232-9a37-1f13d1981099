﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace aibase.Projects.Dto;

/// <summary>
/// 
/// </summary>
public class CreateProjectDto
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [Required]
    [MaxLength(3)]
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public double CoreTrayLength { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public string BackgroundColor { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public string TextColor { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public string LoggingTextColor { get; set; } = "#FFFFFF";

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public List<int> BoundingBoxId { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public List<int> BoundingRowsId { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public bool IsActive { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? MobileWorkflowId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<int>? LoggingViewIds { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    public List<int>? SuiteIds { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    public List<int>? GeologySuiteIds { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    public List<int>? AssaySuiteIds { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    public List<int>? GeotechSuiteIds { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    public List<int>? RqdCalculationIds { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    public List<int>? ImageTypeIds { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    public int? RockGroupId { get; set; }

    /// <summary>
    /// Mobile profile ID
    /// </summary>
    public int? MobileProfileId { get; set; }
}
