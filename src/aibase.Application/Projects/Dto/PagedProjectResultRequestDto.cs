﻿using Abp.Application.Services.Dto;

namespace aibase.Projects.Dto;

/// <inheritdoc />
public class PagedProjectResultRequestDto : PagedResultRequestDto
{
    /// <summary>
    /// 
    /// </summary>
    public string? Keyword { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public bool? IsActive { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public string? SortField { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public string? SortOrder { get; set; }
}