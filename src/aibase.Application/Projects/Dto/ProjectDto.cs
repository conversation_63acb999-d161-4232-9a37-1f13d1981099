﻿using Abp.Application.Services.Dto;
using aibase.ProjectEntity;
using AutoMapper;
using System.Collections.Generic;
using aibase.AssaySuites.Dto;
using aibase.GeologySuites.Dto;
using aibase.GeotechSuites.Dto;
using aibase.ImageTypes.Dto;
using aibase.LoggingViews;
using aibase.MobileProfiles.Dto;
using aibase.Polygons.Dto;
using aibase.RockGroups.Dto;
using aibase.RqdCalculations.Dto;
using aibase.Suites.Dto;
using aibase.Workflows.Dto;

namespace aibase.Projects.Dto;

/// <inheritdoc />
[AutoMap(typeof(Project))]
public class ProjectDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public string Name { get; set; } = string.Empty;
        
    /// <summary>
    /// 
    /// </summary>
    public string Code { get; set; } = string.Empty;
        
    /// <summary>
    /// 
    /// </summary>
    public double CoreTrayLength { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public string Description { get; set; } = string.Empty;
        
    /// <summary>
    /// 
    /// </summary>
    public string BackgroundColor { get; set; } = string.Empty;
        
    /// <summary>
    /// 
    /// </summary>
    public string TextColor { get; set; } = string.Empty;
        
    /// <summary>
    /// 
    /// </summary>
    public string LoggingTextColor { get; set; } = "#FFFFFF";
        
    /// <summary>
    /// 
    /// </summary>
    public string BoundingBoxIds { get; set; } = string.Empty;
        
    /// <summary>
    /// 
    /// </summary>
    public List<PolygonDto> BoundingBoxs { get; set; } = [];
        
    /// <summary>
    /// 
    /// </summary>
    public List<int> BoundingBoxIdArr { get; set; } = [];
        
    /// <summary>
    /// 
    /// </summary>
    public string BoundingRowsIds { get; set; } = string.Empty;
        
    /// <summary>
    /// 
    /// </summary>
    public List<int> BoundingRowsIdAr { get; set; } = [];
        
    /// <summary>
    /// 
    /// </summary>
    public List<PolygonDto> BoundingRows { get; set; } = [];
        
    /// <summary>
    /// 
    /// </summary>
    public List<GeologySuiteDto>? GeologySuites { get; set; } = [];
        
    /// <summary>
    /// 
    /// </summary>
    public List<SuiteDto>? GeophysicsSuites { get; set; } = [];
        
    /// <summary>
    /// 
    /// </summary>
    public List<AssaySuiteDto>? AssaySuites { get; set; } = [];
        
    /// <summary>
    /// 
    /// </summary>
    public bool IsActive { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public WorkflowDto? Workflow { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public List<LoggingView>? LoggingViews { get; set; } = [];
        
    /// <summary>
    /// 
    /// </summary>
    public List<GeotechSuiteDto>? GeotechSuites { get; set; } = [];
        
    /// <summary>
    /// 
    /// </summary>
    public List<RqdCalculationDto>? RqdCalculations { get; set; } = [];
        
    /// <summary>
    /// 
    /// </summary>
    public List<ImageTypeDto>? ImageTypes { get; set; } = [];
        
    /// <summary>
    /// 
    /// </summary>
    public RockGroupDto? RockGroup { get; set; }
        
    /// <summary>
    /// Mobile profile name
    /// </summary>
    public MobileProfileDto? MobileProfile { get; set; }
}
