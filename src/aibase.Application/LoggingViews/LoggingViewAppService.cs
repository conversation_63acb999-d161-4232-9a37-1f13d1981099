using System.Threading.Tasks;
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using aibase.LoggingViews.Dto;
using aibase.LoggingViews.Services;

namespace aibase.LoggingViews;

/// <summary>
/// 
/// </summary>
[AbpAuthorize]
public class LoggingViewAppService : AsyncCrudAppService<LoggingView, LoggingViewDto, int,
    PagedLoggingViewResultRequestDto,
    CreateLoggingViewDto, UpdateLoggingViewDto>, ILoggingViewAppService
{
    private readonly ILoggingViewService _loggingViewService;

    /// <inheritdoc />
    public LoggingViewAppService(
        IRepository<LoggingView, int> repository,
        ILoggingViewService loggingViewService) : base(repository)
    {
        _loggingViewService = loggingViewService;
    }

    /// <inheritdoc />
    public override async Task<LoggingViewDto> CreateAsync(CreateLoggingViewDto input)
    {
        var loggingView = await _loggingViewService.CreateAsync(input);
        return MapToEntityDto(loggingView);
    }

    /// <inheritdoc />
    public override async Task<LoggingViewDto> UpdateAsync(UpdateLoggingViewDto input)
    {
        return await _loggingViewService.UpdateAsync(input);
    }

    /// <inheritdoc />
    public async Task<string> ExportDataAsync(ExportDataDto input)
    {
        return await _loggingViewService.ExportDataAsync(input);
    }

    /// <inheritdoc />
    public override async Task<LoggingViewDto> GetAsync(EntityDto<int> input)
    {
        return await _loggingViewService.GetAsync(input);
    }

    /// <inheritdoc />
    public override async Task<PagedResultDto<LoggingViewDto>> GetAllAsync(PagedLoggingViewResultRequestDto input)
    {
        return await _loggingViewService.GetAllAsync(input);
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<DrillholeLoggingDto>> GetDrillholeLoggingAsync(GetDrillholeLoggingRequestDto input)
    {
        return await _loggingViewService.GetDrillholeLoggingAsync(input);
    }
}