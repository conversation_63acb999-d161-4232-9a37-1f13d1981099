using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Threading.Tasks;
using OfficeOpenXml;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.AssayDatas;
using aibase.AssayProjectSuites;
using aibase.AssaySuites;
using aibase.DataEntries;
using aibase.DataEntries.Dto;
using aibase.DataEntries.Services;
using aibase.DownholeDatas;
using aibase.DrillHoleEntity;
using aibase.GeologyFields;
using aibase.GeologySuiteFields;
using aibase.GeologySuites;
using aibase.GeotechDatas;
using aibase.GeotechSuites;
using aibase.Images.Services.AzureService;
using aibase.LoggingViewColumns;
using aibase.LoggingViews.Dto;
using aibase.ProjectEntity;
using aibase.ProjectLoggingViews;
using aibase.Projects.Dto;
using aibase.ProjectSuites;
using aibase.Prospects.Dto;
using aibase.RecoveryResults;
using aibase.RqdCalculations;
using aibase.RqdPercentResults;
using aibase.Suites;
using aibase.TrayDepthResults;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.LoggingViews.Services;

/// <inheritdoc />
public class LoggingViewService : ILoggingViewService
{
    private readonly IRepository<LoggingView, int> _repository;
    private readonly IRepository<LoggingViewColumn, int> _loggingViewColumnRepository;
    private readonly IRepository<ProjectLoggingView, int> _projectLoggingViewRepository;
    private readonly IRepository<Project, int> _projectRepository;
    private readonly IRepository<RecoveryResult, int> _recoveryResultRepository;
    private readonly IRepository<RqdCalculation, int> _rqdCalculationRepository;
    private readonly IRepository<RqdCalculationResult, int> _rqdCalculationResultRepository;
    private readonly IRepository<GeologySuite, int> _geologySuiteRepository;
    private readonly IRepository<DataEntry, int> _dataEntryRepository;
    private readonly IRepository<DrillHole, int> _drillholeRepository;
    private readonly IRepository<AssayData, int> _assayDataRepository;
    private readonly IRepository<AssayProjectSuite, int> _assayProjectSuiteRepository;
    private readonly IRepository<AssaySuite, int> _assaySuiteRepository;
    private readonly IRepository<DownholeData, int> _downholeDataRepository;
    private readonly IRepository<ProjectSuite, int> _projectSuiteRepository;
    private readonly IRepository<DrillHole, int> _drillHoleRepository;
    private readonly IRepository<GeotechData, int> _geotechDataRepository;
    private readonly IRepository<GeotechSuite, int> _geotechSuiteRepository;
    private readonly IRepository<Suite, int> _suiteRepository;
    private readonly IRepository<TrayDepthResult, int> _trayDepthResultRepository;
    private readonly IRepository<GeologySuiteField, int> _geologyFieldRepository;
    private readonly IRepository<RqdPercentResult, int> _rqdPercentResultRepository;
    private readonly IAzureService _azureService;
    private readonly IDataEntryService _dataEntryService;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    public LoggingViewService(
        IRepository<LoggingView, int> repository,
        IRepository<LoggingViewColumn, int> loggingViewColumnRepository,
        IRepository<ProjectLoggingView, int> projectLoggingViewRepository,
        IRepository<Project, int> projectRepository,
        IRepository<RecoveryResult, int> recoveryResultRepository,
        IRepository<RqdCalculation, int> rqdCalculationRepository,
        IRepository<RqdCalculationResult, int> rqdCalculationResultRepository,
        IRepository<GeologySuite, int> geologySuiteRepository,
        IRepository<DataEntry, int> dataEntryRepository,
        IRepository<DrillHole, int> drillholeRepository,
        IRepository<AssayData, int> assayDataRepository,
        IRepository<AssayProjectSuite, int> assayProjectSuiteRepository,
        IRepository<DownholeData, int> downholeDataRepository,
        IRepository<ProjectSuite, int> projectSuiteRepository,
        IRepository<DrillHole, int> drillHoleRepository,
        IRepository<GeotechData, int> geotechDataRepository,
        IRepository<AssaySuite, int> assaySuiteRepository,
        IRepository<Suite, int> suiteRepository,
        IRepository<GeotechSuite, int> geotechSuiteRepository,
        IRepository<TrayDepthResult, int> trayDepthResultRepository,
        IRepository<GeologySuiteField, int> geologyFieldRepository,
        IRepository<RqdPercentResult, int> rqdPercentResultRepository,
        IDataEntryService dataEntryService,
        IAzureService azureService,
        IUnitOfWorkManager unitOfWorkManager,
        IAbpSession abpSession,
        IMapper mapper
    )
    {
        _repository = repository;
        _projectLoggingViewRepository = projectLoggingViewRepository;
        _loggingViewColumnRepository = loggingViewColumnRepository;
        _projectRepository = projectRepository;
        _recoveryResultRepository = recoveryResultRepository;
        _rqdCalculationRepository = rqdCalculationRepository;
        _rqdCalculationResultRepository = rqdCalculationResultRepository;
        _geologySuiteRepository = geologySuiteRepository;
        _dataEntryRepository = dataEntryRepository;
        _drillholeRepository = drillholeRepository;
        _azureService = azureService;
        _unitOfWorkManager = unitOfWorkManager;
        _abpSession = abpSession;
        _mapper = mapper;
        _drillHoleRepository = drillHoleRepository;
        _geotechDataRepository = geotechDataRepository;
        _assaySuiteRepository = assaySuiteRepository;
        _suiteRepository = suiteRepository;
        _geotechSuiteRepository = geotechSuiteRepository;
        _trayDepthResultRepository = trayDepthResultRepository;
        _geologyFieldRepository = geologyFieldRepository;
        _dataEntryService = dataEntryService;
        _rqdPercentResultRepository = rqdPercentResultRepository;
        _assayDataRepository = assayDataRepository;
        _assayProjectSuiteRepository = assayProjectSuiteRepository;
        _downholeDataRepository = downholeDataRepository;
        _projectSuiteRepository = projectSuiteRepository;
    }

    /// <inheritdoc />
    public async Task<LoggingView> CreateAsync(CreateLoggingViewDto input, bool returnExist = false)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var tenantId = _abpSession.GetTenantId();

        var existingLoggingView =
            await _repository.FirstOrDefaultAsync(d => d.TenantId == tenantId && d.Name == input.Name);
        if (existingLoggingView != null)
        {
            if (returnExist)
            {
                return existingLoggingView;
            }

            throw new UserFriendlyException(
                $"The LoggingView with the name {existingLoggingView.Name} already exists.");
        }

        var loggingView = new LoggingView()
        {
            Name = input.Name,
            Description = input.Description ?? string.Empty,
            Type = input.Type,
            IsActive = input.IsActive,
            TenantId = tenantId,
        };

        await _repository.InsertAsync(loggingView);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        if (input.ProjectIds.Count == 0) return loggingView;
        var assignProject = new AssignProjectDto
        {
            LoggingViewId = loggingView.Id,
            ProjectIds = input.ProjectIds,
        };
        await AssignProjectAsync(assignProject);

        return loggingView;
    }

    /// <inheritdoc />
    public async Task<LoggingViewDto> UpdateAsync(UpdateLoggingViewDto input)
    {
        var loggingView = await ValidateLoggingViewEntity(input.Id);

        if (input.IsActive == false)
        {
            var loggingViewUsed =
                await _loggingViewColumnRepository.FirstOrDefaultAsync(x => x.LoggingViewId == input.Id);
            if (loggingViewUsed != null)
            {
                throw new UserFriendlyException("Cannot deactivate this record because it is already in use.");
            }
        }

        if (input.ProjectIds is { Count: > 0 })
        {
            var assignProject = new AssignProjectDto
            {
                LoggingViewId = loggingView.Id,
                ProjectIds = input.ProjectIds,
            };
            await AssignProjectAsync(assignProject);
        }

        loggingView.Name = input.Name ?? loggingView.Name;
        loggingView.Description = input.Description ?? string.Empty;
        loggingView.Type = input.Type ?? loggingView.Type;
        loggingView.IsActive = input.IsActive ?? loggingView.IsActive;

        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<LoggingViewDto>> GetAllAsync(PagedLoggingViewResultRequestDto input)
    {
        var query = _repository.GetAllIncluding(x => x.ProjectLoggingViews)
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(),
                x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
            .WhereIf(input.ProjectId.HasValue,
                x => x.ProjectLoggingViews.Any(p => input.ProjectId != null && p.ProjectId == input.ProjectId.Value))
            .OrderBy(r => r.Name);

        var totalCount = await query.CountAsync();

        var loggingViews = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        return new PagedResultDto<LoggingViewDto>(totalCount, _mapper.Map<List<LoggingViewDto>>(loggingViews));
    }

    /// <inheritdoc />
    public async Task<LoggingViewDto> GetAsync(EntityDto<int> input)
    {
        var loggingView = await ValidateLoggingViewEntity(input.Id);

        var relateProject = await _projectRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.ProjectLoggingViews.Any(y => y.LoggingViewId == loggingView.Id))
            .ToListAsync();
        var projectsDto = _mapper.Map<List<ProjectDto>>(relateProject);

        var loggingViewDto = _mapper.Map<LoggingViewDto>(loggingView);
        loggingViewDto.Projects = projectsDto;

        return loggingViewDto;
    }

    /// <inheritdoc />
    public async Task<string> ExportDataAsync(ExportDataDto input)
    {
        var project = await _projectRepository.FirstOrDefaultAsync(x => x.Id == input.ProjectId);
        if (project == null)
        {
            throw new UserFriendlyException("Project not found.");
        }

        // Get drill holes
        var query = _drillholeRepository.GetAllIncluding(x => x.Project, x => x.Prospect)
            .AsNoTracking()
            .Where(x => x.ProjectId == input.ProjectId)
            .WhereIf(input.ProspectId.HasValue, x => x.ProspectId == input.ProspectId)
            .WhereIf(input.DrillHoleIds.Count > 0, x => input.DrillHoleIds.Contains(x.Id));

        var drillHoles = await query.Where(x => x.IsActive == true).ToListAsync();
        var drillHoleIds = drillHoles.Select(x => x.Id).ToList();

        // RQD
        var rqds = await _rqdCalculationRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.RqdCalculationProjects.Any(p => p.ProjectId == input.ProjectId))
            .ToListAsync();
        var rqdIds = rqds.Select(x => x.Id).ToList();

        var rqdResults = await _rqdPercentResultRepository.GetAllIncluding(x => x.RqdCalculation, x => x.DrillHole)
            .AsNoTracking()
            .Where(x => rqdIds.Contains(x.RqdCalculationId))
            .WhereIf(input.DrillHoleIds.Count > 0, x => input.DrillHoleIds.Contains(x.DrillHoleId))
            .ToListAsync();
        var drillHoleRqdResults = rqdResults.Where(x => drillHoleIds.Contains(x.DrillHoleId)).ToList();
        var rqdResultDisplay = drillHoleRqdResults.Where(x => x.RqdCalculation.IsDisplay == true).ToList();
        
        // Recovery
        var recoveryResults = await _recoveryResultRepository.GetAllIncluding(x => x.DrillHole)
            .AsNoTracking()
            .WhereIf(input.DrillHoleIds.Count > 0, x => input.DrillHoleIds.Contains(x.DrillHoleId))
            .OrderBy(x => x.OcrValueFrom)
            .ToListAsync();

        // TrayDepth
        var trayDepthResults = await _trayDepthResultRepository.GetAllIncluding(x => x.DrillHole)
            .AsNoTracking()
            .WhereIf(input.DrillHoleIds.Count > 0, x => input.DrillHoleIds.Contains(x.DrillHoleId))
            .OrderBy(x => x.TrayNumber)
            .ToListAsync();

        // For Calculations export type, we only need to process RQD, Recovery, and TrayDepth results
        // For All export type, we need to process all data including geology data
        var dataEntryDto = new List<DataEntryDto>();

        if (input.ExportType == ExportType.All)
        {
            // Geology Suites
            var geologySuites = await _geologySuiteRepository.GetAll()
                .Include(x => x.GeologySuiteFields)
                .ThenInclude(x => x.GeologyField)
                .AsNoTracking()
                .Where(x => x.GeologyProjectSuites.Any(p => p.ProjectId == input.ProjectId))
                .ToListAsync();
            var geologySuiteIds = geologySuites.Select(x => x.Id).ToList();

            // Get raw data entries and map to DTOs
            var dataEntries = await _dataEntryRepository.GetAllIncluding(x => x.DrillHole)
                .AsNoTracking()
                .Where(x => geologySuiteIds.Contains(x.GeologySuiteId))
                .WhereIf(input.DrillHoleIds.Count > 0, x => input.DrillHoleIds.Contains(x.DrillholeId))
                .OrderBy(r => r.DrillholeId)
                .ThenBy(r => r.DepthFrom)
                .ThenBy(r => r.DepthTo)
                .ToListAsync();

            dataEntryDto = _mapper.Map<List<DataEntryDto>>(dataEntries);

            var geologySuiteFields = await _geologyFieldRepository.GetAll()
                .AsNoTracking()
                .Include(x => x.GeologyField)
                .ThenInclude(x => x.RockNode)
                .Where(x => geologySuiteIds.Contains(x.GeologySuiteId))
                .Select(x => new DataEntryValueDto
                {
                    GeologysuiteFieldId = x.Id,
                    Sequence = x.Sequence,
                    FieldName = x.Name,
                    FieldType = x.GeologyField.Type
                    // RockNode = _mapper.Map<RockNodeDto>(x.GeologyField.RockNode),
                    // RockNodeId = x.GeologyField.RockNodeId
                })
                .ToListAsync();

            await _dataEntryService.HandleGetDataEntryAsync(geologySuiteFields, dataEntryDto);
        }

        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

        var exportType = input.ExportType == ExportType.All ? "HoleData" : "Calculations";
        var tempPath = Path.Combine(Path.GetTempPath(), $"LoggingView_{DateTime.Now:yyyyMMdd_HHmmss}");
        Directory.CreateDirectory(tempPath);

        // Create Excel file for each drill hole
        using var package = new ExcelPackage();
        
        // If export type is All, include all data sheets (geology suites, assay data, geophysics data)
        if (input.ExportType == ExportType.All)
        {
            var geologySuites = await _geologySuiteRepository.GetAll()
                .Include(x => x.GeologySuiteFields)
                .ThenInclude(x => x.GeologyField)
                .AsNoTracking()
                .Where(x => x.GeologyProjectSuites.Any(p => p.ProjectId == input.ProjectId))
                .ToListAsync();

            // Create separate sheets for each GeologySuite's data entries
            foreach (var geologySuite in geologySuites)
            {
                var isGeotech = geologySuite.IsGeotech;
                // Filter entries for this drill hole and suite
                var suiteEntries = dataEntryDto
                    .Where(x => x.GeologySuiteId == geologySuite.Id && drillHoleIds.Contains(x.DrillholeId))
                    .OrderBy(x => x.Drillhole.Name)
                    .ThenBy(x => x.DepthFrom)
                    .ToList();

                var suiteSheet = package.Workbook.Worksheets.Add(geologySuite.Name);

                // Add headers
                suiteSheet.Cells[1, 1].Value = "HOLEID";
                suiteSheet.Cells[1, 2].Value = "Depth From";
                suiteSheet.Cells[1, 3].Value = "Depth To";
                if (isGeotech)
                {
                    suiteSheet.Cells[1, 4].Value = "RQD(%)";
                }

                // Add field headers from GeologySuiteFields
                var columnIndex = isGeotech ? 5 : 4;
                var fieldIndexMap = new Dictionary<int, int>();
                foreach (var field in geologySuite.GeologySuiteFields.OrderBy(f => f.Sequence))
                {
                    suiteSheet.Cells[1, columnIndex].Value = field.Name;
                    fieldIndexMap[field.Id] = columnIndex;
                    columnIndex++;
                }

                // Add data
                for (var row = 0; row < suiteEntries.Count; row++)
                {
                    var entry = dataEntryDto.First(x => x.Id == suiteEntries[row].Id);
                    suiteSheet.Cells[row + 2, 1].Value = entry.Drillhole.Name;
                    suiteSheet.Cells[row + 2, 2].Value = entry.DepthFrom;
                    suiteSheet.Cells[row + 2, 3].Value = entry.DepthTo;
                    if (isGeotech)
                    {
                        var matchingItem = rqdResultDisplay.FirstOrDefault(item => item.OcrValueFrom == entry.DepthFrom && item.OcrValueTo == entry.DepthTo);
                        suiteSheet.Cells[row + 2, 4].Value = matchingItem != null ? matchingItem.Total : "";
                    }

                    // Add field values in their respective columns based on field type
                    foreach (var value in entry.DataEntryValues)
                    {
                        if (fieldIndexMap.TryGetValue(value.GeologysuiteFieldId, out var valueColumnIndex))
                        {
                            object? cellValue = value.FieldType switch
                            {
                                FieldType.RockSelectNumber => string.IsNullOrEmpty(value.RockType?.Name)
                                    ? ""
                                    : $"{value.RockType?.Name} - {value.NumberValue}",
                                FieldType.Number => value.NumberValue,
                                FieldType.RockTypeNumber => value.NumberValue,
                                FieldType.Colour => value.Colour?.Name,
                                FieldType.RockGroup => value.RockType?.Name,
                                FieldType.PickList => value.PickListItem?.Name,
                                FieldType.Description => value.Description,
                                FieldType.Date => value.DateValue,
                                FieldType.RockNode => value.RockNode?.Name,
                                _ => null
                            };
                            suiteSheet.Cells[row + 2, valueColumnIndex].Value = cellValue;

                            if (value.FieldType == FieldType.Date && cellValue != null)
                            {
                                suiteSheet.Cells[row + 2, valueColumnIndex].Style.Numberformat.Format =
                                    "dd/MM/yyyy";
                            }
                        }
                    }
                }
            }

            // Export Assay data
            var assayProjectSuites = await _assayProjectSuiteRepository.GetAllIncluding(x => x.AssaySuite)
                .AsNoTracking()
                .Where(x => x.ProjectId == input.ProjectId)
                .ToListAsync();

            foreach (var assaySuite in assayProjectSuites)
            {
                var assayData = await _assayDataRepository.GetAll()
                    .AsNoTracking()
                    .Where(x => drillHoleIds.Contains(x.DrillHoleId) && x.AssaySuiteId == assaySuite.AssaySuiteId)
                    .Where(x => !string.IsNullOrEmpty(x.AttributeName))
                    .OrderBy(x => x.CreationTime)
                    .ToListAsync();

                if (assayData.Count != 0)
                {
                    var assaySheet = package.Workbook.Worksheets.Add($"{assaySuite.AssaySuite.Name}");

                    // Group data by GroupId to get complete rows
                    var groupedData = assayData
                        .GroupBy(d => new { d.GroupId, d.DrillHole })
                        .Select(g => new
                        {
                            DrillHoleName = g.Key.DrillHole,
                            Attributes = g.ToDictionary(d => d.AttributeName, d => d.AttributeValue)
                        })
                        .OrderBy(g => g.DrillHoleName)
                        .ThenBy(g => double.Parse(g.Attributes.GetValueOrDefault("Depth From", "0")))
                        .ToList();

                    if (groupedData.Count != 0)
                    {
                        // Add Drillhole as first column
                        assaySheet.Cells[1, 1].Value = "HOLEID";

                        // Get all unique column names
                        var columnNames = groupedData
                            .SelectMany(d => d.Attributes.Keys)
                            .Distinct()
                            .ToList();

                        // Write headers (starting from column 2 since Drillhole is in column 1)
                        for (var col = 0; col < columnNames.Count; col++)
                        {
                            assaySheet.Cells[1, col + 2].Value = columnNames[col];
                        }

                        // Write data
                        for (var row = 0; row < groupedData.Count; row++)
                        {
                            // Add drillhole name in first column
                            assaySheet.Cells[row + 2, 1].Value = groupedData[row].DrillHoleName;

                            var rowData = groupedData[row].Attributes;
                            for (var col = 0; col < columnNames.Count; col++)
                            {
                                if (rowData.TryGetValue(columnNames[col], out var value))
                                {
                                    assaySheet.Cells[row + 2, col + 2].Value = value;
                                }
                            }
                        }
                    }
                }
            }

            // Export Geophysics data
            var geophysicsSuites = await _projectSuiteRepository.GetAllIncluding(x => x.Suite)
                .AsNoTracking()
                .Where(x => x.ProjectId == input.ProjectId)
                .ToListAsync();

            foreach (var geophysicsSuite in geophysicsSuites)
            {
                var geophysicsData = await _downholeDataRepository.GetAll()
                    .AsNoTracking()
                    .Where(x => drillHoleIds.Contains(x.DrillHoleId) && x.SuiteId == geophysicsSuite.SuiteId)
                    .Where(x => !string.IsNullOrEmpty(x.AttributeName))
                    .OrderBy(x => x.CreationTime)
                    .ToListAsync();

                if (geophysicsData.Count != 0)
                {
                    var geophysicsSheet = package.Workbook.Worksheets.Add($"{geophysicsSuite.Suite.Name}");

                    // Group data by GroupId to get complete rows
                    var groupedData = geophysicsData
                        .GroupBy(d => new { d.GroupId, d.DrillHole })
                        .Select(g => new
                        {
                            DrillHoleName = g.Key.DrillHole,
                            Attributes = g.ToDictionary(d => d.AttributeName, d => d.AttributeValue)
                        })
                        .OrderBy(g => g.DrillHoleName)
                        .ThenBy(g => double.Parse(g.Attributes.GetValueOrDefault("Depth From", "0")))
                        .ToList();

                    if (groupedData.Count != 0)
                    {
                        // Add Drillhole and Depth (m) as first two columns
                        geophysicsSheet.Cells[1, 1].Value = "HOLEID";
                        geophysicsSheet.Cells[1, 2].Value = "Depth (m)";

                        // Get all unique column names
                        var columnNames = groupedData
                            .SelectMany(d => d.Attributes.Keys)
                            .Distinct()
                            .ToList();

                        // Write headers (starting from column 3 since Drillhole and Depth are in columns 1-2)
                        for (var col = 0; col < columnNames.Count; col++)
                        {
                            geophysicsSheet.Cells[1, col + 3].Value = columnNames[col];
                        }

                        // Write data
                        for (var row = 0; row < groupedData.Count; row++)
                        {
                            // Add drillhole name in first column
                            geophysicsSheet.Cells[row + 2, 1].Value = groupedData[row].DrillHoleName;

                            var rowData = groupedData[row].Attributes;
                            // Try to get depth value
                            if (rowData.TryGetValue("Depth (m)", out var depthValue))
                            {
                                geophysicsSheet.Cells[row + 2, 2].Value = depthValue;
                            }

                            for (var col = 0; col < columnNames.Count; col++)
                            {
                                if (rowData.TryGetValue(columnNames[col], out var value))
                                {
                                    geophysicsSheet.Cells[row + 2, col + 3].Value = value;
                                }
                            }
                        }
                    }
                }
            }
        }

        // Add Recovery sheet at the end - included in both All and Calculations export types
        var drillHoleRecoveryResults = recoveryResults.Where(x => drillHoleIds.Contains(x.DrillHoleId))
            .OrderBy(x => x.OcrValueFrom)
            .ToList();
        if (drillHoleRecoveryResults.Count != 0)
        {
            var recoverySheet = package.Workbook.Worksheets.Add("Recovery");
            // Add headers
            recoverySheet.Cells[1, 1].Value = "HOLEID";
            recoverySheet.Cells[1, 2].Value = "Depth From";
            recoverySheet.Cells[1, 3].Value = "Depth To";
            recoverySheet.Cells[1, 4].Value = "Drilled Length";
            recoverySheet.Cells[1, 5].Value = "Core Length";
            recoverySheet.Cells[1, 6].Value = "Recovery(%)";

            // Add recovery data
            for (var row = 0; row < drillHoleRecoveryResults.Count; row++)
            {
                var result = drillHoleRecoveryResults[row];
                recoverySheet.Cells[row + 2, 1].Value = result.DrillHole.Name;
                recoverySheet.Cells[row + 2, 2].Value = result.OcrValueFrom;
                recoverySheet.Cells[row + 2, 3].Value = result.OcrValueTo;
                recoverySheet.Cells[row + 2, 4].Value = result.DepthInterval;
                recoverySheet.Cells[row + 2, 5].Value = result.Length;
                recoverySheet.Cells[row + 2, 6].Value = Math.Round(result.Recovery, 2);
            }
        }

        // Group RQD results by RQD name and create separate sheets - included in both All and Calculations export types
        var rqdGroups = drillHoleRqdResults.GroupBy(x => x.RqdCalculation.Name);
        foreach (var group in rqdGroups)
        {
            var rqdSheet = package.Workbook.Worksheets.Add(group.Key);

            // Add headers
            rqdSheet.Cells[1, 1].Value = "HOLEID";
            rqdSheet.Cells[1, 2].Value = "Depth From";
            rqdSheet.Cells[1, 3].Value = "Depth To";
            rqdSheet.Cells[1, 4].Value = "Length of core pieces";
            rqdSheet.Cells[1, 5].Value = "Length of interval";
            rqdSheet.Cells[1, 6].Value = "RQD(%)";
            rqdSheet.Cells[1, 7].Value = "Number of pieces";

            // Add RQD data
            var results = group.OrderBy(x => x.OcrValueFrom).ToList();
            for (var row = 0; row < results.Count; row++)
            {
                var result = results[row];
                rqdSheet.Cells[row + 2, 1].Value = result.DrillHole.Name;
                rqdSheet.Cells[row + 2, 2].Value = result.OcrValueFrom;
                rqdSheet.Cells[row + 2, 3].Value = result.OcrValueTo;
                rqdSheet.Cells[row + 2, 4].Value = result.Length;
                rqdSheet.Cells[row + 2, 5].Value = result.DepthInterval;
                rqdSheet.Cells[row + 2, 6].Value = Math.Round(result.Total, 2);
                rqdSheet.Cells[row + 2, 7].Value = result.NumberOfPieces;
            }
        }

        // Add TrayDepth sheet at the end - included in both All and Calculations export types
        var drillHoleTrayDepthResults = trayDepthResults.Where(x => drillHoleIds.Contains(x.DrillHoleId))
            .OrderBy(x => x.TrayNumber)
            .ToList();
        if (drillHoleTrayDepthResults.Count != 0)
        {
            var trayDepthSheet = package.Workbook.Worksheets.Add("Tray Depth");
            // Add headers
            trayDepthSheet.Cells[1, 1].Value = "HOLEID";
            trayDepthSheet.Cells[1, 2].Value = "Tray Number";
            trayDepthSheet.Cells[1, 3].Value = "Start Depth";
            trayDepthSheet.Cells[1, 4].Value = "End Depth";

            // Add tray depth data
            for (var row = 0; row < drillHoleTrayDepthResults.Count; row++)
            {
                var result = drillHoleTrayDepthResults[row];
                trayDepthSheet.Cells[row + 2, 1].Value = result.DrillHole.Name;
                trayDepthSheet.Cells[row + 2, 2].Value = result.TrayNumber;
                trayDepthSheet.Cells[row + 2, 3].Value = Math.Round(result.StartDepth, 2);
                trayDepthSheet.Cells[row + 2, 4].Value = Math.Round(result.EndDepth, 2);
            }
        }

        // If no worksheets were added (could happen for Calculations type with no calculation data)
        // add an info sheet so the Excel file is not empty
        if (package.Workbook.Worksheets.Count == 0)
        {
            var infoSheet = package.Workbook.Worksheets.Add("Info");
            infoSheet.Cells[1, 1].Value = "No calculation data available for this drill hole";
        }

        var fileNameDrillhole = drillHoles.FirstOrDefault() != null ? drillHoles.FirstOrDefault()?.Name : project.Name;
        var fileName = drillHoles.Count > 1
            ? $"{project.Name}_{DateTime.Now:yyyy-MM-dd-HH-mm}.xlsx"
            : $"{fileNameDrillhole}_{DateTime.Now:yyyy-MM-dd-HH-mm}.xlsx";

        // Save Excel file
        var excelFilePath = Path.Combine(tempPath, fileName);
        await package.SaveAsAsync(new FileInfo(excelFilePath));

        // Create ZIP file
        var zipPath = Path.Combine(Path.GetTempPath(), $"LoggingView_{exportType}_{DateTime.Now:yyyy-MM-dd-HH-mm}.zip");
        ZipFile.CreateFromDirectory(tempPath, zipPath);

        // Upload ZIP to Azure
        string uploadedUrl;
        await using (var fileStream = File.OpenRead(zipPath))
        {
            uploadedUrl = await _azureService.UploadFileAsync(
                fileStream,
                $"Exports/{project.Name}_LoggingViewData_{exportType}_{DateTime.Now:yyyy-MM-dd-HH-mm}.zip",
                true);
        }

        // Cleanup temporary files
        Directory.Delete(tempPath, true);
        File.Delete(zipPath);

        return uploadedUrl;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<DrillholeLoggingDto>> GetDrillholeLoggingAsync(GetDrillholeLoggingRequestDto input)
    {
        // Get all project suites first
        var geologySuites = await _geologySuiteRepository.GetAllIncluding(x => x.GeologyProjectSuites)
            .AsNoTracking()
            .Where(x => x.GeologyProjectSuites.Any(p => p.ProjectId == input.ProjectId))
            .ToListAsync();

        var assaySuites = await _assaySuiteRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.AssayProjectSuites.Any(p => p.ProjectId == input.ProjectId))
            .ToListAsync();

        var suites = await _suiteRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.SuiteProjects.Any(p => p.ProjectId == input.ProjectId))
            .ToListAsync();

        var queryGeotech = _geotechSuiteRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.ProjectGeotechSuites.Any(p => p.ProjectId == input.ProjectId));

        var geotechSuites = await queryGeotech
            .ToListAsync();

        // Get all drillhole for the project and prospect
        var queryDrillHole = _drillHoleRepository.GetAllIncluding(x => x.Project, x => x.Prospect)
            .AsNoTracking()
            .Where(dh => dh.ProjectId == input.ProjectId)
            .WhereIf(input.ProspectId.HasValue, dh => dh.ProspectId == input.ProspectId)
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(),
                x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .WhereIf(input.DrillHoleStatus.HasValue, x => x.DrillHoleStatus == input.DrillHoleStatus);

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.SortField) && !string.IsNullOrWhiteSpace(input.SortOrder))
        {
            queryDrillHole = queryDrillHole.ApplySorting(input.SortField, input.SortOrder, r => r.CreationTime);
        }
        else
        {
            queryDrillHole = queryDrillHole.ApplySorting("Project.Name", "asc", r => r.CreationTime);
            queryDrillHole = queryDrillHole.OrderBy(x => x.Name); // Default sorting
        }

        var totalCount = await queryDrillHole.CountAsync();

        var drillHoles = await queryDrillHole
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        var result = new List<DrillholeLoggingDto>();

        foreach (var drillhole in drillHoles)
        {
            var dto = new DrillholeLoggingDto
            {
                Name = drillhole.Name,
                Project = _mapper.Map<ProjectDto>(drillhole.Project),
                Prospect = _mapper.Map<ProspectDto>(drillhole.Prospect)
            };

            // Initialize all geology suites with null values
            foreach (var suite in geologySuites.Where(suite => !string.IsNullOrEmpty(suite.Name)))
            {
                dto.GeologySuites[suite.Name] = null;
            }

            // Get and populate geology data where it exists
            var geologyData = await _dataEntryRepository.GetAllIncluding(x => x.GeologySuite)
                .AsNoTracking()
                .Where(d => d.DrillholeId == drillhole.Id)
                .ToListAsync();

            if (geologyData.Count != 0)
            {
                var groupedGeology = geologyData.GroupBy(d => d.GeologySuite.Name);
                foreach (var group in groupedGeology.Where(g => !string.IsNullOrEmpty(g.Key)))
                {
                    var orderedEntries = group.OrderBy(e => e.DepthFrom).ToList();
                    dto.GeologySuites[group.Key] = new GeologySuiteInfoDto
                    {
                        StartDepth = orderedEntries.Min(e => e.DepthFrom ?? 0),
                        EndDepth = orderedEntries.Max(e => e.DepthTo ?? 0),
                        HasOverlap = HasOverlappingIntervals(orderedEntries),
                        HasGap = HasGaps(orderedEntries)
                    };
                }
            }

            // Initialize all assay suites with null values
            foreach (var suite in assaySuites.Where(suite => !string.IsNullOrEmpty(suite.Name)))
            {
                dto.AssaySuites[suite.Name] = null;
            }

            // Get and populate assay data where it exists
            var assayData = await _assayDataRepository.GetAllIncluding(x => x.AssaySuite)
                .AsNoTracking()
                .Where(d => d.DrillHoleId == drillhole.Id)
                .ToListAsync();

            if (assayData.Count != 0)
            {
                var groupedAssays = assayData.GroupBy(a => a.AssaySuite.Name);
                foreach (var group in groupedAssays.Where(g => !string.IsNullOrEmpty(g.Key)))
                {
                    var orderedAssays = group
                        .OrderBy(a => a.GroupId)
                        .Where(x => x.AttributeName.ToLower().Contains("depth"))
                        .ToList();
                    var depthString = orderedAssays.Select(a => a.AttributeValue).ToList();
                    var depth = depthString
                        .Select(s => double.TryParse(s, out var d) ? d : (double?)null)
                        .Where(d => d.HasValue)
                        .Select(d => d.Value)
                        .ToList();
                    dto.AssaySuites[group.Key] = new AssaySuiteInfoDto
                    {
                        StartDepth = depth.Min(),
                        EndDepth = depth.Max(),
                        AssayCount = orderedAssays.Count
                    };
                }
            }

            // Get geophysics data
            foreach (var suite in suites.Where(suite => !string.IsNullOrEmpty(suite.Name)))
            {
                dto.GeophysicsSuites[suite.Name] = false;
            }

            var geophysicsData = await _downholeDataRepository.GetAllIncluding(x => x.Suite)
                .AsNoTracking()
                .Where(d => d.DrillHoleId == drillhole.Id)
                .ToListAsync();

            if (geophysicsData.Count != 0)
            {
                var groupedGeophysics = geophysicsData.GroupBy(g => g.Suite.Name);
                foreach (var group in groupedGeophysics.Where(g => !string.IsNullOrEmpty(g.Key)))
                {
                    dto.GeophysicsSuites[group.Key] =
                        group.Any(g => !string.IsNullOrEmpty(g.AttributeValue));
                }
            }

            // Get geotech data
            foreach (var suite in geotechSuites.Where(suite => !string.IsNullOrEmpty(suite.Name)))
            {
                dto.GeotechSuites[suite.Name] = false;
            }

            var geotechData = await _geotechDataRepository.GetAllIncluding(x => x.GeotechSuite)
                .AsNoTracking()
                .Where(d => d.DrillHoleId == drillhole.Id)
                .ToListAsync();

            if (geotechData.Count != 0)
            {
                var groupedGeotech = geotechData.GroupBy(g => g.GeotechSuite.Name);
                foreach (var group in groupedGeotech.Where(g => !string.IsNullOrEmpty(g.Key)))
                {
                    dto.GeotechSuites[group.Key] = group.Any();
                }
            }

            // Initialize calculation tabs with false values
            dto.CalculationTabs["Recovery"] = false;
            dto.CalculationTabs["Rqd"] = false;

            // Get and update recovery data
            var recoveryData = await _recoveryResultRepository.GetAll()
                .AsNoTracking()
                .Where(d => d.DrillHoleId == drillhole.Id)
                .ToListAsync();
            if (recoveryData.Count != 0)
            {
                dto.CalculationTabs["Recovery"] = true;
            }

            // Get and update RQD data
            var rqdData = await _rqdCalculationResultRepository.GetAll()
                .AsNoTracking()
                .Where(d => d.DrillHoleId == drillhole.Id)
                .ToListAsync();
            if (rqdData.Count != 0)
            {
                dto.CalculationTabs["Rqd"] = true;
            }

            result.Add(dto);
        }

        return new PagedResultDto<DrillholeLoggingDto>(totalCount, _mapper.Map<List<DrillholeLoggingDto>>(result));
    }

    private static bool HasOverlappingIntervals(List<DataEntry> entries)
    {
        for (var i = 0; i < entries.Count - 1; i++)
        {
            if (entries[i].DepthTo > entries[i + 1].DepthFrom)
            {
                return true;
            }
        }

        return false;
    }

    private static bool HasGaps(List<DataEntry> entries)
    {
        for (var i = 0; i < entries.Count - 1; i++)
        {
            if (entries[i].DepthTo < entries[i + 1].DepthFrom)
            {
                return true;
            }
        }

        return false;
    }

    private async Task<LoggingView> ValidateLoggingViewEntity(int id)
    {
        var loggingView = await _repository.FirstOrDefaultAsync(x => x.Id == id);

        if (loggingView == null)
        {
            throw new EntityNotFoundException(typeof(LoggingView), id);
        }

        return loggingView;
    }

    private async Task AssignProjectAsync(AssignProjectDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var existingProjects = await _projectLoggingViewRepository.GetAllListAsync(x =>
            x.LoggingViewId == input.LoggingViewId);

        var existingIds = existingProjects.Select(x => x.ProjectId).ToList();

        var projectsToAdd = input.ProjectIds.Except(existingIds).ToList();
        var projectsToDelete = existingIds.Except(input.ProjectIds).ToList();

        foreach (var projectLoggingView in projectsToAdd.Select(projectId =>
                     new ProjectLoggingView
                     {
                         ProjectId = projectId,
                         LoggingViewId = input.LoggingViewId,
                     }))
        {
            await _projectLoggingViewRepository.InsertAsync(projectLoggingView);
        }

        foreach (var projectId in projectsToDelete)
        {
            await _projectLoggingViewRepository.DeleteAsync(x =>
                x.ProjectId == projectId && x.LoggingViewId == input.LoggingViewId);
        }
    }
}