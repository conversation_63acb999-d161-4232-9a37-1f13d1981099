using System.Threading.Tasks;
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using aibase.LoggingViews.Dto;

namespace aibase.LoggingViews;

/// <inheritdoc />
public interface ILoggingViewAppService : IAsyncCrudAppService<LoggingViewDto, int, PagedLoggingViewResultRequestDto,
    CreateLoggingViewDto, UpdateLoggingViewDto>
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<string> ExportDataAsync(ExportDataDto input);

    /// <summary>
    /// Gets logging data summary for all drillholes in a project and prospect
    /// </summary>
    /// <param name="input">ProjectId and ProspectId</param>
    /// <returns>List of drillhole logging summaries</returns>
    Task<PagedResultDto<DrillholeLoggingDto>> GetDrillholeLoggingAsync(GetDrillholeLoggingRequestDto input);
}