using System.Collections.Generic;
using aibase.Projects.Dto;
using aibase.Prospects.Dto;

namespace aibase.LoggingViews.Dto;

/// <summary>
/// 
/// </summary>
public class DrillholeLoggingDto
{
    /// <summary>
    /// 
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public ProjectDto Project { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public ProspectDto Prospect { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Dictionary<string, GeologySuiteInfoDto?> GeologySuites { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Dictionary<string, AssaySuiteInfoDto?> AssaySuites { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Dictionary<string, bool?> GeophysicsSuites { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Dictionary<string, bool?> GeotechSuites { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public Dictionary<string, bool> CalculationTabs { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public DrillholeLoggingDto()
    {
        GeologySuites = new Dictionary<string, GeologySuiteInfoDto>();
        AssaySuites = new Dictionary<string, AssaySuiteInfoDto>();
        GeophysicsSuites = new Dictionary<string, bool?>();
        GeotechSuites = new Dictionary<string, bool?>();
        CalculationTabs = new Dictionary<string, bool>();
    }
}

/// <summary>
/// 
/// </summary>
public class GeologySuiteInfoDto
{
    /// <summary>
    /// 
    /// </summary>
    public double StartDepth { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double EndDepth { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool HasOverlap { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool HasGap { get; set; }
}

/// <summary>
/// 
/// </summary>
public class AssaySuiteInfoDto
{
    /// <summary>
    /// 
    /// </summary>
    public double StartDepth { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double EndDepth { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int AssayCount { get; set; }
}