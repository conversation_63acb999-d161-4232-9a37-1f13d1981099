using System.ComponentModel.DataAnnotations;
using Abp.Application.Services.Dto;
using aibase.DrillHoleEntity;

namespace aibase.LoggingViews.Dto;

/// <summary>
/// 
/// </summary>
public class GetDrillholeLoggingRequestDto : PagedResultRequestDto
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int ProjectId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? ProspectId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? Keyword { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public DrillHoleStatus? DrillHoleStatus { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? SortField { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string? SortOrder { get; set; }
}