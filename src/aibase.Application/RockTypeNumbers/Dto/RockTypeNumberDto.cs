using Abp.Application.Services.Dto;
using Abp.AutoMapper;
using aibase.Numbers.Dto;
using aibase.RockTypes.Dto;

namespace aibase.RockTypeNumbers.Dto;

/// <inheritdoc />
[AutoMap(typeof(RockTypeNumber))]
public class RockTypeNumberDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public virtual RockTypeDto RockType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public virtual NumberDto Number { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool IsActive { get; set; }
}