using System.ComponentModel.DataAnnotations;

namespace aibase.RockTypeNumbers.Dto;

/// <summary>
/// 
/// </summary>
public class CreateRockTypeNumberDto
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int RockTypeId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int NumberId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public bool IsActive { get; set; }
}