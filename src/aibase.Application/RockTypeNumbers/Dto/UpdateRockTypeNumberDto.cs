using Abp.Application.Services.Dto;
using Abp.AutoMapper;

namespace aibase.RockTypeNumbers.Dto;

/// <inheritdoc />
[AutoMap(typeof(RockTypeNumber))]
public class UpdateRockTypeNumberDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? RockTypeId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? NumberId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public bool? IsActive { get; set; }
}