using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.GeologyFields;
using aibase.RockTypeNumbers.Dto;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.RockTypeNumbers.Services;

/// <inheritdoc />
public class RockTypeNumberService : IRockTypeNumberService
{
    private readonly IRepository<RockTypeNumber, int> _repository;
    private readonly IRepository<GeologyField, int> _geologyFieldRepository;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="repository"></param>
    /// <param name="geologyFieldRepository"></param>
    /// <param name="abpSession"></param>
    /// <param name="mapper"></param>
    public RockTypeNumberService(
        IRepository<RockTypeNumber, int> repository, 
        IRepository<GeologyField, int> geologyFieldRepository,
        IAbpSession abpSession, 
        IMapper mapper
        )
    {
        _repository = repository;
        _geologyFieldRepository = geologyFieldRepository;
        _abpSession = abpSession;
        _mapper = mapper;
    }

    /// <inheritdoc />
    public async Task<RockTypeNumber> CreateAsync(CreateRockTypeNumberDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var rockTypeNumber = new RockTypeNumber()
        {
            Name = input.Name,
            RockTypeId = input.RockTypeId,
            NumberId = input.NumberId,
            IsActive = input.IsActive,
            TenantId = _abpSession.GetTenantId(),
        };

        await _repository.InsertAsync(rockTypeNumber);
        return rockTypeNumber;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<RockTypeNumberDto>> GetAllAsync(PagedRockTypeNumberResultRequestDto input)
    {
        var query = _repository.GetAllIncluding(x => x.RockType, x => x.Number)
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .OrderBy(r => r.Name);

        var totalCount = await query.CountAsync();

        var rockTypeNumber = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        return new PagedResultDto<RockTypeNumberDto>(totalCount, _mapper.Map<List<RockTypeNumberDto>>(rockTypeNumber));
    }

    /// <inheritdoc />
    public async Task<RockTypeNumberDto> UpdateAsync(UpdateRockTypeNumberDto input)
    {
        var rockTypeNumber = await ValidateRockTypeNumberEntity(input.Id);

        if (input.IsActive == false)
        {
            var geologyDateUsed =
                await _geologyFieldRepository.FirstOrDefaultAsync(x =>
                    x.Type == FieldType.RockTypeNumber && x.RockTypeNumberId == input.Id);
            if (geologyDateUsed != null)
            {
                throw new UserFriendlyException("Cannot deactivate this record because it is already in use.");
            }
        }

        rockTypeNumber.Name = input.Name ?? rockTypeNumber.Name;
        rockTypeNumber.RockTypeId = input.RockTypeId ?? rockTypeNumber.RockTypeId;
        rockTypeNumber.NumberId = input.NumberId ?? rockTypeNumber.NumberId;
        rockTypeNumber.IsActive = input.IsActive ?? rockTypeNumber.IsActive;

        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task<RockTypeNumberDto> GetAsync(EntityDto<int> input)
    {
        var rockTypeNumber = await ValidateRockTypeNumberEntity(input.Id);
        return _mapper.Map<RockTypeNumberDto>(rockTypeNumber);
    }

    private async Task<RockTypeNumber> ValidateRockTypeNumberEntity(int id)
    {
        var rockTypeNumber = await _repository.FirstOrDefaultAsync(x => x.Id == id);

        if (rockTypeNumber == null)
        {
            throw new EntityNotFoundException(typeof(RockTypeNumber), id);
        }

        return rockTypeNumber;
    }
}