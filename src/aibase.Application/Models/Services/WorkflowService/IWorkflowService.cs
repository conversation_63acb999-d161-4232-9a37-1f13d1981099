using System.Collections.Generic;
using System.Threading.Tasks;
using Abp.Dependency;
using aibase.ImageCrops;
using aibase.ImageCrops.Dto;
using aibase.ImageEntity;
using aibase.Models.Dto;

namespace aibase.Models.Services.WorkflowService;

/// <inheritdoc />
public interface IWorkflowService : ITransientDependency
{
    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    Task<List<OcrResultV2Dto>> PerformPolygonCropOcrSegmentAsync(string url, Image image, ImageStatus? imageStatusDone, string? prompt, bool segmentFlag);
    
    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    Task<List<ImageCrop>> PerformPolygonCropAsync(string url, Image image, ImageStatus? imageStatusDone);
    
    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    Task<List<SegmentResultDto>> PerformPolygonCropOcrSegmentDetailCorePiecesAsync(string url, Image image, ImageStatus? imageStatusDone, string? prompt);
    
    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    Task<List<ImageCropDto>> PerformSegmentAutoCropAsync(string url, Image image, ImageStatus? imageStatusDone);
    
    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    Task<List<OcrResultV2Dto>> PerformProcessCoreOutlineAsync(string url, Image image, ImageStatus? imageStatusDone);
}