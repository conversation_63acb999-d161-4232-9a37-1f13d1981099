﻿using System.Collections.Generic;
using aibase.Images.Dto;

namespace aibase.Models.Dto;

/// <summary>
/// 
/// </summary>
public class SegmentResultDto
{
    /// <summary>
    /// 
    /// </summary>
    public string name { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public bool isPolyComplete { get; set; } = true;

    /// <summary>
    /// 
    /// </summary>
    public List<List<double>> points { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    public string color { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public string Class { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public int rowIndex { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public CoordinateV2Dto BoundingSegment { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public LineCoordinateDto IntersectionPoints { get; set; }
}