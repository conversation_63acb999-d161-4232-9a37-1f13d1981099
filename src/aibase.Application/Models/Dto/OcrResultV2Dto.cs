﻿namespace aibase.Models.Dto;

/// <summary>
/// 
/// </summary>
public class OcrResultV2Dto
{
    /// <summary>
    /// 
    /// </summary>
    public string id { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public string type { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public double x { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double originalX { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double y { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double width { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double height { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string text { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public double probability { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? rowIndex { get; set; } = -1;

    /// <summary>
    /// 
    /// </summary>
    public int ImageCropId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int GeotechDataId { get; set; }
}