using System.Collections.Generic;
using Newtonsoft.Json;

namespace aibase.Models.Dto;

/// <summary>
/// 
/// </summary>
public class SegmentDetailOutputDto
{
    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("status")]
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("refined_segmentation")]
    public List<RefinedSegmentation> RefinedSegmentations { get; set; } = [];
}

/// <summary>
/// 
/// </summary>
public class RefinedSegmentation
{
    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("class")]
    public string Class { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("points")]
    public List<List<double>> Points { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("rowIndex")]
    public int RowIndex { get; set; }
}