using System.Collections.Generic;
using Newtonsoft.Json;

namespace aibase.Models.Dto;

/// <summary>
/// 
/// </summary>
public class DetectionResponse
{
    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("detection")]
    public DetectionResult Detection { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("detections_filter")]
    public DetectionResult DetectionsFilter { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("google_vision_ocr_entire_image")]
    public OcrResult GoogleVisionOcrEntireImage { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("google_vision_ocr_segment_crop")]
    public List<List<OcrResult>> GoogleVisionOcrSegmentCrop { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("google_vision_block_ocr")]
    public List<OcrResult> GoogleVisionBlockOcr { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("google_vision_ocr_segment_full")]
    public List<object> GoogleVisionOcrSegmentFull { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("segmentation")]
    public List<SegmentationPoint> Segmentation { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("segmentation_detail")]
    public List<SegmentationDetail> SegmentationDetail { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("segmentation_full")]
    public object SegmentationFull { get; set; }
}

/// <summary>
/// 
/// </summary>
public class Point
{
    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("x")]
    public double X { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("y")]
    public double Y { get; set; }
}

/// <summary>
/// 
/// </summary>
public class ImageDimensions
{
    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("height")]
    public double? Height { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("width")]
    public double? Width { get; set; }
}

/// <summary>
/// 
/// </summary>
public class Prediction
{
    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("boxId")]
    public string? BoxId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("class")]
    public string Class { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("class_id")]
    public double ClassId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("confidence")]
    public double Confidence { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("detection_id")]
    public string DetectionId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("height")]
    public double Height { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("parent_id")]
    public string ParentId { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("rows_count")]
    public int RowsCount { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("rowIndex")]
    public int RowIndex { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("valid")]
    public bool? Valid { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("width")]
    public double Width { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("x")]
    public double X { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("x_raw")]
    public double XRaw { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("y")]
    public double Y { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("y_raw")]
    public double YRaw { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("points")]
    public List<Point> Points { get; set; } = [];
}

/// <summary>
/// 
/// </summary>
public class DetectionResult
{
    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("image")]
    public ImageDimensions Image { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("predictions")]
    public List<Prediction> Predictions { get; set; } = [];
}

/// <summary>
/// 
/// </summary>
public class OcrPrediction
{
    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("class")]
    public string Class { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("class_id")]
    public double ClassId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("confidence")]
    public double Confidence { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("detection_id")]
    public string DetectionId { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("height")]
    public double Height { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("parent_id")]
    public string ParentId { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("width")]
    public double Width { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("x")]
    public double X { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("y")]
    public double Y { get; set; }
}

/// <summary>
/// 
/// </summary>
public class OcrResult
{
    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("language")]
    public string Language { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("text")]
    public string Text { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("gemini_text")]
    public string GeminiText { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("x")]
    public double X { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("y")]
    public double Y { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("width")]
    public double Width { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("height")]
    public double Height { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("rowIndex")]
    public int RowIndex { get; set; }
}

/// <summary>
/// 
/// </summary>
public class SegmentationPoint
{
    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("boxId")]
    public string BoxId { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("class")]
    public string Class { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("confidence")]
    public double Confidence { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("detection_id")]
    public string DetectionId { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("height")]
    public double Height { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("points")]
    public List<List<double>> Points { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("rowIndex")]
    public int RowIndex { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("width")]
    public double Width { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("x")]
    public double X { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("y")]
    public double Y { get; set; }
}

/// <summary>
/// 
/// </summary>
public class SegmentationDetail
{
    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("area")]
    public double Area { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("bbox")]
    public List<double> Bbox { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("class")]
    public string Class { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("confidence")]
    public double Confidence { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("point_coords")]
    public List<List<double>> PointCoords { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("points")]
    public List<List<List<double>>> Points { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("predicted_iou")]
    public double PredictedIou { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("rowIndex")]
    public int RowIndex { get; set; }
}