using System.Collections.Generic;
using Newtonsoft.Json;

namespace aibase.Models.Dto;

/// <summary>
/// 
/// </summary>
public class SegmentDetailInputDto
{
    /// <summary>
    /// 
    /// <summary>
    /// 
    /// </summary>/// </summary>
    [JsonProperty("boxId")]
    public string BoxId { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("class")]
    public string Class { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("x")]
    public double X { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("y")]
    public double Y { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("width")]
    public double Width { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("height")]
    public double Height { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("confidence")]
    public double Confidence { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("detection_id")]
    public string DetectionId { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("points")]
    public List<List<double>> Points { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    [JsonProperty("rowIndex")]
    public int RowIndex { get; set; }
}