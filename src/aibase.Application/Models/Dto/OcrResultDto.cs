﻿using aibase.Images.Dto;
using System.Collections.Generic;

namespace aibase.Models.Dto;

/// <summary>
/// 
/// </summary>
public class OcrResultDto
{
    /// <summary>
    /// 
    /// </summary>
    public int? ImageId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string Text { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public double Probability { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public List<CoordinateDto> Coordinate { get; set; } = [];

    /// <summary>
    /// 
    /// </summary>
    public CoordinateV2Dto? CoordinateV2 { get; set; }    }