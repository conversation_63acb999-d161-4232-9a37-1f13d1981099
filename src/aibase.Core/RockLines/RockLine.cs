using System;
using System.ComponentModel.DataAnnotations;
using Abp.Domain.Entities;
using Abp.Domain.Entities.Auditing;
using aibase.ImageCrops;

namespace aibase.RockLines;

public class RockLine : Entity<int>, IHasCreationTime, IHasModificationTime
{
    [Required]
    public RockLineType Type { get; set; }
    
    [Required]
    public double DepthFrom { get; set; }
    
    [Required]
    public double DepthTo { get; set; }
    
    [Required]
    public double StartX { get; set; }
    
    [Required]
    public double StartY { get; set; }
    
    [Required]
    public double EndX { get; set; }
    
    [Required]
    public double EndY { get; set; }
    
    [Required]
    public int RowIndex { get; set; }
    
    public double Length { get; set; }
    
    [Required]
    public int ImageCropId { get; set; }
    public virtual ImageCrop ImageCrop { get; set; }
    
    public DateTime CreationTime { get; set; }
    public DateTime? LastModificationTime { get; set; }
}

public enum RockLineType
{
    Recovery = 1,
    Rqd
}