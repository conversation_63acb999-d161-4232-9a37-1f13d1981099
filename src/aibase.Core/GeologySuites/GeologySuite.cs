﻿using Abp.Domain.Entities.Auditing;
using Abp.Domain.Entities;
using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;
using aibase.GeologyProjectSuites;
using aibase.GeologySuiteFields;
using aibase.RockTree;

namespace aibase.GeologySuites
{
    public class GeologySuite : Entity<int>, IHasCreationTime, IHasModificationTime
    {
        [Required] 
        public string Name { get; set; } = string.Empty;

        [Required] 
        public bool IsActive { get; set; }
        
        [Required]
        public bool IsGeotech { get; set; }
        
        [Required] 
        public int TenantId { get; set; }

        public DateTime CreationTime { get; set; }
        public DateTime? LastModificationTime { get; set; }

        public ICollection<GeologyProjectSuite> GeologyProjectSuites { get; set; } = [];
        public ICollection<GeologySuiteField> GeologySuiteFields { get; set; } = [];
        public virtual RockTreeRoot? RockTreeRoot { get; set; }
    }
}