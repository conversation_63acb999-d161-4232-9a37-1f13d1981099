using System;
using Abp.Domain.Entities;
using aibase.DrillHoleEntity;
using System.ComponentModel.DataAnnotations;
using Abp.Domain.Entities.Auditing;

namespace aibase.RqdCalculations
{
    public class RqdCalculationResult : Entity<int>, IMustHaveTenant, IHasCreationTime, IHasModificationTime
    {
        [Required]
        public int DrillHoleId { get; set; }
        public virtual DrillHole DrillHole { get; set; }

        [Required]
        public int RqdCalculationId { get; set; }
        public virtual RqdCalculation RqdCalculation { get; set; }

        [Required]
        public double FromDepth { get; set; }

        [Required]
        public double ToDepth { get; set; }

        [Required]
        public double Total { get; set; }
        
        [Required]
        public int TenantId { get; set; }

        public DateTime CreationTime { get; set; }
        public DateTime? LastModificationTime { get; set; }
    }
}