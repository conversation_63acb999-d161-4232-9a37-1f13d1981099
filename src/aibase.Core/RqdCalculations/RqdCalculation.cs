using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Abp.Domain.Entities;
using Abp.Domain.Entities.Auditing;
using aibase.Structures;
using aibase.StructureTypes;

namespace aibase.RqdCalculations
{
    public class RqdCalculation : Entity<int>, IMustHaveTenant, IHasCreationTime, IHasModificationTime
    {
        [Required]
        public string Name { get; set; }

        [Required]
        public bool IsActive { get; set; }

        [Required]
        public CalculationType CalculationType { get; set; }

        [Required]
        public CountingMethod CountingMethod { get; set; }

        public double? Interval { get; set; }

        public int? StructureTypeId { get; set; }
        public virtual StructureType StructureType { get; set; }
        
        public int? StructureId { get; set; }
        public virtual Structure Structure { get; set; }

        public decimal? MinimumWidth { get; set; }

        public int? MinimumSegmentLength { get; set; }
        
        public bool? IsDisplay { get; set; }

        [Required]
        public int TenantId { get; set; }
        
        public DateTime CreationTime { get; set; }
        public DateTime? LastModificationTime { get; set; }
        
        public ICollection<RqdCalculationProject> RqdCalculationProjects { get; set; } = [];
    }

    public enum CalculationType
    {
        Block = 1,
        Distance
    }

    public enum CountingMethod
    {
        Structures = 1,
        RockSegments
    }
}