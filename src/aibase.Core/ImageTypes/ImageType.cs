using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Abp.Domain.Entities;
using Abp.Domain.Entities.Auditing;
using aibase.ImageSubtypes;
using aibase.ProjectImageTypes;

namespace aibase.ImageTypes;

public class ImageType : Entity<int>, IHasCreationTime, IHasModificationTime
{
    [Required]
    public string Name { get; set; } = string.Empty;

    public bool IsActive { get; set; } = true;

    public bool IsStandard { get; set; } = false;

    public bool IsRigCorrected { get; set; } = false;
    
    public bool IsRig { get; set; } = false;
    
    public int Sequence { get; set; }
    
    public int? Priority { get; set; }

    [Required]
    public int TenantId { get; set; }
    
    public DateTime CreationTime { get; set; }
    public DateTime? LastModificationTime { get; set; }

    public ICollection<ImageSubtype> ImageSubtypes { get; set; } = [];
    public ICollection<ProjectImageType> ProjectImageTypes { get; set; } = [];
}