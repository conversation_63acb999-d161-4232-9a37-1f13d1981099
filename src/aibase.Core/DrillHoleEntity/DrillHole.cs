﻿using Abp.Domain.Entities.Auditing;
using Abp.Domain.Entities;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using aibase.DesurveyResults;
using aibase.ProjectEntity;
using aibase.Prospects;

namespace aibase.DrillHoleEntity
{
    public class DrillHole : Entity<int>, IHasCreationTime, IHasModificationTime, IMustHaveTenant
    {
        [Required]
        public string Name { get; set; } = string.Empty;

        public DrillHoleStatus? DrillHoleStatus { get; set; }

        public decimal? Elevation { get; set; }
        
        public decimal? Northing { get; set; }
        
        public decimal? Easting { get; set; }
        
        public decimal? Longitude { get; set; }
        
        public decimal? Latitude { get; set; }
        
        public decimal? Dip { get; set; }
        
        public decimal? Azimuth { get; set; }

        [Required]
        public double MaxDepth { get; set; }

        [Required]
        public int ProjectId { get; set; }
        public virtual Project Project { get; set; }        
        
        [Required]
        public int ProspectId { get; set; }
        public virtual Prospect Prospect { get; set; }
        
        [Required]
        public bool IsExport { get; set; }
        public int OriginalImages { get; set; }
        public int CroppedRows { get; set; }
        
        public bool IsActive { get; set; }

        [Required]
        public int TenantId { get; set; }
        
        public ICollection<DesurveyResult> DesurveyResults { get; set; } = [];

        public DateTime CreationTime { get; set; }
        public DateTime? LastModificationTime { get; set; }
    }

    public enum DrillHoleStatus
    {
        NotStarted = 1,
        InProgress,
        Reprocess,
        Review,
        Complete,
        Exported,
        Unnamed
    }
}
