﻿using Abp.Domain.Entities;
using Abp.Domain.Entities.Auditing;
using aibase.ProjectSuites;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using aibase.AssayProjectSuites;
using aibase.GeologyProjectSuites;
using aibase.MobileProfiles;
using aibase.ProjectGeotechSuites;
using aibase.ProjectImageTypes;
using aibase.ProjectLoggingViews;
using aibase.RockGroups;
using aibase.RqdCalculations;
using aibase.WorkflowEntity;

namespace aibase.ProjectEntity
{
    public class Project : Entity<int>, IHasCreationTime, IHasModificationTime, IMustHaveTenant
    {
        [Required]
        public string Name { get; set; } = string.Empty;

        [Required]
        [MaxLength(3)]
        public string Code { get; set; } = string.Empty;

        [Required] 
        public double CoreTrayLength { get; set; } = 1;

        [Required]
        public string Description { get; set; } = string.Empty;

        [Required]
        public string BackgroundColor { get; set; } = string.Empty;

        [Required]
        public string TextColor { get; set; } = string.Empty;

        [Required] 
        public string LoggingTextColor { get; set; } = "#FFFFFF";

        [Required]
        public string BoundingBoxIds { get; set; } = string.Empty;

        [Required]
        public string BoundingRowsIds { get; set; } = string.Empty;

        [Required]
        public int TenantId { get; set; }

        [Required] 
        public bool IsActive { get; set; } = true;
        
        public int? WorkflowId { get; set; }
        public virtual Workflow Workflow { get; set; }
        
        public int? RockGroupId { get; set; }
        public virtual RockGroup RockGroup { get; set; }
        
        public int? MobileProfileId { get; set; }
        public virtual MobileProfile? MobileProfile { get; set; }
        
        public DateTime CreationTime { get; set; }
        public DateTime? LastModificationTime { get; set; }

        public ICollection<ProjectSuite> ProjectSuites { get; set; } = [];
        public ICollection<GeologyProjectSuite> GeologyProjectSuites { get; set; } = [];
        public ICollection<ProjectLoggingView> ProjectLoggingViews { get; set; } = [];
        public ICollection<ProjectGeotechSuite> ProjectGeotechSuites {get; set; } = [];
        public ICollection<RqdCalculationProject> RqdCalculationProjects { get; set; } = [];
        public ICollection<AssayProjectSuite> AssayProjectSuites { get; set; } = [];
        public ICollection<ProjectImageType> ProjectImageTypes { get; set; } = [];
    }
}
