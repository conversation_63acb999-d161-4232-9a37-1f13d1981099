using System;
using System.ComponentModel.DataAnnotations;
using Abp.Domain.Entities;
using Abp.Domain.Entities.Auditing;
using aibase.ImageTypes;

namespace aibase.ImageSubtypes;

public class ImageSubtype : Entity<int>, IHasCreationTime, IHasModificationTime
{
    [Required]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    public int ImageTypeId { get; set; }
    public virtual ImageType ImageType { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    public bool IsWet { get; set; } = false;
    
    public bool IsDry { get; set; } = false;
    
    public bool IsUv { get; set; } = false;
    
    public int Sequence { get; set; }

    [Required]
    public int TenantId { get; set; }
    
    public DateTime CreationTime { get; set; }
    public DateTime? LastModificationTime { get; set; }
}