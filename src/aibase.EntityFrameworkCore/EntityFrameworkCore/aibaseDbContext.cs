﻿using Microsoft.EntityFrameworkCore;
using Abp.Zero.EntityFrameworkCore;
using aibase.Authorization.Roles;
using aibase.Authorization.Users;
using aibase.MultiTenancy;
using aibase.ProjectEntity;
using aibase.DrillHoleEntity;
using aibase.ImageEntity;
using aibase.FileEntity;
using aibase.Settings;
using aibase.Polygons;
using aibase.WorkflowEntity;
using aibase.StepWorkflowEntity;
using aibase.SourceTypeWorkflowEntity;
using aibase.AIServiceEntity;
using aibase.APIKeys;
using aibase.AssayAttributes;
using aibase.AssayDatas;
using aibase.AssayProjectSuites;
using aibase.AssaySuiteAttributes;
using aibase.AssaySuiteFields;
using aibase.AssaySuites;
using aibase.AssayTranslations;
using aibase.ImageCrops;
using aibase.ResultSteps;
using aibase.ImageOcrs;
using aibase.ImageSegments;
using aibase.SuiteAttributes;
using aibase.Suites;
using aibase.ProjectSuites;
using aibase.Attributes;
using aibase.Colours;
using aibase.ColourValues;
using aibase.DataEntries;
using aibase.DownholeDatas;
using aibase.ExportEvents;
using aibase.ExportTemplates;
using aibase.GeologyDatas;
using aibase.GeologyDates;
using aibase.GeologyDateValues;
using aibase.GeologyDescriptions;
using aibase.GeologyDescriptionValues;
using aibase.GeologyProjectSuites;
using aibase.GeologySuiteFields;
using aibase.GeologySuites;
using aibase.Themes;
using aibase.WorkflowJobs;
using aibase.Prospects;
using aibase.RockStyles;
using aibase.RockTypes;
using aibase.PickListItems;
using aibase.PickLists;
using aibase.Units;
using aibase.Numbers;
using aibase.NumberValues;
using aibase.PickListValues;
using aibase.RockGroupProjects;
using aibase.RockGroupRockTypes;
using aibase.RockGroups;
using aibase.RockGroupValues;
using aibase.RockSelectNumbers;
using aibase.RockSelectNumberValues;
using aibase.RockTypeNumbers;
using aibase.RockTypeNumberValues;
using aibase.CoordinatePolygons;
using aibase.DesurveyResults;
using aibase.DownholeSurveys;
using aibase.ExportTemplateDrillHoles;
using aibase.ExportTemplateImageSubtypes;
using aibase.ExportTemplateImageTypes;
using aibase.GeologyFields;
using aibase.GeotechDatas;
using aibase.GeotechSuites;
using aibase.GeotechSuiteStructures;
using aibase.ImageSubtypes;
using aibase.ImportMappingTemplates;
using aibase.LoggingBars;
using aibase.LoggingViewColumns;
using aibase.LoggingViews;
using aibase.MobileProfiles;
using aibase.NumberRanges;
using aibase.ProjectGeotechSuites;
using aibase.ProjectImageTypes;
using aibase.ProjectLoggingViews;
using aibase.RecoveryResults;
using aibase.RockLines;
using aibase.StructureConditions;
using aibase.Structures;
using aibase.StructureTypes;
using aibase.UserProjects;
using aibase.UserRoleConfigs;
using aibase.WorkRoleGeotechSuites;
using aibase.WorkRoles;
using aibase.WorkRoleSuites;
using aibase.RqdCalculations;
using aibase.TrayDepthResults;
using aibase.RockNodeValues;
using aibase.RockTree;
using aibase.RqdPercentResults;
using ImageType = aibase.ImageTypes.ImageType;

namespace aibase.EntityFrameworkCore
{
    public class aibaseDbContext : AbpZeroDbContext<Tenant, Role, User, aibaseDbContext>
    {
        /* Define a DbSet for each entity of the application */
        public DbSet<Project> AbpProjects { get; set; }
        public DbSet<DrillHole> AbpDrillHoles { get; set; }
        public DbSet<Image> AbpImages { get; set; }
        public DbSet<File> AbpFiles { get; set; }
        public DbSet<Setting> AbpSettingAccounts { get; set; }
        public DbSet<Polygon> AbpPolygons { get; set; }
        public DbSet<Workflow> AbpWorkflows { get; set; }
        public DbSet<StepWorkflow> AbpStepWorkflows { get; set; }
        public DbSet<SourceTypeWorkflow> AbpSourceTypeWorkflow { get; set; }
        public DbSet<AIService> AbpAIServices { get; set; }
        public DbSet<ImageCrop> AbpImageCrops { get; set; }
        public DbSet<ResultStep> AbpResultSteps { get; set; }
        public DbSet<ImageOcr> AbpImageOcrs { get; set; }
        public DbSet<ImageSegment> AbpImageSegments { get; set; }
        public DbSet<Attribute> AbpAttributes { get; set; }
        public DbSet<Suite> AbpSuites { get; set; }
        public DbSet<SuiteAttribute> AbpSuiteAttributes { get; set; }
        public DbSet<ProjectSuite> AbpProjectSuites { get; set; }
        public DbSet<DownholeData> AbpDownholeDatas { get; set; }
        public DbSet<Theme> AbpThemes { get; set; }
        public DbSet<WorkflowJob> AbpWorkflowJobs { get; set; }
        public DbSet<Prospect> AbpProspects { get; set; }
        public DbSet<ExportTemplate> AbpExportTemplates { get; set; }
        public DbSet<ExportEvent> AbpExportEvents { get; set; }
        public DbSet<GeologySuite> AbpGeologySuites { get; set; }
        public DbSet<GeologyProjectSuite> AbpGeologyProjectSuites { get; set; }
        public DbSet<RockStyle> AbpRockStyles { get; set; }
        public DbSet<RockType> AbpRockTypes { get; set; }
        public DbSet<GeologyData> AbpGeologyData { get; set; }
        public DbSet<AssayAttribute> AbpAssayAttributes { get; set; }
        public DbSet<AssaySuite> AbpAssaySuites { get; set; }
        public DbSet<AssaySuiteAttribute> AbpAssaySuiteAttributes { get; set; }
        public DbSet<AssayProjectSuite> AbpAssayProjectSuites { get; set; }
        public DbSet<AssayData> AbpAssayData { get; set; }
        public DbSet<Unit> AbpUnits { get; set; }
        public DbSet<Colour> AbpColours { get; set; }
        public DbSet<Number> AbpNumbers { get; set; }
        public DbSet<PickList> AbpPickLists { get; set; }
        public DbSet<RefreshToken> AbpRefreshTokens { get; set; }
        public DbSet<PickListItem> AbpPickListItems { get; set; }
        public DbSet<RockGroup> AbpRockGroups { get; set; }
        public DbSet<RockGroupProject> AbpRockGroupProjects { get; set; }
        public DbSet<RockGroupRockType> AbpRockGroupRockTypes { get; set; }
        public DbSet<GeologySuiteField> AbpGeologySuiteFields { get; set; }
        public DbSet<GeologyDescription> AbpGeologyDescriptions { get; set; }
        public DbSet<GeologyDate> AbpGeologyDates { get; set; }
        public DbSet<RockSelectNumber> AbpRockSelectNumbers { get; set; }
        public DbSet<RockTypeNumber> AbpRockTypeNumbers { get; set; }
        public DbSet<DataEntry> AbpDataEntries { get; set; }
        public DbSet<ColourValue> AbpColourValues { get; set; }
        public DbSet<NumberValue> AbpNumberValues { get; set; }
        public DbSet<RockGroupValue> AbpRockGroupValues { get; set; }
        public DbSet<PickListValue> AbpPickListValues { get; set; }
        public DbSet<GeologyDescriptionValue> AbpGeologyDescriptionValues { get; set; }
        public DbSet<RockTypeNumberValue> AbpRockTypeNumberValues { get; set; }
        public DbSet<RockSelectNumberValue> AbpRockSelectNumberValues { get; set; }
        public DbSet<GeologyDateValue> AbpGeologyDateValues { get; set; }
        public DbSet<CoordinatePolygon> AbpCoordinatePolygons { get; set; }
        public DbSet<GeologyField> AbpGeologyFields { get; set; }
        public DbSet<UserRoleConfig> AbpUserRoleConfigs { get; set; }
        public DbSet<UserProject> AbpUserProjects { get; set; }
        public DbSet<APIKey> AbpAPIKeys { get; set; }
        public DbSet<WorkRole> AbpWorkRoles { get; set; }
        public DbSet<WorkRoleSuite> AbpWorkRoleSuites { get; set; }
        public DbSet<APIKeyRole> AbpAPIKeyRoles { get; set; }
        public DbSet<LoggingView> AbpLoggingViews { get; set; }
        public DbSet<LoggingViewColumn> AbpLoggingViewColumns { get; set; }
        public DbSet<ProjectLoggingView> AbpProjectLoggingViews { get; set; }
        public DbSet<APIKeyTypes> AbpAPIKeyTypes { get; set; }
        public DbSet<RecoveryResult> AbpRecoveryResults { get; set; }
        public DbSet<StructureType> AbpStructureTypes { get; set; }
        public DbSet<Structure> AbpStructures { get; set; }
        public DbSet<GeotechSuite> AbpGeotechSuites { get; set; }
        public DbSet<GeotechSuiteStructure> AbpGeotechSuiteStructures { get; set; }
        public DbSet<ProjectGeotechSuite> AbpProjectGeotechSuites { get; set; }
        public DbSet<WorkRoleGeotechSuite> AbpWorkRoleGeotechSuites { get; set; }
        public DbSet<NumberRange> AbpNumberRanges { get; set; }
        public DbSet<NumberRangeInterval> AbpNumberRangeIntervals { get; set; }
        public DbSet<StructureCondition> AbpStructureConditions { get; set; }
        public DbSet<GeotechData> AbpGeotechData { get; set; }
        public DbSet<RqdCalculation> AbpRqdCalculations { get; set; }
        public DbSet<RqdCalculationProject> AbpRqdCalculationProjects { get; set; }
        public DbSet<RqdCalculationResult> AbpRqdCalculationResults { get; set; }
        public DbSet<WorkflowJobError> AbpWorkflowJobErrors { get; set; }
        public DbSet<RockLine> AbpRockLines { get; set; }
        public DbSet<TrayDepthResult> AbpTrayDepthResults { get; set; }
        public DbSet<ImportMappingTemplate> AbpImportMappingTemplates { get; set; }
        public DbSet<ImportMappingTemplateField> AbpImportMappingTemplateFields { get; set; }
        public DbSet<DownholeSurvey> AbpDownholeSurveys { get; set; }
        public DbSet<DownholeSurveyType> AbpDownholeSurveyTypes { get; set; }
        public DbSet<AssayTranslation> AbpAssayTranslations { get; set; }
        public DbSet<AssaySuiteField> AbpAssaySuiteFields { get; set; }
        public DbSet<ImageType> AbpImageTypes { get; set; }
        public DbSet<ImageSubtype> AbpImageSubtypes { get; set; }
        public DbSet<ExportTemplateImageType> AbpExportTemplateImageTypes { get; set; }
        public DbSet<ExportTemplateImageSubtype> AbpExportTemplateImageSubtypes { get; set; }
        public DbSet<ExportTemplateDrillHole> AbpExportTemplateDrillHoles { get; set; }
        public DbSet<DesurveyResult> AbpDesurveyResults { get; set; }
        public DbSet<ProjectImageType> AbpProjectImageTypes { get; set; }
        public DbSet<LoggingBar> AbpLoggingBars { get; set; }
        public DbSet<RqdPercentResult> AbpRqdPercentResults { get; set; }
        public DbSet<MobileProfile> AbpMobileProfiles { get; set; }
        
        // Rock Tree Entities
        public DbSet<RockNode> AbpRockNodes { get; set; }
        public DbSet<RockNodeRelation> AbpRockNodeRelations { get; set; }
        public DbSet<RockTreeRoot> AbpRockTreeRoots { get; set; }
        public DbSet<RockTreeRootNode> AbpRockTreeRootNodes { get; set; }
        public virtual DbSet<RockNodeValue> AbpRockNodeValues { get; set; }

        /// <inheritdoc />
        public aibaseDbContext(DbContextOptions<aibaseDbContext> options)
            : base(options)
        {
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // RockTree Configurations
            modelBuilder.Entity<RockNode>(b =>
            {
                b.ToTable("RockNodes");
                b.HasOne(rn => rn.RockType)
                    .WithMany() // Assuming RockType doesn't need a direct navigation back to RockNodes for this specific relationship
                    .HasForeignKey(rn => rn.RockTypeId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            modelBuilder.Entity<RockNodeRelation>(b =>
            {
                b.ToTable("RockNodeRelations");
                b.HasOne(rnr => rnr.ParentNode)
                    .WithMany(rn => rn.ChildRelations)
                    .HasForeignKey(rnr => rnr.ParentNodeId)
                    .OnDelete(DeleteBehavior.Restrict);

                b.HasOne(rnr => rnr.ChildNode)
                    .WithMany(rn => rn.ParentRelations)
                    .HasForeignKey(rnr => rnr.ChildNodeId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            modelBuilder.Entity<RockTreeRoot>(b =>
            {
                b.ToTable("RockTreeRoots");
            });

            modelBuilder.Entity<RockTreeRootNode>(b =>
            {
                b.ToTable("RockTreeRootNodes");
                b.HasOne(rtrn => rtrn.RockTreeRoot)
                    .WithMany(rtr => rtr.RockTreeRootNodes)
                    .HasForeignKey(rtrn => rtrn.RockTreeRootId)
                    .OnDelete(DeleteBehavior.Cascade);

                b.HasOne(rtrn => rtrn.RockNode)
                    .WithMany(rn => rn.RockTreeRootNodes)
                    .HasForeignKey(rtrn => rtrn.RockNodeId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
        }
    }
}
